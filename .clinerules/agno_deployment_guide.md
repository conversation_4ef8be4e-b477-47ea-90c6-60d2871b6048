# Agno Framework 完整部署指南

基於 Agno Framework 的 Dy-Flow v3 DeFi 自動化交易系統部署指南

## 📋 目錄

1. [系統概述](#系統概述)
2. [環境準備](#環境準備)
3. [安裝步驟](#安裝步驟)
4. [配置設置](#配置設置)
5. [組件部署](#組件部署)
6. [測試驗證](#測試驗證)
7. [監控運維](#監控運維)
8. [故障排除](#故障排除)

## 🎯 系統概述

### **核心架構**

```
┌─────────────────────────────────────────────────────────────┐
│                    Dy-Flow v3 + Agno Framework             │
├─────────────────────────────────────────────────────────────┤
│  🎨 Agno Playground (Web UI)                               │
│     ├── DeFi Pool Analyzer                                 │
│     ├── Risk Advisor                                       │
│     ├── Strategy Planner                                   │
│     ├── Wallet Assistant                                   │
│     └── Market Analyst                                     │
├─────────────────────────────────────────────────────────────┤
│  🤖 Agno Enhanced Agents                                   │
│     ├── Scorer V2 Agno (6因子動態評分)                     │
│     ├── Risk Sentinel Agno (AI風險監控)                    │
│     └── Planner Agno (AI策略規劃)                          │
├─────────────────────────────────────────────────────────────┤
│  🔄 Agno DAG 調度系統                                      │
│     ├── 主要數據流水線 (5分鐘)                              │
│     ├── 深度分析流水線 (30分鐘)                             │
│     └── 緊急響應流水線 (事件觸發)                           │
├─────────────────────────────────────────────────────────────┤
│  🧠 AI 能力層                                              │
│     ├── OpenAI GPT-4o (主要推理)                           │
│     ├── Anthropic Claude 3.7 (策略分析)                    │
│     ├── ReasoningTools (複雜決策)                           │
│     └── DuckDuckGo (市場研究)                               │
├─────────────────────────────────────────────────────────────┤
│  💾 存儲和記憶層                                            │
│     ├── SQLite (Agno 會話存儲)                             │
│     ├── DeFi 知識庫                                        │
│     └── 策略學習歷史                                        │
└─────────────────────────────────────────────────────────────┘
```

### **核心特性**

- ✅ **雙重架構保障**: Agno 增強 + 傳統降級
- ✅ **AI 推理增強**: 複雜決策場景的智能處理
- ✅ **結構化輸出**: Pydantic 模型確保數據一致性
- ✅ **多模型支持**: OpenAI + Anthropic 混合使用
- ✅ **實時監控**: Web UI + 自動化告警
- ✅ **企業級特性**: 容錯、擴展、安全

## 🔧 環境準備

### **系統要求**

```bash
# 操作系統
Ubuntu 20.04+ / macOS 11+ / Windows 10+

# Python 環境
Python 3.9+

# 硬件要求
- CPU: 4核心+
- 內存: 8GB+
- 存儲: 20GB+
- 網絡: 穩定互聯網連接

# API 要求
- OpenAI API Key (必須)
- Anthropic API Key (推薦)
- BSCScan API Key (可選)
- 其他 DeFi API Keys (可選)
```

### **依賴檢查**

```bash
# 檢查 Python 版本
python --version  # 需要 3.9+

# 檢查 pip
pip --version

# 檢查 git
git --version

# 檢查網絡連接
curl -I https://api.openai.com/v1/models
```

## 🚀 安裝步驟

### **1. 克隆項目**

```bash
# 克隆 Dy-Flow v3 項目
git clone <repository-url> dyflow-v3
cd dyflow-v3

# 檢查 Agno 增強組件
ls -la src/agents/*agno.py
ls -la tests/*agno*.py
ls -la config/*agno*.yaml
```

### **2. 創建虛擬環境**

```bash
# 創建虛擬環境
python -m venv venv

# 激活虛擬環境
# Linux/macOS
source venv/bin/activate

# Windows
venv\Scripts\activate
```

### **3. 安裝依賴**

```bash
# 安裝基礎依賴
pip install -r requirements.txt

# 安裝 Agno Framework 完整包
pip install agno openai anthropic duckduckgo-search

# 安裝可選增強包
pip install yfinance exa-py newspaper4k lxml_html_clean

# 安裝數據庫支持
pip install sqlalchemy psycopg2-binary pgvector

# 驗證安裝
python -c "import agno; print('Agno Framework installed successfully')"
```

### **4. 創建目錄結構**

```bash
# 創建必要目錄
mkdir -p data/agno_memory
mkdir -p data/backups
mkdir -p data/cache
mkdir -p data/logs
mkdir -p data/state
mkdir -p tests/reports
mkdir -p logs

# 設置權限
chmod 755 data/
chmod 755 logs/
```

## ⚙️ 配置設置

### **1. 環境變量配置**

```bash
# 創建環境變量文件
cat > .env << 'EOF'
# OpenAI 配置 (必須)
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic 配置 (推薦)
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# 可選 API Keys
DUCKDUCKGO_API_KEY=your-duckduckgo-key  # 可選
EXA_API_KEY=your-exa-key                # 可選
BSCSCAN_API_KEY=your-bscscan-key        # 可選

# 數據庫配置
DATABASE_URL=sqlite:///data/dyflow.db

# Redis 配置 (可選)
REDIS_URL=redis://localhost:6379/0

# 監控配置
WEBHOOK_URL=your-webhook-url            # 可選
SLACK_WEBHOOK=your-slack-webhook        # 可選

# 調試模式
DEBUG=false
LOG_LEVEL=INFO
EOF

# 加載環境變量
source .env
```

### **2. 核心配置文件**

```bash
# 檢查主要配置文件
ls -la config/

# 主要配置文件說明
- config/default.yaml           # 系統默認配置
- config/agno_dag_complete.yaml # Agno 完整 DAG 配置
- config/scorer_v2_agno.yaml    # Scorer V2 Agno 配置
- config/networks.yaml          # 網絡配置
- config/pools.yaml             # 池子配置
- config/strategies.yaml        # 策略配置
```

### **3. 檢查配置有效性**

```bash
# 測試配置加載
python -c "
from src.utils.config import load_config
config = load_config()
print('配置加載成功')
print(f'Agents 數量: {len(config.get(\"agents\", {}))}')
"
```

## 🧩 組件部署

### **1. 部署 Agno 增強 Agents**

```bash
# 1.1 測試 Scorer V2 Agno Agent
echo "🧪 測試 Scorer V2 Agno Agent..."
python tests/scorer_v2_agno_test.py

# 1.2 測試 Risk Sentinel Agno Agent  
echo "🧪 測試 Risk Sentinel Agno Agent..."
python tests/risk_sentinel_agno_test.py

# 1.3 測試 Planner Agno Agent
echo "🧪 測試 Planner Agno Agent..."
python tests/planner_agno_test.py
```

### **2. 部署 Agno Playground**

```bash
# 2.1 啟動 Playground (開發模式)
echo "🎨 啟動 Agno Playground..."
python src/ui/agno_playground.py --host 0.0.0.0 --port 7777

# 2.2 後台運行 Playground
nohup python src/ui/agno_playground.py --host 0.0.0.0 --port 7777 --no-reload > logs/playground.log 2>&1 &

# 2.3 檢查 Playground 狀態
curl -I http://localhost:7777
```

### **3. 部署 DAG 調度系統**

```bash
# 3.1 測試 DAG 配置
echo "🔄 驗證 DAG 配置..."
python -c "
import yaml
with open('config/agno_dag_complete.yaml', 'r') as f:
    config = yaml.safe_load(f)
print(f'DAG 配置有效: {config[\"name\"]}')
print(f'Agents 數量: {len(config[\"agents\"])}')
print(f'工作流數量: {len(config[\"workflows\"])}')
"

# 3.2 啟動 DAG 調度器 (如果實現)
# python src/core/agno_scheduler.py --config config/agno_dag_complete.yaml
```

### **4. 部署監控和告警**

```bash
# 4.1 設置日誌目錄
mkdir -p logs/agents logs/system logs/errors

# 4.2 配置日誌輪轉
cat > /etc/logrotate.d/dyflow << 'EOF'
/path/to/dyflow/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    sharedscripts
}
EOF

# 4.3 設置系統服務 (systemd)
cat > /etc/systemd/system/dyflow-agno.service << 'EOF'
[Unit]
Description=Dy-Flow Agno Framework Service
After=network.target

[Service]
Type=simple
User=dyflow
WorkingDirectory=/path/to/dyflow
Environment=PATH=/path/to/dyflow/venv/bin
ExecStart=/path/to/dyflow/venv/bin/python src/ui/agno_playground.py --host 0.0.0.0 --port 7777 --no-reload
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 啟用服務
sudo systemctl enable dyflow-agno
sudo systemctl start dyflow-agno
```

## 🧪 測試驗證

### **1. 運行完整系統測試**

```bash
# 運行完整系統測試
echo "🚀 運行 Agno 完整系統測試..."
python tests/agno_complete_system_test.py

# 檢查測試報告
ls -la tests/reports/agno_complete_system_test_*.json
```

### **2. 功能驗證測試**

```bash
# 2.1 驗證 API 連接
echo "🔗 測試 API 連接..."
python -c "
import openai
try:
    client = openai.OpenAI()
    models = client.models.list()
    print('✅ OpenAI API 連接成功')
except Exception as e:
    print(f'❌ OpenAI API 連接失敗: {e}')
"

# 2.2 驗證 Anthropic API
python -c "
try:
    import anthropic
    client = anthropic.Anthropic()
    print('✅ Anthropic API 配置成功')
except Exception as e:
    print(f'❌ Anthropic API 配置失敗: {e}')
"

# 2.3 驗證數據庫連接
python -c "
from src.utils.database import Database
try:
    db = Database()
    print('✅ 數據庫連接成功')
except Exception as e:
    print(f'❌ 數據庫連接失敗: {e}')
"
```

### **3. 性能基準測試**

```bash
# 3.1 測試 Agent 響應時間
echo "⏱️ 測試 Agent 性能..."
python -c "
import asyncio
import time
from src.agents.scorer_v2_agno import ScorerV2AgnoAgent
from src.utils.config import load_config

async def test_performance():
    config = load_config()
    agent = ScorerV2AgnoAgent('test', config, None)
    await agent.initialize()
    
    start = time.time()
    insights = await agent.get_scoring_insights()
    end = time.time()
    
    print(f'Scorer V2 響應時間: {end - start:.2f}s')

asyncio.run(test_performance())
"

# 3.2 測試內存使用
python -c "
import psutil
import os
process = psutil.Process(os.getpid())
memory_mb = process.memory_info().rss / 1024 / 1024
print(f'當前內存使用: {memory_mb:.1f} MB')
"
```

## 📊 監控運維

### **1. 系統健康監控**

```bash
# 1.1 創建健康檢查腳本
cat > scripts/health_check.sh << 'EOF'
#!/bin/bash

# 檢查服務狀態
check_service() {
    if systemctl is-active --quiet dyflow-agno; then
        echo "✅ Dy-Flow Agno 服務運行正常"
    else
        echo "❌ Dy-Flow Agno 服務異常"
        return 1
    fi
}

# 檢查端口
check_port() {
    if curl -s http://localhost:7777 > /dev/null; then
        echo "✅ Playground 端口 7777 響應正常"
    else
        echo "❌ Playground 端口 7777 無響應"
        return 1
    fi
}

# 檢查日誌錯誤
check_logs() {
    error_count=$(tail -100 logs/playground.log | grep -i error | wc -l)
    if [ $error_count -lt 5 ]; then
        echo "✅ 日誌錯誤數量正常 ($error_count)"
    else
        echo "⚠️ 日誌錯誤數量偏高 ($error_count)"
    fi
}

# 執行所有檢查
check_service && check_port && check_logs
EOF

chmod +x scripts/health_check.sh
```

### **2. 設置定時監控**

```bash
# 2.1 添加 crontab 任務
crontab -e

# 添加以下行 (每5分鐘檢查一次)
# */5 * * * * /path/to/dyflow/scripts/health_check.sh >> /path/to/dyflow/logs/health_check.log 2>&1

# 2.2 設置日誌清理 (每天清理)
# 0 2 * * * find /path/to/dyflow/logs -name "*.log" -mtime +7 -delete
```

### **3. 性能監控**

```bash
# 3.1 創建性能監控腳本
cat > scripts/performance_monitor.sh << 'EOF'
#!/bin/bash

# 獲取系統資源使用情況
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
memory_usage=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')

echo "$(date): CPU: ${cpu_usage}%, Memory: ${memory_usage}%, Disk: ${disk_usage}%"

# 檢查 Dy-Flow 進程
process_count=$(ps aux | grep -v grep | grep agno_playground | wc -l)
echo "$(date): Dy-Flow 進程數: $process_count"

# 記錄到監控日誌
echo "$(date): CPU: ${cpu_usage}%, Memory: ${memory_usage}%, Disk: ${disk_usage}%, Processes: $process_count" >> logs/performance.log
EOF

chmod +x scripts/performance_monitor.sh
```

### **4. 告警設置**

```bash
# 4.1 創建告警腳本
cat > scripts/alert.sh << 'EOF'
#!/bin/bash

send_alert() {
    local message="$1"
    local severity="$2"
    
    # 記錄到日誌
    echo "$(date): [$severity] $message" >> logs/alerts.log
    
    # 發送到 Webhook (如果配置)
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
             -H "Content-Type: application/json" \
             -d "{\"text\":\"[$severity] Dy-Flow Alert: $message\"}"
    fi
    
    # 發送郵件 (如果配置)
    if [ -n "$ALERT_EMAIL" ]; then
        echo "$message" | mail -s "[$severity] Dy-Flow Alert" "$ALERT_EMAIL"
    fi
}

# 使用範例
# send_alert "Agno Playground 服務異常" "CRITICAL"
EOF

chmod +x scripts/alert.sh
```

## 🔧 故障排除

### **1. 常見問題**

#### **問題 1: Agno Framework 導入失敗**

```bash
# 症狀
ImportError: No module named 'agno'

# 解決方案
pip install agno openai anthropic duckduckgo-search

# 驗證
python -c "import agno; print('Agno 安裝成功')"
```

#### **問題 2: OpenAI API 連接失敗**

```bash
# 症狀
AuthenticationError: Invalid API key

# 解決方案
export OPENAI_API_KEY="your-actual-api-key"

# 驗證
python -c "
import openai
client = openai.OpenAI()
print('OpenAI API 連接成功')
"
```

#### **問題 3: Playground 無法啟動**

```bash
# 症狀
ModuleNotFoundError: No module named 'src.agents.wallet_analyzer_agno'

# 解決方案
# 檢查模組路徑
ls -la src/agents/

# 如果缺少 wallet_analyzer_agno.py，創建一個簡化版本或移除導入
```

#### **問題 4: 內存使用過高**

```bash
# 診斷
ps aux | grep python | grep agno
free -h

# 解決方案
# 1. 重啟服務
sudo systemctl restart dyflow-agno

# 2. 優化配置
# 在配置文件中調整：
# - max_concurrent_agents: 2
# - timeout_seconds: 120
# - memory_storage 配置
```

### **2. 日誌分析**

```bash
# 查看 Playground 日誌
tail -f logs/playground.log

# 查看錯誤日誌
grep -i error logs/*.log | tail -20

# 查看性能日誌
tail -f logs/performance.log

# 查看告警日誌
tail -f logs/alerts.log
```

### **3. 調試模式**

```bash
# 啟用調試模式
export DEBUG=true
export LOG_LEVEL=DEBUG

# 重啟服務
sudo systemctl restart dyflow-agno

# 查看詳細日誌
tail -f logs/playground.log | grep DEBUG
```

### **4. 恢復流程**

```bash
# 4.1 完全重啟流程
sudo systemctl stop dyflow-agno
sleep 5
sudo systemctl start dyflow-agno

# 4.2 重置配置
cp config/default.yaml config/default.yaml.backup
# 恢復到已知良好的配置

# 4.3 重建環境 (最後手段)
rm -rf venv/
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install agno openai anthropic duckduckgo-search
```

## 📈 性能優化

### **1. 資源優化**

```bash
# 1.1 調整並發設置
# 在 config/agno_dag_complete.yaml 中：
performance:
  max_concurrent_agents: 2  # 降低並發數
  timeout_seconds: 180      # 縮短超時時間
  
# 1.2 啟用緩存
caching:
  enable_redis: true
  default_ttl: 600
  max_cache_size: "200MB"
```

### **2. API 調用優化**

```bash
# 2.1 使用批量調用
# 2.2 實施請求限流
# 2.3 優化提示詞長度
```

## 🚀 生產部署

### **1. 生產環境配置**

```bash
# 1.1 設置生產環境變量
export ENVIRONMENT=production
export DEBUG=false
export LOG_LEVEL=INFO

# 1.2 使用生產數據庫
export DATABASE_URL=postgresql://user:password@localhost/dyflow_prod

# 1.3 啟用監控
export ENABLE_MONITORING=true
export METRICS_ENDPOINT=http://prometheus:9090
```

### **2. 安全加固**

```bash
# 2.1 設置防火牆
sudo ufw allow 7777/tcp
sudo ufw enable

# 2.2 配置 SSL (使用 nginx)
# 配置 nginx 反向代理和 SSL 證書

# 2.3 API 密鑰輪換
# 設置定期 API 密鑰輪換計劃
```

### **3. 擴展部署**

```bash
# 3.1 容器化部署
docker build -t dyflow-agno .
docker run -d -p 7777:7777 dyflow-agno

# 3.2 Kubernetes 部署
kubectl apply -f k8s/dyflow-agno-deployment.yaml

# 3.3 負載均衡
# 配置多實例負載均衡
```

## 📞 支持和維護

### **聯繫方式**
- 技術支持: [技術支持郵箱]
- 問題報告: [GitHub Issues]
- 文檔反饋: [文檔反饋地址]

### **維護計劃**
- 定期更新: 每週
- 安全補丁: 即時
- 功能升級: 每月
- 系統備份: 每日

---

**部署完成！🎉**

您的 Dy-Flow v3 + Agno Framework 系統現在已經準備就緒，可以開始智能化的 DeFi 自動化交易！

記住定期檢查系統健康狀況並保持組件更新。

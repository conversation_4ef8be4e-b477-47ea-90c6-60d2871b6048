# DyFlow项目结构

## 📋 项目概述

DyFlow是基于Agno Framework的LP监控和自动调整系统，专注于BSC和Solana链上的流动性池管理。

## 🏗️ 核心架构

### 基于Agno Framework
- **Teams**: 多Agent协作团队
- **Workflows**: 结构化工作流程
- **Agents**: 专业化AI代理

### 主要功能
1. **LP监控**: 实时监控BSC/Solana LP池子
2. **风险评估**: AI驱动的风险分析
3. **自动调整**: 智能LP范围调整
4. **对冲管理**: 自动对冲策略

## 📁 项目结构

```
dyflow/
├── dyflow_main.py              # 主程序入口
├── PROJECT_STRUCTURE.md        # 项目结构文档
├── README.md                   # 项目说明
├── requirements.txt            # 依赖包
│
├── src/                        # 源代码目录
│   ├── teams/                  # Agno Teams
│   │   ├── __init__.py
│   │   ├── base_team.py        # 基础团队类
│   │   ├── lp_monitoring_team.py   # LP监控团队
│   │   ├── lp_adjustment_team.py   # LP调整团队
│   │   └── risk_management_team.py # 风险管理团队
│   │
│   ├── workflows/              # Agno Workflows
│   │   └── __init__.py
│   │
│   ├── agents/                 # 现有Agent (保留兼容)
│   │   ├── scorer_v2_agno.py   # 评分Agent
│   │   └── risk_sentinel_agno.py # 风险监控Agent
│   │
│   ├── integrations/           # 外部集成
│   │   └── pancakeswap.py      # PancakeSwap V3 API
│   │
│   ├── agno_tools/            # Agno工具
│   │   ├── pool_scanner_tool.py
│   │   └── pool_scoring_tool.py
│   │
│   └── utils/                 # 工具类
│       ├── config.py
│       └── database.py
│
├── data/                      # 数据目录
│   ├── agno_memory/          # Agno存储
│   └── reports/              # 报告输出
│
└── config/                   # 配置文件
    └── settings.yaml
```

## 🚀 核心组件

### 1. DyFlow主系统 (`dyflow_main.py`)
- 系统入口点
- 整合Teams和Workflows
- 提供命令行接口

### 2. Teams架构 (`src/teams/`)

#### LPMonitoringTeam
- **DataScout**: 数据收集专家
- **PoolAnalyzer**: 池子分析专家  
- **MarketMonitor**: 市场监控专家

#### LPAdjustmentTeam (待实现)
- **StrategyAdvisor**: 策略顾问
- **ExecutionManager**: 执行管理
- **RiskController**: 风险控制

#### RiskManagementTeam (待实现)
- **RiskAnalyst**: 风险分析师
- **HedgeManager**: 对冲管理
- **AlertSystem**: 预警系统

### 3. 数据集成 (`src/integrations/`)
- **PancakeSwap V3**: BSC链数据源
- **Solana DEX**: Solana链数据源 (待实现)

## 🎯 使用方法

### 基本命令

```bash
# 单次监控
python dyflow_main.py monitor

# 持续监控 (120分钟)
python dyflow_main.py continuous 120

# 获取市场总结
python dyflow_main.py market

# 分析特定池子
python dyflow_main.py analyze pool1,pool2
```

### 配置选项

系统配置在 `dyflow_main.py` 中的 `DyFlowSystem.config`:

```python
{
    'supported_chains': ['bsc', 'solana'],
    'default_monitoring_pools': 10,
    'monitoring_interval_minutes': 5,
    'risk_check_interval_minutes': 2,
    'auto_adjustment_enabled': True,
    'max_slippage_percent': 1.0,
    'min_tvl_threshold': 50000
}
```

## 📊 数据流程

1. **数据收集**: Teams收集BSC/Solana LP数据
2. **AI分析**: 多Agent协作分析池子性能
3. **风险评估**: 评估无常损失和市场风险
4. **策略制定**: 生成LP调整和对冲建议
5. **自动执行**: 执行调整操作 (待实现)

## 🔧 技术栈

- **Agno Framework**: AI Agent和Teams框架
- **OpenAI GPT-4o**: 主要AI模型
- **The Graph**: 区块链数据查询
- **SQLite**: 本地数据存储
- **Structlog**: 结构化日志

## 📈 监控指标

### 池子指标
- TVL (总锁定价值)
- 24h交易量
- 手续费率和收益
- 流动性深度
- 价格影响

### 风险指标
- 无常损失
- 价格波动率
- 流动性风险
- 对手方风险

### 性能指标
- APR (年化收益率)
- 夏普比率
- 最大回撤
- 胜率

## 🛠️ 开发指南

### 添加新的Team

1. 继承 `DyFlowBaseTeam`
2. 定义专门的Agent
3. 实现团队特定方法
4. 在 `__init__.py` 中注册

### 添加新的Agent

1. 使用 `Agent` 类创建
2. 定义角色和指令
3. 配置工具和模型
4. 集成到相应Team

### 添加新的数据源

1. 在 `src/integrations/` 创建集成类
2. 实现数据获取方法
3. 在Teams中集成使用

## 📝 待实现功能

1. **LP调整团队**: 自动调整LP范围
2. **风险管理团队**: 实时风险监控和对冲
3. **Solana集成**: 支持Solana链LP监控
4. **Web界面**: 可视化监控面板
5. **通知系统**: 风险预警和报告推送
6. **回测系统**: 策略历史表现分析

## 🔒 安全考虑

- API密钥安全存储
- 交易权限最小化
- 多重签名支持
- 审计日志记录

## 📞 支持

如有问题或建议，请查看项目文档或联系开发团队。

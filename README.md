# Dy-Flow 2.0 - Delta-Neutral LP + Perp-Hedge

![Python](https://img.shields.io/badge/python-3.9+-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)
![Status](https://img.shields.io/badge/status-active-green.svg)
![Version](https://img.shields.io/badge/version-2.0.0-orange.svg)

**Delta-Neutral LP with Perpetual Hedging System** - 跨 BSC 与 Solana 的全自动化流动性挖矿策略，集成期货对冲实现 Delta 中性。
**Experimental v3 skeleton code is available under `src/v3` for the upcoming agentic architecture. A new `LpMonitorAgent` demonstrates dynamic risk-based scheduling.**


## 🎯 核心功能

- 🤖 **AGNO DAG 工作流**: 7个专业 AI Agent 协同决策
- 🔍 **智能池子发现**: 自动发现 PancakeSwap & Meteora DLMM 高收益池
- ⚖️ **Delta 中性策略**: 期货对冲消除价格风险
- 📊 **因子评分模型**: `0.45*APR + 0.25*Volume + 0.10*Depth - 0.15*Volatility - 0.05*IL`
- 🛡️ **极致风险控制**: 1小时跌幅 >10% 触发紧急退出
- 🔄 **自动再平衡**: ATR 范围突破自动调整
- 💰 **USDT 金库模式**: 本金保持 USDT，收益复投
- 🧠 **LLM 驱动决策**: 本地 Ollama Qwen3 7B 模型

## 🏗️ 系统架构

### Multi-Agent DAG 工作流

```mermaid
graph LR
    A[Pool Hunter] --> B[Scorer]
    B --> C[Planner LLM]
    D[Risk Sentinel] --> C
    C --> E[Executor]
    E --> F[Auditor]
    F --> G[Critic LLM]
    F --> H[CLI Reporter]
    D --> H
```

### 核心 Agent 说明

| Agent | 功能 | 执行频率 | 类型 |
|-------|------|----------|------|
| **Pool Hunter** | 获取高 APR 池子 | 5分钟 | Python |
| **Scorer** | 因子评分排序 | 触发式 | Python |
| **Risk Sentinel** | 价格监控预警 | 1分钟 | Python |
| **Planner** | 再平衡决策 | 触发式 | LLM |
| **Executor** | LP & 对冲执行 | 触发式 | Python |
| **Auditor** | PnL 审计 | 6小时 | Python |
| **Critic LLM** | 策略优化 | 每日 | LLM |
| **CLI Reporter** | 实时仪表板 | 10秒 | Python |

## 🚀 快速开始

### 方法一：Docker 部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd dyflow

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env，填入必要配置：
# - SUPABASE_URL & SUPABASE_SERVICE_KEY
# - BSC_PK & SOL_KEYFILE  
# - BINANCE_API_KEY & BINANCE_SECRET_KEY

# 3. 拉取 LLM 模型
docker-compose exec ollama ollama pull qwen3:7b

# 4. 启动服务
docker-compose up -d

# 5. 查看状态
curl http://localhost:8080/health
```

### 方法二：本地开发

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动本地 Ollama
ollama serve
ollama pull qwen3:7b

# 3. 配置 Supabase 数据库
# 在 Supabase 控制台执行 docs/supabase_schema.sql

# 4. 运行应用
python main.py
```

## 📊 实时监控界面

```
┌─ Dy-Flow 2.0 Delta-Neutral Dashboard ─────────────────────┐
│ Pool           Qty    Hedge   APR   IL   Daily$  Status   │
│ BNB-BUSD       0      0      312%  1.1%  +4.7   ✅ Hold   │
│ SOL-USDC      +3k    -3k     198%  0.5%  +3.1   🔄 Rebal  │
│ ETH-USDT      +2k    -2k     156%  0.8%  +2.9   ✅ Hold   │
│ ─────────────────────────────────────────────────────────│
│ Top Candidates: GMT-BNB 0.71  CAKE-ETH 0.68  ...         │
│ Emergency Risk: ✅ Clear  │  Net 24h PnL: +12.8$         │
│ Next Scan: 04:23  │  Gas: 2.3 gwei  │  Hedge Ratio: 95% │
└───────────────────────────────────────────────────────────┘
```

## ⚙️ 策略参数

### 核心配置

```yaml
strategy:
  # 评分阈值
  enter_threshold: 0.60      # 进入阈值
  exit_threshold: 0.30       # 退出阈值
  max_positions: 5           # 最大持仓数
  
  # 风险控制
  emergency_exit_threshold: 0.10  # 10% 紧急退出
  max_slippage: 0.005            # 0.5% 最大滑点
  
  # ATR 参数
  atr_window: 14                 # ATR 计算窗口
  lp_range_multiplier: 1.5       # ±1.5 ATR LP 范围
  trigger_multiplier: 2.0        # ±2 ATR 触发重平衡
  
  # 对冲配置
  funding_rate_threshold: 0.10   # 10% 年化资金费率阈值
  hedge_efficiency: 0.95         # 对冲效率
  max_leverage: 3.0              # 最大杠杆
```

### 数据源

| 链 | DEX | API 端点 |
|----|----|---------|
| **BSC** | PancakeSwap V2/V3 | StreamingFast GraphQL |
| **Solana** | Meteora DLMM | Official REST API |

## 💡 策略逻辑

### USDT 金库流程

```
USDT Pool → 进入时
├─ 50% → TokenA
├─ 50% → TokenB  
└─ 添加流动性

LP Position → 退出时
├─ 移除流动性
├─ 全部换回 USDT
└─ 更新金库
```

### Delta 中性对冲

```
LP 敞口 → 分析
├─ 计算 Delta 敞口
├─ 期货开仓对冲
├─ 维持 95% 中性度
└─ 监控资金费率
```

## 🛡️ 风险管理

### 多层保护机制

1. **实时价格监控**: 1分钟检查价格变动
2. **紧急退出**: 任何资产1小时跌幅 >10% 立即退出
3. **资金费率监控**: 年化 >10% 关闭对冲
4. **滑点保护**: 最大 0.5% 滑点限制
5. **Gas 效率**: 预期收益 > Gas 成本 × 1.2 才执行

### 风险事件处理

```python
if price_drop_1h > 10%:
    emergency_exit_all_positions()
    convert_all_to_usdt()
    lock_strategy()
    send_alert()
```

## 🗄️ 数据库架构

### 核心表结构

- **pools**: 池子信息和评分
- **positions**: LP 持仓记录
- **hedges**: 期货对冲持仓
- **transactions**: 所有链上交易
- **metrics**: 时序指标数据
- **daily_summary**: 每日汇总报告

### 部署选项

- **生产环境**: Supabase 托管 PostgreSQL
- **开发环境**: 本地 Docker PostgreSQL
- **数据备份**: 自动每日备份

## 🔧 API 接口

### 健康检查

```bash
curl http://localhost:8080/health
# {
#   "status": "healthy",
#   "components": {
#     "scheduler": true,
#     "llm": true,
#     "database": true
#   }
# }
```

### 系统状态

```bash
curl http://localhost:8080/status
# {
#   "active_positions": 3,
#   "total_value_usd": 15420.50,
#   "daily_pnl": 127.80,
#   "hedge_ratio": 0.95
# }
```

## 📈 监控 & 告警

### Prometheus 指标

- `dyflow_system_running`: 系统运行状态
- `dyflow_active_positions`: 活跃持仓数
- `dyflow_daily_pnl`: 每日 PnL
- `dyflow_hedge_ratio`: 对冲比例
- `dyflow_emergency_events`: 紧急事件计数

### Telegram 告警

```bash
# 配置 .env
ENABLE_NOTIFICATIONS=true
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id
```

## 🔮 LLM 集成

### 本地 Ollama

```bash
# 拉取模型
ollama pull qwen3:7b

# 检查状态
curl http://localhost:11434/api/tags
```

### 决策提示模板

- **Rebalance Planner**: `tools/rebalance_prompt.j2`
- **Strategy Critic**: `tools/critic_prompt.j2`

## 🐳 Docker 配置

### 基础服务

```bash
docker-compose up -d  # 基础服务
```

### 完整监控栈

```bash
docker-compose --profile monitoring up -d
# 包含: Prometheus + Grafana
```

### 本地数据库

```bash
docker-compose --profile local-db up -d
# 本地 PostgreSQL 用于开发
```

## 🔐 安全要求

### 必需密钥

```env
# 数据库访问
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key

# 区块链钱包
BSC_PK=your_bsc_private_key
SOL_KEYFILE=/path/to/solana/keypair.json

# 交易所 API (选择一个)
BINANCE_API_KEY=your_binance_key
BINANCE_SECRET_KEY=your_binance_secret
```

### 安全建议

1. **私钥管理**: 使用环境变量，禁止硬编码
2. **API 权限**: 仅开启必要的交易权限
3. **网络安全**: 使用 VPN 和防火墙
4. **定期审计**: 监控异常交易和 API 调用

## 📋 部署清单

- [ ] 创建 Supabase 项目并执行 schema
- [ ] 生成 BSC 和 Solana 钱包
- [ ] 申请 Binance Futures API 密钥
- [ ] 配置 .env 环境变量
- [ ] 拉取 Ollama qwen3:7b 模型
- [ ] 启动 Docker 服务
- [ ] 验证健康检查通过
- [ ] 配置 Telegram 告警（可选）
- [ ] 设置监控面板（可选）

## 🎓 使用指南

1. **初次运行**: 系统将自动发现池子并开始评分
2. **资金投入**: 确保钱包有足够 USDT/SOL 作为基础货币
3. **监控运行**: 通过 CLI 界面观察实时状态
4. **参数调优**: 根据市场情况调整策略参数
5. **收益提取**: 系统自动复投，可手动提取收益

## 🔄 升级路径

从 Dy-Flow 1.0 升级：

```bash
# 备份现有配置
cp -r config config_backup

# 更新代码
git pull origin main

# 更新数据库 schema
# 在 Supabase 控制台执行迁移脚本

# 重启服务
docker-compose down
docker-compose up -d
```

## 📚 文档

- [完整部署指南](docs/DEPLOYMENT.md)
- [API 接口文档](docs/API.md)
- [AGNO DAG 配置](docs/agno_flow.yaml)
- [数据库架构](docs/supabase_schema.sql)

## 🤝 贡献

欢迎贡献代码和建议！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证。

## ⚠️ 免责声明

Dy-Flow 2.0 是实验性金融软件，投资有风险，请谨慎使用。开发者不承担任何投资损失责任。

---

**🚀 准备启动您的 Delta-Neutral LP 策略了吗？**

```bash
git clone <repository-url>
cd dyflow
cp .env.example .env
# 编辑 .env 配置
docker-compose up -d
# DyFlow

DyFlow is a Liquidity Provider (LP) monitoring and adjustment system built around the Agno Framework. The project focuses on BSC and Solana networks and provides tools for collecting market data, scoring pools and planning LP strategies.

## Setup

1. Install Python 3.11.
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Prepare configuration files under `config/` or use the provided defaults.

## Usage

Run a monitoring cycle:
```bash
python dyflow_main.py monitor
```

Run continuous monitoring for a specific duration (minutes):
```bash
python dyflow_main.py continuous 120
```

Generate a market summary:
```bash
python dyflow_main.py market
```

For details on deployment and API usage see the documentation in the `docs/` directory.

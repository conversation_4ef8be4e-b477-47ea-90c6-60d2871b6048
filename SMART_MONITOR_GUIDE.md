# 🤖 DyFlow Smart Monitor 使用指南

## 📋 概述

DyFlow Smart Monitor 是一个AI驱动的智能LP监控系统，能够：

- 🔍 **自动发现新池子** - 持续扫描BSC和Solana链上的新LP池子
- 🤖 **AI智能分析** - 使用Agno Framework的Agent自动分析投资策略
- 📊 **实时监控** - 美观的CLI界面实时显示监控状态
- 💰 **投资建议** - 自动生成BUY/HOLD/AVOID建议和进入区间
- 🚨 **智能警报** - 发现高APR池子时自动警报

## 🚀 快速开始

### 1. 查看配置
```bash
./start_smart_monitor.sh config
```

### 2. 一次性发现分析
```bash
./start_smart_monitor.sh discover
```

### 3. 启动持续监控
```bash
./start_smart_monitor.sh monitor
```

## 🎯 监控模式

### 标准监控
```bash
./start_smart_monitor.sh monitor
```
- ⏰ 扫描间隔: 60秒
- 📊 APR警报阈值: 25%
- 🤖 AI自动分析: 启用

### 快速监控
```bash
./start_smart_monitor.sh fast
```
- ⏰ 扫描间隔: 30秒
- 📊 APR警报阈值: 20%
- 🔥 适合活跃交易者

### 保守监控
```bash
./start_smart_monitor.sh conservative
```
- ⏰ 扫描间隔: 120秒
- 📊 APR警报阈值: 50%
- 💰 最小APR: 30%
- 🛡️ 适合稳健投资者

## 📊 界面说明

### 主界面布局
```
╭─────────────────────────────────────────╮
│        🤖 DyFlow Smart Monitor          │
│    AI-Powered LP Discovery & Analysis   │
╰─────────────────────────────────────────╯

┌─── 🚨 New Pool Alerts & AI Analysis ───┐  ┌─ 📈 Stats ─┐
│ Time  │ Chain │ Pair     │ APR │ Strategy │  │ Scanned: 50 │
│ 17:42 │ BSC   │ ETH/USDT │ 63% │ BUY      │  │ New: 7      │
└─────────────────────────────────────────┘  │ Analyzed: 3 │
                                             └─────────────┘
┌────────── 🔧 Configuration ─────────────┐
│ Min TVL: $50,000                        │
│ Alert APR: 25%                          │
│ Auto Analysis: ✅                       │
└─────────────────────────────────────────┘
```

### 警报表格说明
- **Time**: 发现时间
- **Chain**: BSC 或 SOL
- **Pair**: 交易对名称
- **TVL**: 总锁定价值
- **APR**: 年化收益率
- **Status**: 分析状态 (pending/analyzing/completed/error)
- **Strategy**: AI分析结果 (BUY/HOLD/AVOID)

### 状态颜色
- 🟢 **绿色**: BUY建议，低风险
- 🟡 **黄色**: HOLD建议，中等风险
- 🔴 **红色**: AVOID建议，高风险
- 🔵 **蓝色**: 正在分析中

## 🤖 AI分析功能

### 分析维度
1. **收益潜力评估** - 基于APR和手续费收入
2. **流动性分析** - TVL稳定性和交易深度
3. **风险评估** - 无常损失和市场风险
4. **策略建议** - 具体的投资建议

### 投资策略输出
```json
{
  "strategy_recommendation": "BUY - High potential",
  "risk_level": "Medium",
  "entry_range": {
    "min_price": 95000,
    "max_price": 105000,
    "target_allocation": 0.1
  }
}
```

## 📈 监控统计

### 实时统计指标
- **Pools Scanned**: 总扫描池子数
- **New Discovered**: 新发现的高APR池子
- **AI Analyzed**: AI分析完成数量
- **Buy Recommendations**: 买入建议数量
- **Active Alerts**: 当前活跃警报

## ⚙️ 配置参数

### 扫描设置
- `min_tvl`: 最小TVL阈值 ($50,000)
- `min_apr`: 最小APR阈值 (10%)
- `alert_threshold_apr`: APR警报阈值 (25%)
- `scan_interval`: 扫描间隔 (60秒)
- `max_pools_per_chain`: 每链最大池子数 (20)

### AI分析设置
- `auto_analysis`: 自动AI分析 (启用)
- `max_risk_score`: 最大风险评分 (7/10)

## 🔍 发现新池子流程

1. **扫描阶段**
   - 每60秒扫描BSC和Solana
   - 获取TVL和APR最高的池子
   - 过滤掉已知池子

2. **警报阶段**
   - 检查APR是否超过警报阈值
   - 创建新池子警报
   - 显示在界面中

3. **分析阶段**
   - AI Agent自动分析池子
   - 评估投资潜力和风险
   - 生成具体策略建议

4. **建议阶段**
   - 显示BUY/HOLD/AVOID建议
   - 提供进入价格区间
   - 计算目标配置比例

## 📝 会话报告

监控结束时自动保存报告到 `data/reports/smart_monitor_*.json`:

```json
{
  "session_id": "smart_monitor_1749116563",
  "timestamp": "2025-06-05T17:42:43",
  "monitoring_stats": {
    "total_scanned": 14,
    "new_discovered": 7,
    "analyzed": 3,
    "recommendations": 0
  },
  "alerts": [...],
  "config": {...}
}
```

## 🎯 使用场景

### 1. 日常监控
```bash
./start_smart_monitor.sh monitor
```
适合日常LP投资监控，平衡效率和准确性。

### 2. 积极发现
```bash
./start_smart_monitor.sh fast
```
适合寻找短期机会的活跃交易者。

### 3. 稳健投资
```bash
./start_smart_monitor.sh conservative
```
适合寻找高质量、低风险LP机会的长期投资者。

### 4. 快速分析
```bash
./start_smart_monitor.sh discover
```
适合快速了解当前市场上的优质LP机会。

## 🚨 注意事项

1. **网络连接**: 需要稳定的网络连接访问API
2. **API限制**: 注意API调用频率限制
3. **风险提示**: AI建议仅供参考，请自行判断投资风险
4. **数据延迟**: 链上数据可能有轻微延迟
5. **资源消耗**: 持续监控会消耗一定的CPU和网络资源

## 🔧 故障排除

### 常见问题
1. **无法获取数据**: 检查网络连接和API密钥
2. **AI分析失败**: 检查Agno Framework是否正确安装
3. **界面显示异常**: 确保终端支持Rich库的显示功能

### 日志查看
监控过程中的详细日志会显示在控制台，包括：
- 池子扫描状态
- AI分析进度
- 错误信息和警告

---

**🌟 享受智能LP监控带来的便利！**

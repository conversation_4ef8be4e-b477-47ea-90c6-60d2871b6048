# AGNO DAG 配置文件
# 定义 AI Agent 工作流程

dag:
  name: "dyflow_trading_dag"
  description: "Dy-Flow 自动化交易 DAG"
  schedule_interval: 300  # 5分钟执行一次
  max_active_runs: 1
  catchup: false

# Agent 配置
agents:
  pool_hunter:
    name: "池子猎手"
    description: "发现和筛选优质流动性池"
    priority: 1
    dependencies: []
    retry_attempts: 3
    timeout: 120
    
  risk_sentinel:
    name: "风险哨兵"
    description: "监控和评估风险指标"
    priority: 2
    dependencies: ["pool_hunter"]
    retry_attempts: 2
    timeout: 60
    
  scorer:
    name: "评分器"
    description: "对池子进行综合评分"
    priority: 3
    dependencies: ["pool_hunter", "risk_sentinel"]
    retry_attempts: 2
    timeout: 90
    
  planner:
    name: "规划器"
    description: "制定交易策略和计划"
    priority: 4
    dependencies: ["scorer"]
    retry_attempts: 3
    timeout: 120
    
  executor:
    name: "执行器"
    description: "执行交易操作"
    priority: 5
    dependencies: ["planner"]
    retry_attempts: 1
    timeout: 180
    
  earnings_auditor:
    name: "收益审计员"
    description: "审计和报告收益情况"
    priority: 6
    dependencies: ["executor"]
    retry_attempts: 2
    timeout: 60
    
  cli_reporter:
    name: "CLI 报告器"
    description: "生成和显示实时报告"
    priority: 7
    dependencies: ["earnings_auditor"]
    retry_attempts: 3
    timeout: 30

# 执行策略
execution:
  parallel_execution: false  # 串行执行
  failure_strategy: "stop_on_first_failure"
  notification_on_failure: true
  notification_on_success: false

# 监控配置
monitoring:
  enable_metrics: true
  log_level: "INFO"
  health_check_interval: 30
  performance_tracking: true
# Agno Framework 完整 DAG 配置
# 集成所有 Agno 增強的 Agents 到統一工作流
# 版本: v3.0 - 2025-06-04

name: "DyFlow_Agno_Complete_Pipeline"
description: "基於 Agno Framework 的完整 DeFi 自動化交易流水線"

# 全局配置
global_config:
  # Agno Framework 設置
  agno:
    primary_model: "gpt-4o"
    secondary_model: "claude-3-7-sonnet-latest"
    use_reasoning: true
    enable_memory: true
    enable_knowledge_base: true
    enable_market_data: true
    debug_mode: false
    
    # 記憶存儲配置
    memory_storage:
      type: "sqlite"
      db_file: "data/agno_memory/complete_pipeline_sessions.db"
      auto_upgrade_schema: true
    
    # 性能配置
    performance:
      max_concurrent_agents: 3
      timeout_seconds: 300
      retry_attempts: 2
      backoff_factor: 1.5

  # 執行調度配置
  scheduler:
    # 主要執行週期 - 每5分鐘
    main_cycle_minutes: 5
    
    # 深度分析週期 - 每30分鐘
    deep_analysis_minutes: 30
    
    # 策略重新評估週期 - 每2小時
    strategy_review_hours: 2
    
    # 系統健康檢查 - 每15分鐘
    health_check_minutes: 15

# Agent 定義
agents:
  # ========== 數據收集階段 ==========
  
  scout_bsc_agno:
    class: "src.agents.scout_bsc_agno.ScoutBSCAgnoAgent"
    enabled: true
    priority: 100
    timeout: 120
    config:
      agno:
        primary_model: "gpt-4o"
        use_reasoning: true
        enable_market_data: true
        enable_cache: true
        cache_duration_minutes: 10
      chains: ["BSC"]
      protocols: ["PancakeSwap", "Uniswap", "BiSwap"]
      min_tvl: 100000
      max_pools: 50

  scout_meteora_agno:
    class: "src.agents.scout_meteora_agno.ScoutMeteoraAgnoAgent"
    enabled: true
    priority: 100
    timeout: 120
    config:
      agno:
        primary_model: "gpt-4o"
        use_reasoning: true
        enable_market_data: true
        enable_cache: true
        cache_duration_minutes: 10
      chains: ["SOL"]
      protocols: ["Meteora", "Orca", "Raydium"]
      min_tvl: 50000
      max_pools: 30

  # ========== 評分分析階段 ==========
  
  scorer_v2_agno:
    class: "src.agents.scorer_v2_agno.ScorerV2AgnoAgent"
    enabled: true
    priority: 90
    timeout: 180
    dependencies: ["scout_bsc_agno", "scout_meteora_agno"]
    config:
      agno:
        primary_model: "gpt-4o"
        secondary_model: "claude-3-7-sonnet-latest"
        use_reasoning: true
        enable_memory: true
        enable_market_data: true
        debug_mode: false
        
        # 動態評分配置
        dynamic_scoring:
          volatility_weight_adjustment: true
          market_regime_adaptation: true
          liquidity_depth_analysis: true
          token_quality_verification: true
          
        # 6因子權重配置 (會根據市場狀況動態調整)
        scoring_factors:
          fee_rate: 0.20
          volume: 0.25
          tvl: 0.20
          token_quality: 0.15
          liquidity_depth: 0.12
          volatility: 0.08
          
        # AI 推理配置
        reasoning:
          enable_market_context_analysis: true
          enable_pool_comparison: true
          enable_trend_analysis: true
          confidence_threshold: 0.7

  # ========== 風險監控階段 ==========
  
  risk_sentinel_agno:
    class: "src.agents.risk_sentinel_agno.RiskSentinelAgnoAgent"
    enabled: true
    priority: 95
    timeout: 200
    dependencies: ["scorer_v2_agno"]
    schedule:
      # 持續監控模式
      continuous: true
      # 深度分析間隔
      deep_analysis_interval: "30m"
      # 快速檢查間隔
      quick_check_interval: "5m"
    config:
      agno:
        primary_model: "gpt-4o"
        secondary_model: "claude-3-7-sonnet-latest"
        use_reasoning: true
        enable_memory: true
        enable_market_data: true
        
        # 風險分析配置
        risk_analysis:
          market_risk_update_minutes: 30
          pool_risk_analysis_depth: 5  # 分析前5個池子
          trend_analysis_timeframes: ["short", "medium", "long"]
          
        # 告警配置
        alerts:
          high_priority_threshold: "high"
          critical_action_threshold: "critical"
          alert_consolidation: true
          max_alerts_per_hour: 10

  # ========== 策略規劃階段 ==========
  
  planner_agno:
    class: "src.agents.planner_agno.PlannerAgnoAgent"
    enabled: true
    priority: 85
    timeout: 240
    dependencies: ["scorer_v2_agno", "risk_sentinel_agno"]
    schedule:
      # 策略規劃間隔
      planning_interval: "2h"
      # 市場評估間隔
      market_assessment_interval: "1h"
      # 快速調整檢查
      quick_adjustment_interval: "15m"
    config:
      agno:
        primary_model: "gpt-4o"
        secondary_model: "claude-3-7-sonnet-latest"
        use_reasoning: true
        enable_memory: true
        enable_knowledge_base: true
        enable_market_data: true
        
        # 策略分析配置
        strategy_analysis:
          available_strategies: ["delta_neutral", "ladder_ss", "passive_high_tvl"]
          capital_allocation_limit: 100000  # USD
          min_allocation_per_strategy: 1000  # USD
          max_allocation_per_pool: 20000     # USD
          
        # 組合優化配置
        portfolio_optimization:
          risk_tolerance: "medium"
          diversification_target: 0.8
          rebalancing_threshold: 0.1
          max_pools_per_strategy: 3

  # ========== 執行階段 ==========
  
  executor_agno:
    class: "src.agents.executor_agno.ExecutorAgnoAgent"
    enabled: true
    priority: 80
    timeout: 300
    dependencies: ["planner_agno"]
    config:
      agno:
        primary_model: "gpt-4o"
        use_reasoning: true
        enable_memory: true
        
        # 執行配置
        execution:
          dry_run_mode: true  # 設置為 false 進行實際交易
          position_size_verification: true
          slippage_tolerance: 0.005  # 0.5%
          gas_price_optimization: true
          
        # 風險控制
        risk_controls:
          max_daily_trades: 10
          max_position_size: 50000  # USD
          stop_loss_enabled: true
          take_profit_enabled: true

  # ========== 審計和報告 ==========
  
  auditor_agno:
    class: "src.agents.auditor_agno.AuditorAgnoAgent"
    enabled: true
    priority: 70
    timeout: 120
    dependencies: ["executor_agno"]
    schedule:
      audit_interval: "1h"
      daily_report: "08:00"
      weekly_report: "MON 09:00"
    config:
      agno:
        primary_model: "gpt-4o"
        use_reasoning: true
        enable_memory: true
        
        # 審計配置
        audit:
          performance_tracking: true
          risk_compliance_check: true
          strategy_effectiveness_analysis: true
          cost_analysis: true
          
        # 報告配置
        reporting:
          generate_charts: true
          include_ai_insights: true
          benchmark_comparison: true
          recommendation_generation: true

# 工作流定義
workflows:
  # ========== 主要數據流水線 ==========
  
  main_pipeline:
    name: "主要 DeFi 分析流水線"
    schedule: "*/5 * * * *"  # 每5分鐘執行
    agents:
      - name: "scout_bsc_agno"
        parallel_group: "data_collection"
      - name: "scout_meteora_agno"
        parallel_group: "data_collection"
      - name: "scorer_v2_agno"
        depends_on: ["data_collection"]
      - name: "risk_sentinel_agno"
        depends_on: ["scorer_v2_agno"]
        mode: "continuous"  # 持續運行
      - name: "planner_agno"
        depends_on: ["scorer_v2_agno", "risk_sentinel_agno"]
        condition: "risk_level < high"  # 風險不高時才執行規劃
      - name: "executor_agno"
        depends_on: ["planner_agno"]
        condition: "has_valid_plans"
      - name: "auditor_agno"
        depends_on: ["executor_agno"]
        mode: "async"  # 異步執行

  # ========== 深度分析流水線 ==========
  
  deep_analysis_pipeline:
    name: "深度市場分析流水線"
    schedule: "0 */30 * * * *"  # 每30分鐘執行
    agents:
      - name: "risk_sentinel_agno"
        mode: "deep_analysis"
        config:
          analysis_depth: "comprehensive"
          market_research: true
          trend_forecasting: true
      - name: "planner_agno"
        mode: "strategic_review"
        depends_on: ["risk_sentinel_agno"]
        config:
          strategy_optimization: true
          portfolio_rebalancing: true
          performance_review: true

  # ========== 緊急風險響應流水線 ==========
  
  emergency_response_pipeline:
    name: "緊急風險響應流水線"
    trigger: "risk_alert_critical"
    agents:
      - name: "risk_sentinel_agno"
        mode: "emergency_analysis"
        priority: 99
      - name: "planner_agno"
        mode: "risk_mitigation"
        depends_on: ["risk_sentinel_agno"]
        config:
          emergency_strategy: "defensive"
          position_reduction: true
      - name: "executor_agno"
        mode: "emergency_execution"
        depends_on: ["planner_agno"]
        config:
          fast_execution: true
          override_limits: true

# 監控和告警配置
monitoring:
  # 系統健康監控
  health_checks:
    agent_responsiveness: 60  # 秒
    memory_usage_threshold: 80  # %
    error_rate_threshold: 5   # %
    
  # 業務指標監控
  business_metrics:
    portfolio_performance: true
    risk_exposure: true
    strategy_effectiveness: true
    
  # 告警配置
  alerts:
    channels: ["log", "webhook"]  # 可擴展到 slack, email 等
    severity_levels: ["info", "warning", "error", "critical"]
    rate_limiting: true

# 性能優化配置
performance:
  # 緩存策略
  caching:
    enable_redis: false  # 可擴展到 Redis
    default_ttl: 300     # 秒
    max_cache_size: "100MB"
    
  # 並發控制
  concurrency:
    max_workers: 4
    agent_timeout: 300   # 秒
    queue_size: 100
    
  # 資源管理
  resources:
    max_memory_per_agent: "512MB"
    cpu_limit_per_agent: 50  # %
    disk_usage_limit: "10GB"

# 安全配置
security:
  # API 密鑰管理
  api_keys:
    rotation_days: 90
    encryption: true
    
  # 交易安全
  trading:
    whitelist_tokens: true
    blacklist_protocols: []
    max_slippage: 0.01  # 1%
    
  # 審計日誌
  audit_logging:
    enable: true
    retention_days: 365
    encryption: true

# 備份和恢復
backup:
  # 數據備份
  data_backup:
    schedule: "0 2 * * *"  # 每天凌晨2點
    retention_days: 30
    
  # 配置備份
  config_backup:
    versioning: true
    git_integration: true
    
  # 災難恢復
  disaster_recovery:
    backup_location: "data/backups/"
    recovery_time_objective: "15m"
    recovery_point_objective: "5m"

# 環境變量要求
environment_variables:
  required:
    - OPENAI_API_KEY
    - ANTHROPIC_API_KEY
  optional:
    - DUCKDUCKGO_API_KEY
    - EXA_API_KEY
    - REDIS_URL
    - DATABASE_URL
    - WEBHOOK_URL

# 版本控制
version:
  config_version: "3.0"
  compatibility: "dy-flow-v3"
  last_updated: "2025-06-04"
  changelog:
    - "v3.0: 完整 Agno Framework 集成"
    - "v3.0: 多代理協作工作流"
    - "v3.0: 企業級監控和告警"
    - "v3.0: 緊急響應機制"

# Nginx Configuration for Dy-Flow 2.0 (Simplified)

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Basic settings
    sendfile on;
    keepalive_timeout 65;
    gzip on;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    # Upstream
    upstream dyflow_backend {
        server dyflow:8080;
    }

    server {
        listen 80;
        server_name localhost;

        # Health check
        location /health {
            proxy_pass http://dyflow_backend;
        }

        # API endpoints
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://dyflow_backend;
        }

        # Metrics (restricted)
        location /metrics {
            allow **********/16;
            deny all;
            proxy_pass http://dyflow_backend;
        }

        # Default
        location / {
            proxy_pass http://dyflow_backend;
        }
    }
}
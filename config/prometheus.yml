# Prometheus Configuration for Dy-Flow 2.0

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Dy-Flow main application metrics
  - job_name: 'dyflow'
    static_configs:
      - targets: ['dyflow:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
    scrape_timeout: 10s

  # Prometheus self-monitoring
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Redis metrics (if redis_exporter is added)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 15s

  # PostgreSQL metrics (if postgres_exporter is added)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

# Alerting rules
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Example alerting rules (uncomment to use)
# rule_files:
#   - "dyflow_alerts.yml"
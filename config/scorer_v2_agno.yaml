# Scorer V2 Agno Agent 配置文件
# 基於 Agno Framework 的增強版池子評分系統配置

# ========== 基本配置 ==========
name: "scorer_v2_agno"
version: "1.0.0"
description: "AI-enhanced pool scoring with Agno Framework integration"

# ========== Agno Framework 配置 ==========
agno:
  # 啟用 Agno 功能
  enabled: true
  
  # LLM 模型配置
  primary_model: "gpt-4o"           # 主要模型：池子分析和市場分析
  secondary_model: "claude-3-7-sonnet-latest"  # 次要模型：權重優化
  fallback_model: "gpt-4o-mini"     # 降級模型：成本優化場景
  
  # AI 功能開關
  use_reasoning: true               # 啟用推理工具
  enable_memory: true               # 啟用記憶存儲
  debug_mode: false                 # 調試模式（顯示工具調用）
  
  # 會話和存儲配置
  memory_storage:
    enabled: true
    db_file: "data/agno_memory/scorer_v2_sessions.db"
    table_name: "scorer_v2_agno_memory"
    auto_upgrade_schema: true
    session_ttl_hours: 24           # 會話過期時間
  
  # API 配置
  api_config:
    timeout_seconds: 30             # API 超時時間
    max_retries: 3                  # 最大重試次數
    retry_delay_seconds: 2          # 重試延遲
    rate_limit_rpm: 50              # 每分鐘請求限制

# ========== AI 分析配置 ==========
ai_analysis:
  # 市場分析配置
  market_analysis:
    enabled: true
    confidence_threshold: 0.7       # 最低信心度閾值
    update_interval_minutes: 30     # 市場分析更新間隔
    factors:
      - volatility_level
      - volume_trend  
      - market_sentiment
      - risk_appetite
  
  # 權重優化配置
  weight_optimization:
    enabled: true
    auto_adjustment: true           # 自動權重調整
    adjustment_sensitivity: 0.1     # 調整敏感度 (0.0-1.0)
    rebalance_threshold: 0.15       # 權重變化閾值觸發重新平衡
    
    # 權重邊界限制
    weight_bounds:
      fee_tvl: [0.10, 0.40]        # 費率權重範圍
      volume_score: [0.05, 0.30]   # 交易量權重範圍
      tvl_score: [0.10, 0.35]      # TVL權重範圍
      token_quality: [0.15, 0.45]  # 代幣品質權重範圍
      liquidity_depth: [0.05, 0.25] # 流動性深度權重範圍
      volatility_score: [0.05, 0.20] # 波動率權重範圍
  
  # 池子分析配置
  pool_analysis:
    enabled: true
    max_ai_pools: 10                # 每次執行最多 AI 分析的池子數
    confidence_threshold: 0.8       # 池子分析信心度閾值
    reasoning_depth: "detailed"     # 推理深度: basic/detailed/comprehensive
    
    # 分析因子配置
    factors:
      fee_score:
        weight: 1.0
        description: "費率收益分析"
      volume_score:
        weight: 1.0
        description: "交易量和流動性分析"
      tvl_score:
        weight: 1.0
        description: "TVL 穩定性分析"
      token_quality_score:
        weight: 1.2                 # 代幣品質權重較高
        description: "代幣品質和信譽分析"
      liquidity_depth_score:
        weight: 1.0
        description: "流動性深度分析"
      volatility_score:
        weight: 0.8                 # 波動率影響相對較小
        description: "價格穩定性分析"

# ========== 評分算法配置 ==========
scoring:
  # 基礎評分參數 (繼承自 scorer_v2.yaml)
  base_weights:
    fee_tvl: 0.25
    volume_score: 0.20
    tvl_score: 0.20
    token_quality: 0.20
    liquidity_depth: 0.10
    volatility_score: 0.05
  
  # AI 增強評分參數
  ai_enhanced:
    enabled: true
    confidence_boost: 1.1           # 高信心度分析的評分提升
    reasoning_bonus: 0.05           # 詳細推理的額外分數
    consensus_weight: 0.3           # AI 與傳統評分的共識權重
    
    # 評分調整規則
    adjustment_rules:
      high_confidence:              # 高信心度 (> 0.9)
        score_multiplier: 1.05
        hedgeable_bonus: 0.02
      medium_confidence:            # 中等信心度 (0.7-0.9)
        score_multiplier: 1.0
        hedgeable_bonus: 0.0
      low_confidence:               # 低信心度 (< 0.7)
        score_multiplier: 0.95
        hedgeable_penalty: -0.05

# ========== 性能優化配置 ==========
performance:
  # 並發控制
  concurrency:
    max_concurrent_ai_calls: 3      # 最大並發 AI 調用數
    pool_analysis_batch_size: 5     # 池子分析批次大小
    timeout_per_analysis: 45        # 單個分析超時時間（秒）
  
  # 緩存配置
  caching:
    enabled: true
    market_analysis_ttl: 1800       # 市場分析緩存 30 分鐘
    weight_optimization_ttl: 3600   # 權重優化緩存 1 小時
    pool_analysis_ttl: 900          # 池子分析緩存 15 分鐘
  
  # 降級策略
  fallback:
    ai_failure_threshold: 3         # AI 失敗次數閾值
    fallback_to_traditional: true   # 是否降級到傳統評分
    recovery_attempts: 2            # 恢復嘗試次數
    recovery_delay_minutes: 5       # 恢復嘗試延遲

# ========== 監控和日誌配置 ==========
monitoring:
  # 性能指標
  metrics:
    enabled: true
    track_ai_response_times: true
    track_confidence_scores: true
    track_scoring_accuracy: true
    
    # 指標閾值
    thresholds:
      avg_response_time_ms: 10000   # 平均響應時間閾值
      min_confidence_score: 0.7     # 最低信心度閾值
      max_error_rate: 0.05          # 最大錯誤率
  
  # 告警配置
  alerts:
    enabled: true
    channels: ["log", "console"]    # 告警通道
    conditions:
      - type: "high_error_rate"
        threshold: 0.1
        action: "fallback_to_traditional"
      - type: "low_confidence"
        threshold: 0.6
        action: "increase_reasoning_depth"
      - type: "api_timeout"
        threshold: 3
        action: "switch_to_fallback_model"

# ========== 調試和開發配置 ==========
development:
  # 調試模式
  debug:
    enabled: false
    log_ai_prompts: false           # 記錄 AI 提示
    log_ai_responses: false         # 記錄 AI 響應
    save_analysis_details: true     # 保存分析詳情
    verbose_logging: false          # 詳細日誌
  
  # 測試配置
  testing:
    mock_ai_responses: false        # 模擬 AI 響應（用於測試）
    test_pools_limit: 5             # 測試模式下的池子限制
    skip_expensive_calls: false     # 跳過昂貴的 API 調用
  
  # 實驗性功能
  experimental:
    multi_model_consensus: false    # 多模型共識（實驗性）
    adaptive_weights: false         # 自適應權重（實驗性）
    sentiment_analysis: false       # 情緒分析（實驗性）

# ========== 集成配置 ==========
integration:
  # 與其他 Agent 的集成
  agents:
    scout_bsc:
      data_sharing: true            # 共享 BSC 數據
      priority: "high"
    scout_meteora:
      data_sharing: true            # 共享 Solana 數據  
      priority: "high"
    risk_sentinel:
      alert_sharing: true           # 共享風險告警
      priority: "critical"
  
  # 外部服務集成
  external_services:
    price_feeds:
      enabled: true
      sources: ["coingecko", "coinmarketcap"]
    market_data:
      enabled: true
      providers: ["messari", "defilama"]
    
# ========== 安全配置 ==========
security:
  # API 密鑰管理
  api_keys:
    rotation_enabled: false         # API 密鑰輪換
    encryption_enabled: true        # 密鑰加密存儲
    
  # 輸入驗證
  input_validation:
    strict_mode: true              # 嚴格模式
    sanitize_prompts: true         # 清理提示內容
    max_prompt_length: 8000        # 最大提示長度
  
  # 輸出驗證
  output_validation:
    validate_scores: true          # 驗證評分範圍
    validate_structure: true       # 驗證輸出結構
    sanitize_responses: true       # 清理響應內容

# ========== 版本和兼容性 ==========
compatibility:
  min_agno_version: "0.4.0"        # 最低 Agno 版本要求
  max_agno_version: "1.0.0"        # 最高兼容 Agno 版本
  fallback_supported: true         # 支援降級模式
  
  # 數據格式版本
  data_format_version: "3.0"       # Dy-Flow v3 數據格式
  api_version: "v1"                # API 版本

# ========== 部署配置 ==========
deployment:
  environment: "development"       # development/staging/production
  
  # 資源限制
  resources:
    memory_limit_mb: 1024          # 內存限制
    cpu_limit_cores: 2             # CPU 核心限制
    disk_space_mb: 500             # 磁盤空間限制
  
  # 健康檢查
  health_check:
    enabled: true
    interval_seconds: 30           # 健康檢查間隔
    timeout_seconds: 10            # 健康檢查超時
    failure_threshold: 3           # 失敗閾值

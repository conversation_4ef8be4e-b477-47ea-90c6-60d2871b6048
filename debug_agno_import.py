#!/usr/bin/env python3
"""
Debug Agno Framework导入问题
"""

print("🔍 检查Agno Framework导入...")

# 检查基本导入
try:
    import agno
    print("✅ agno 基础包导入成功")
    print(f"   agno.__file__: {agno.__file__}")
    print(f"   agno.__version__: {getattr(agno, '__version__', 'unknown')}")
except ImportError as e:
    print(f"❌ agno 基础包导入失败: {e}")
    exit(1)

# 检查子模块
import pkgutil
print("\n📦 可用的子模块:")
for importer, modname, ispkg in pkgutil.iter_modules(agno.__path__):
    pkg_type = "package" if ispkg else "module"
    print(f"   {modname} ({pkg_type})")

# 尝试常见的导入模式
print("\n🧪 测试常见导入模式:")

# 模式1: agno.sdk.Tool
try:
    from agno.sdk import Tool
    print("✅ from agno.sdk import Tool - 成功")
except ImportError as e:
    print(f"❌ from agno.sdk import Tool - 失败: {e}")

# 模式2: agno.Tool
try:
    from agno import Tool
    print("✅ from agno import Tool - 成功")
except ImportError as e:
    print(f"❌ from agno import Tool - 失败: {e}")

# 模式3: agno.tools.Tool
try:
    from agno.tools import Tool
    print("✅ from agno.tools import Tool - 成功")
except ImportError as e:
    print(f"❌ from agno.tools import Tool - 失败: {e}")

# 模式4: agno.Agent
try:
    from agno import Agent
    print("✅ from agno import Agent - 成功")
except ImportError as e:
    print(f"❌ from agno import Agent - 失败: {e}")

print("\n🔍 详细检查agno包内容:")
try:
    import agno
    print("agno包的属性:")
    for attr in sorted(dir(agno)):
        if not attr.startswith('_'):
            try:
                obj = getattr(agno, attr)
                print(f"   {attr}: {type(obj).__name__}")
            except Exception as e:
                print(f"   {attr}: 访问失败 - {e}")
except Exception as e:
    print(f"检查失败: {e}")
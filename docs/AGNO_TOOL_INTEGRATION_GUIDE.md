# Agno Framework 工具集成指南

本指南详细介绍如何在 DyFlow 项目中集成和使用 Agno Framework 工具增强方案，实现 Agent 灵活调用工具、数据驱动决策和模块化开发。

---

## 1. 集成步骤

1. **核心文件引入**
   - `src/workflows/enhanced_lp_monitoring_workflow.py`
   - `src/agents/enhanced_planner_with_tools.py`
   - `examples/enhanced_workflow_usage.py`

2. **工具依赖配置**
   - 确保 `src/tools/` 目录下已实现 `PoolScannerTool`、`PoolScoringTool`、`SupabaseDbTool`、`BinanceHedgeTool`
   - 配置好链上数据源和数据库连接

3. **Agent 角色分离**
   - DataScout/DataAnalyst：负责数据采集与分析
   - PoolAnalyzer/StrategyPlanner：负责评分与策略规划
   - DataManager/RiskManager：负责结果存储与风险评估

---

## 2. 配置要求

- Python 3.8+
- 依赖包见 `requirements.txt`
- 数据库和链上节点配置详见 `config/` 目录
- 推荐使用虚拟环境隔离依赖

---

## 3. 性能优化建议

- 工具调用采用异步方式可提升并发性能
- 数据采集与评分建议批量处理，减少网络请求次数
- 结果存储可按需落库，避免频繁写入

---

## 4. 监控与调试

- 所有工具调用过程均有日志输出，便于追踪
- 可通过 `examples/enhanced_workflow_usage.py` 快速验证集成效果
- 推荐结合 VSCode 调试断点，逐步排查问题

---

## 5. 扩展性说明

- 新增工具：在 `src/tools/` 下实现并注册到对应 Agent
- 新增 Agent：继承基础 Agent 类，组合所需工具
- 工作流可按需拆分/组合，满足不同业务场景
- 支持同步与异步两种调用方式

---

## 6. 常见问题

- 工具未生效：检查依赖和配置，确保工具类已正确导入
- 数据异常：优先排查链上节点和数据库连接
- 性能瓶颈：建议开启异步，优化批量处理逻辑

---

如需进一步支持，请参考项目 README 或联系开发团队。

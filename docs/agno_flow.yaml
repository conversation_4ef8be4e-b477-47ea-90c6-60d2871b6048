# Dy-Flow v3 Agno DAG 配置
# 實現 Scout-Scorer-Planner-Executor-Auditor 架構

dag:
  name: "dyflow_v3_dag"
  description: "Dy-Flow v3 多鏈自動做市系統 DAG"
  version: "3.0.0"
  max_active_runs: 1
  catchup: false

# v3 Agent 調度配置
agents:
  # === 數據收集層 ===
  scout_bsc:
    name: "BSC Scout"
    description: "BSC PancakeSwap v3 池子數據收集"
    module: "src.agents.scout.bsc"
    class: "ScoutBSC"
    schedule:
      cron: "*/5 * * * *"  # 每 5 分鐘
    priority: 1
    dependencies: []
    output_type: "pools_raw"
    timeout: 120
    retry_attempts: 3
    
  scout_meteora:
    name: "Meteora Scout"
    description: "Solana Meteora DLMM 池子數據收集"
    module: "src.agents.scout.meteora"
    class: "ScoutMeteora"
    schedule:
      cron: "*/1 * * * *"  # 每 1 分鐘
    priority: 1
    dependencies: []
    output_type: "pools_raw"
    timeout: 60
    retry_attempts: 3

  # === 評分決策層 ===
  scorer:
    name: "Pool Scorer (Legacy)"
    description: "四因子池子評分系統 (原版)"
    module: "src.agents.scorer"
    class: "ScorerAgent"
    priority: 2
    dependencies: []
    needs: ["pools_raw"]  # 需要任一 Scout 輸出
    output_type: "pools_scored"
    timeout: 90
    retry_attempts: 2
    enabled: false  # 默認禁用，用於A/B測試
    
  scorer_v2:
    name: "Pool Scorer V2 (Enhanced)"
    description: "6因子動態評分系統 - 增強版"
    module: "src.agents.scorer_v2"
    class: "ScorerV2Agent"
    priority: 2
    dependencies: []
    needs: ["pools_raw"]  # 需要任一 Scout 輸出
    output_type: "pools_scored"
    timeout: 120  # 稍長一些，因為有更複雜的分析
    retry_attempts: 2
    enabled: true  # 默認啟用
    config_file: "config/scorer_v2.yaml"
    features:
      - "dynamic_weights"
      - "6_factor_scoring"
      - "liquidity_depth_analysis"
      - "volatility_assessment"
      - "enhanced_token_analysis"
      - "risk_adjustment"
      - "market_adaptation"
    performance:
      cache_enabled: true
      batch_processing: true
      max_concurrent: 10
    
  risk_sentinel:
    name: "Risk Sentinel"
    description: "ATR + kDrop 風險監控"
    module: "src.agents.risk_sentinel"
    class: "RiskSentinelAgent"
    schedule:
      cron: "*/1 * * * *"  # 每 1 分鐘
    priority: 2
    dependencies: []
    output_type: "risk_alerts"
    timeout: 30
    retry_attempts: 1
    
  planner:
    name: "Strategy Planner"
    description: "三大策略規劃系統"
    module: "src.agents.planner"
    class: "PlannerAgent"
    priority: 3
    dependencies: []
    needs: ["pools_scored", "risk_alerts"]
    output_type: "plans"
    timeout: 120
    retry_attempts: 2

  # === 執行層 ===
  planner_llm:
    name: "Planner LLM"
    description: "Handles exceptional plans using LLM. Module/class are placeholders."
    module: "src.agents.planner_llm"
    class: "PlannerLLMAgent"
    priority: 4
    dependencies: []
    needs: ["plans"]
    output_type: "plans_fixed"
    timeout: 180
    retry_attempts: 1
    
  executor:
    name: "Trade Executor"
    description: "區塊鏈交易執行與對沖"
    module: "src.agents.executor"
    class: "ExecutorAgent"
    priority: 5
    dependencies: []
    needs: ["plans", "plans_fixed"]
    output_type: "execution_results"
    timeout: 300
    retry_attempts: 1

  # === 審計監控層 ===
  auditor:
    name: "Earnings Auditor"
    description: "收益審計與分析"
    module: "src.agents.auditor"
    class: "EarningsAuditorAgent"
    schedule:
      cron: "0 */6 * * *"  # 每 6 小時
    priority: 6
    dependencies: []
    needs: ["execution_results"]
    output_type: "audit_reports"
    timeout: 120
    retry_attempts: 2
    
  cli_reporter:
    name: "CLI Reporter"
    description: "實時狀態報告"
    module: "src.agents.cli_reporter"
    class: "CLIReporterAgent"
    schedule:
      cron: "*/10 * * * * *"  # 每 10 秒
    priority: 7
    dependencies: []
    needs: ["pools_scored", "plans", "plans_fixed", "execution_results"]
    output_type: "reports"
    timeout: 15
    retry_attempts: 1

# v3 執行策略
execution:
  # 資料流執行策略
  data_flow_execution: true
  
  # 並行執行規則
  parallel_groups:
    - ["scout_bsc", "scout_meteora", "risk_sentinel"]  # 數據收集並行
    - ["scorer"]  # 評分串行
    - ["planner"]  # 規劃串行
    - ["planner_llm"] # 新增 LLM 規劃器
    - ["executor"]  # 執行串行
    - ["auditor", "cli_reporter"]  # 審計報告並行
    
  # 錯誤處理策略
  failure_strategy: "continue_on_non_critical"
  critical_agents: ["executor", "risk_sentinel"]
  
  # 資料流觸發
  trigger_on_data_change: true
  min_trigger_interval: 10  # 最小觸發間隔（秒）

# v3 監控配置
monitoring:
  enable_metrics: true
  enable_agent_timing: true
  enable_data_flow_tracking: true
  
  # 性能監控
  performance_tracking:
    enabled: true
    track_execution_time: true
    track_memory_usage: true
    track_data_size: true
    
  # 告警配置
  alerts:
    critical_failure_timeout: 300  # 5分鐘無回應告警
    execution_failure_threshold: 3  # 連續失敗3次告警
    data_staleness_threshold: 600  # 數據超過10分鐘告警

# v3 數據管理
data_management:
  # 數據保留策略
  retention_policy:
    pools_raw: "24h"
    pools_scored: "7d"
    plans: "30d"
    execution_results: "90d"
    audit_reports: "1y"
    
  # 數據清理策略
  cleanup_schedule: "0 2 * * *"  # 每日 2AM 清理
  
  # 數據壓縮
  compression:
    enabled: true
    threshold_age: "7d"

# v3 緊急處理
emergency_protocols:
  # 緊急停止條件
  emergency_stop_conditions:
    - "risk_sentinel.critical_risk_detected"
    - "executor.consecutive_failures >= 5"
    - "system.memory_usage > 90%"
    
  # 緊急動作
  emergency_actions:
    - "stop_all_trading"
    - "close_all_positions"
    - "send_critical_alert"
    
  # 恢復檢查
  recovery_checks:
    interval: 60  # 每分鐘檢查
    conditions:
      - "risk_levels_normal"
      - "system_resources_available"
      - "blockchain_connectivity_ok"

# v3 配置驗證
validation:
  # Agent 依賴驗證
  validate_dependencies: true
  validate_data_flow: true
  validate_schedule_conflicts: true
  
  # 運行時驗證
  runtime_validation:
    enabled: true
    check_interval: 300  # 5分鐘
    
# 日誌配置
logging:
  level: "INFO"
  format: "json"
  agent_specific_logs: true
  data_flow_logs: true
  performance_logs: true
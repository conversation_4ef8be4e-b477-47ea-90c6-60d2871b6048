# Dy-Flow v3 完整 Agno DAG 配置
# 基於 Agno Framework 的企業級 Agentic 架構

name: "dyflow_v3_agentic"
version: "3.0.0"
description: "24/7 自動做市+動態避險的多鏈錢包管家，基於 Agno Framework"

# 全局配置
globals:
  timezone: "UTC"
  log_level: "INFO"
  max_retries: 3
  timeout_seconds: 300
  
# Agent 節點定義
agents:

  # ========== Data Layer ==========
  scout_bsc:
    type: "tool"
    class: "src.agents.scout_bsc_agno.ScoutBSCAgent"
    schedule: "*/5 * * * *"  # 每 5 分鐘
    description: "BSC PancakeSwap V3 池數據收集"
    config:
      min_tvl_usd: 100000
      max_pools: 50
      schedule_interval: 300
    outputs:
      - "pools_raw_bsc"
    dependencies: []
    
  scout_meteora:
    type: "tool" 
    class: "src.agents.scout_meteora_agno.ScoutMeteoraAgent"
    schedule: "* * * * *"  # 每 1 分鐘
    description: "Solana Meteora 池數據收集"
    config:
      min_tvl_usd: 50000
      max_pools: 30
      schedule_interval: 60
    outputs:
      - "pools_raw_sol"
    dependencies: []

  # ========== Decision Layer ==========
  scorer:
    type: "tool"
    class: "src.agents.scorer_agno.ScorerAgent"
    schedule: "needs"
    description: "多因子池評分和排名"
    config:
      factor_weights:
        tvl_score: 0.3
        fee_score: 0.25
        volume_score: 0.2
        liquidity_score: 0.15
        risk_score: 0.1
      min_score: 60.0
      max_pools_ranked: 20
    inputs:
      - "pools_raw_bsc"
      - "pools_raw_sol" 
    outputs:
      - "pools_scored"
    dependencies:
      - "scout_bsc"
      - "scout_meteora"
      
  risk_sentinel:
    type: "tool"
    class: "src.agents.risk_sentinel_agno.RiskSentinelAgent"
    schedule: "* * * * *"  # 每 1 分鐘
    description: "實時風險監控和警報"
    config:
      k_drop: 2.5
      min_drop_pct: 0.06
      tvl_drop_threshold: 0.6
      hedge_fail_threshold: 3
      delta_risk_threshold: 0.3
      schedule_interval: 60
    outputs:
      - "risk_alerts"
    dependencies: []

  planner:
    type: "tool"
    class: "src.agents.planner_agno.PlannerAgent"
    schedule: "needs"
    description: "規則決策和計劃生成"
    config:
      strategies:
        - "ladder_single_sided"
        - "delta_neutral_lp" 
        - "passive_high_tvl"
      max_plans: 10
      risk_threshold: 0.8
    inputs:
      - "pools_scored"
      - "risk_alerts"
    outputs:
      - "plans"
    dependencies:
      - "scorer"
      - "risk_sentinel"

  planner_llm:
    type: "llm"
    class: "src.agents.planner_llm_agno.PlannerLLMAgent"
    schedule: "needs"
    description: "LLM 例外處理和複雜決策"
    config:
      model: "gpt-4o"
      temperature: 0.2
      max_tokens: 2000
    inputs:
      - "plans?needs_llm"
    outputs:
      - "plans_fixed"
    dependencies:
      - "planner"
    condition: "plans.needs_llm == true"

  # ========== Execution Layer ==========
  executor:
    type: "tool"
    class: "src.agents.executor_agno.ExecutorAgent"
    schedule: "needs"
    description: "LP 和對沖操作執行"
    config:
      max_concurrent_operations: 3
      gas_price_multiplier: 1.2
      slippage_tolerance: 0.005
    inputs:
      - "plans"
      - "plans_fixed"
    outputs:
      - "tx_receipts"
    dependencies:
      - "planner"
      - "planner_llm"

  lp_manager:
    type: "tool"
    class: "src.agents.lp_manager_agno.LPManagerAgent"
    schedule: "needs"
    description: "流動性倉位管理"
    config:
      position_size_limits:
        min_usd: 1000
        max_usd: 50000
      rebalance_threshold: 0.1
    inputs:
      - "tx_receipts"
    outputs:
      - "lp_positions"
    dependencies:
      - "executor"

  hedge_manager:
    type: "tool"
    class: "src.agents.hedge_manager_agno.HedgeManagerAgent" 
    schedule: "needs"
    description: "Delta 對沖管理"
    config:
      hedge_ratio: 1.0
      delta_tolerance: 0.05
      rehedge_threshold: 0.1
    inputs:
      - "lp_positions"
    outputs:
      - "hedge_positions"
    dependencies:
      - "lp_manager"

  # ========== Audit & Reporting ==========
  auditor:
    type: "tool"
    class: "src.agents.auditor_agno.AuditorAgent"
    schedule: "0 */6 * * *"  # 每 6 小時
    description: "收益審計和績效分析"
    config:
      audit_period_hours: 6
      performance_metrics:
        - "total_pnl"
        - "fee_earned"
        - "gas_cost"
        - "sharpe_ratio"
    inputs:
      - "lp_positions"
      - "hedge_positions"
      - "tx_receipts"
    outputs:
      - "daily_report"
    dependencies:
      - "lp_manager"
      - "hedge_manager"
      - "executor"

  cli_reporter:
    type: "tool"
    class: "src.agents.cli_reporter_agno.CLIReporterAgent"
    schedule: "*/10 * * * * *"  # 每 10 秒
    description: "實時 CLI 狀態顯示"
    config:
      display_mode: "table"
      refresh_interval: 10
      max_items: 20
    inputs:
      - "pools_scored"
      - "risk_alerts"
      - "daily_report"
    outputs: []
    dependencies:
      - "scorer"
      - "risk_sentinel"
      - "auditor"

  # ========== AI Enhanced Agents ==========
  wallet_analyzer:
    type: "tool"
    class: "src.agents.wallet_analyzer_agno.WalletAnalyzerAgent"
    schedule: "0 */1 * * *"  # 每小時
    description: "AI 增強錢包分析"
    config:
      target_wallets:
        - "******************************************"
      analysis_depth: "deep"
    outputs:
      - "wallet_insights"
    dependencies: []

  market_analyst:
    type: "llm"
    class: "src.agents.market_analyst_agno.MarketAnalystAgent"
    schedule: "0 */4 * * *"  # 每 4 小時
    description: "AI 市場分析和趨勢預測"
    config:
      model: "gpt-4o"
      analysis_window: "24h"
      sources:
        - "pools_scored"
        - "risk_alerts" 
        - "wallet_insights"
    inputs:
      - "pools_scored"
      - "risk_alerts"
      - "wallet_insights"
    outputs:
      - "market_analysis"
    dependencies:
      - "scorer"
      - "risk_sentinel"
      - "wallet_analyzer"

# 數據流配置
data_flows:
  - from: "scout_bsc"
    to: "scorer"
    data: "pools_raw_bsc"
    
  - from: "scout_meteora"
    to: "scorer"
    data: "pools_raw_sol"
    
  - from: "scorer"
    to: "planner"
    data: "pools_scored"
    
  - from: "risk_sentinel"
    to: "planner"
    data: "risk_alerts"
    
  - from: "planner"
    to: "planner_llm"
    data: "plans?needs_llm"
    
  - from: "planner"
    to: "executor"
    data: "plans"
    
  - from: "planner_llm"
    to: "executor"
    data: "plans_fixed"
    
  - from: "executor"
    to: "lp_manager"
    data: "tx_receipts"
    
  - from: "lp_manager"
    to: "hedge_manager"
    data: "lp_positions"
    
  - from: "lp_manager"
    to: "auditor"
    data: "lp_positions"
    
  - from: "hedge_manager"
    to: "auditor"
    data: "hedge_positions"
    
  - from: "executor"
    to: "auditor"
    data: "tx_receipts"

# 監控配置
monitoring:
  health_checks:
    interval: 60  # 秒
    timeout: 30
    
  metrics:
    - name: "agent_execution_time"
      type: "histogram"
      
    - name: "agent_success_rate"
      type: "gauge"
      
    - name: "pools_processed"
      type: "counter"
      
    - name: "alerts_generated"
      type: "counter"
      
    - name: "total_pnl"
      type: "gauge"

  alerts:
    critical_failure:
      threshold: 3
      window: "5m"
      
    performance_degradation:
      threshold: 0.8
      metric: "agent_success_rate"

# 錯誤處理
error_handling:
  max_retries: 3
  retry_delay: "exponential"
  fallback_mode: true
  
  critical_agents:
    - "risk_sentinel"
    - "executor"
    
  non_critical_agents:
    - "cli_reporter"
    - "wallet_analyzer"

# 資源限制
resources:
  memory_limit: "2Gi"
  cpu_limit: "1000m"
  disk_limit: "10Gi"
  
  agent_limits:
    scout_bsc:
      memory: "256Mi"
      cpu: "200m"
      
    scout_meteora:
      memory: "256Mi" 
      cpu: "200m"
      
    scorer:
      memory: "512Mi"
      cpu: "300m"
      
    risk_sentinel:
      memory: "256Mi"
      cpu: "200m"
      
    planner:
      memory: "512Mi"
      cpu: "300m"
      
    planner_llm:
      memory: "1Gi"
      cpu: "500m"
      
    executor:
      memory: "512Mi"
      cpu: "400m"

# 安全配置
security:
  encryption:
    enabled: true
    algorithm: "AES-256"
    
  api_keys:
    rotation_interval: "30d"
    backup_count: 3
    
  network:
    allowed_hosts:
      - "api.pancakeswap.info"
      - "api.mainnet-beta.solana.com"
      - "api.openai.com"
      
    rate_limits:
      default: "100/min"
      openai: "50/min"

# 部署配置
deployment:
  environment: "production"
  replicas: 1
  
  rollout:
    strategy: "RollingUpdate"
    max_unavailable: 0
    max_surge: 1
    
  persistence:
    enabled: true
    storage_class: "ssd"
    size: "50Gi"
# ScorerV2 生产部署指南

## 📋 部署准备清单

### ✅ 已完成的准备工作

#### 1. **核心开发**
- [x] ScorerV2Agent 完整实现 (`src/agents/scorer_v2.py`)
- [x] 6因子动态评分算法
- [x] 动态权重调整系统
- [x] 增强的风险评估机制
- [x] 配置驱动架构

#### 2. **测试验证**
- [x] 单元测试通过 (基础功能)
- [x] A/B测试框架完成 (`tests/scorer_ab_test.py`)
- [x] 集成测试通过 (`tests/integration_scorer_v2_test.py`)
- [x] 性能基准测试 (87.5%成功率，91.7平均分)
- [x] 错误处理验证

#### 3. **配置系统**
- [x] 完整配置文件 (`config/scorer_v2.yaml`)
- [x] 动态权重配置
- [x] 风险参数调优
- [x] 性能优化设置

#### 4. **集成准备**
- [x] Agno DAG 配置更新 (`docs/agno_flow.yaml`)
- [x] 向后兼容性确保
- [x] 数据流验证
- [x] 依赖关系确认

### 🚀 生产部署步骤

#### 第一阶段：灰度发布 (建议)

```yaml
# 灰度配置示例
scorer_deployment:
  strategy: "canary"
  canary_percentage: 20  # 20%流量使用V2
  fallback_enabled: true
  monitoring_period: "7d"
```

**步骤：**
1. 部署ScorerV2但保持禁用状态
2. 配置20%的池子使用V2评分
3. 监控7天性能指标
4. 对比V1和V2的评分效果
5. 根据结果决定是否全量切换

#### 第二阶段：全量部署

```yaml
# 生产配置
scorer_v2:
  enabled: true
  primary_scorer: true
  fallback_to_v1: true  # 紧急情况下回退
```

### 🔧 环境配置

#### 生产环境依赖

```bash
# Python 依赖
structlog>=23.1.0
asyncio
dataclasses
typing-extensions

# 系统依赖
redis>=5.0  # 缓存支持
postgresql>=13  # 数据存储
```

#### 环境变量

```bash
# ScorerV2 配置
SCORER_V2_ENABLED=true
SCORER_V2_CONFIG_PATH="/app/config/scorer_v2.yaml"
SCORER_V2_CACHE_TTL=300
SCORER_V2_MAX_CONCURRENT=10

# Redis 缓存
REDIS_URL="redis://localhost:6379/1"
REDIS_POOL_SIZE=10

# 监控配置
SCORER_V2_METRICS_ENABLED=true
SCORER_V2_DETAILED_LOGGING=true
```

### 📊 监控指标

#### 关键性能指标 (KPIs)

```yaml
monitoring:
  core_metrics:
    - "scorer_v2_execution_time"
    - "scorer_v2_success_rate" 
    - "scorer_v2_pool_throughput"
    - "scorer_v2_cache_hit_rate"
    - "scorer_v2_error_rate"
  
  business_metrics:
    - "average_pool_score"
    - "high_quality_pool_ratio"
    - "risk_filter_effectiveness"
    - "hedgeable_pool_percentage"
  
  alerts:
    - metric: "scorer_v2_success_rate"
      threshold: 0.95
      severity: "critical"
    - metric: "scorer_v2_execution_time"
      threshold: 2000  # 2s
      severity: "warning"
```

#### 仪表板配置

**Grafana 面板建议：**
1. **性能面板**：执行时间、吞吐量、成功率
2. **质量面板**：平均评分、过滤率、准确性
3. **比较面板**：V1 vs V2 对比图表
4. **错误面板**：错误类型分布、失败池子统计

### 🛡️ 风险控制

#### 回滚策略

```yaml
rollback_conditions:
  automatic:
    - success_rate < 0.90
    - average_execution_time > 3000ms
    - error_rate > 0.05
  
  manual:
    - business_metric_degradation
    - user_reported_issues
    - performance_impact_on_other_systems
```

#### 熔断机制

```python
# 熔断器配置
circuit_breaker:
  failure_threshold: 5      # 连续5次失败触发熔断
  recovery_timeout: 60      # 60秒后尝试恢复
  half_open_max_calls: 3    # 半开状态最多3次尝试
```

### 🔄 数据迁移

#### 评分数据迁移

```sql
-- 创建V2评分结果表
CREATE TABLE pool_scores_v2 (
    id VARCHAR(255) PRIMARY KEY,
    score DECIMAL(5,2) NOT NULL,
    hedgeable BOOLEAN NOT NULL,
    factor_scores JSONB,
    risk_adjustment DECIMAL(4,3),
    scorer_version VARCHAR(10) DEFAULT 'v2',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 索引优化
CREATE INDEX idx_pool_scores_v2_score ON pool_scores_v2(score DESC);
CREATE INDEX idx_pool_scores_v2_hedgeable ON pool_scores_v2(hedgeable);
CREATE INDEX idx_pool_scores_v2_created_at ON pool_scores_v2(created_at);
```

### 📈 性能优化

#### 缓存策略

```yaml
cache_config:
  pool_details:
    ttl: 300  # 5分钟
    max_size: 1000
  
  market_conditions:
    ttl: 60   # 1分钟
    max_size: 10
  
  dynamic_weights:
    ttl: 300  # 5分钟
    max_size: 50
```

#### 并发优化

```python
# 异步批量处理配置
async_config = {
    "max_concurrent_pools": 10,
    "batch_size": 50,
    "timeout_per_pool": 2.0,
    "retry_attempts": 2
}
```

### 🧪 验收测试

#### 生产验收标准

```yaml
acceptance_criteria:
  performance:
    - execution_time_p95 < 2000ms
    - success_rate > 0.95
    - throughput >= 100 pools/minute
  
  quality:
    - average_score_deviation < 5%  # 与V1相比
    - risk_filter_accuracy > 0.90
    - hedgeable_prediction_accuracy > 0.85
  
  stability:
    - zero_downtime_deployment: true
    - fallback_mechanism_tested: true
    - error_recovery_verified: true
```

### 🔧 故障排除

#### 常见问题解决

```bash
# 1. 检查ScorerV2状态
curl -X GET "http://localhost:8080/health/scorer_v2"

# 2. 查看详细日志
docker logs -f dyflow_scorer_v2 --tail 100

# 3. 检查配置加载
python -c "
from src.agents.scorer_v2 import ScorerV2Agent
print('ScorerV2 配置验证通过')
"

# 4. 缓存清理
redis-cli FLUSHDB 1
```

#### 性能调优

```yaml
# 高负载优化配置
high_load_config:
  cache_ttl: 600          # 延长缓存时间
  max_concurrent: 20      # 增加并发数
  batch_processing: true  # 启用批量处理
  async_timeout: 5.0      # 增加超时时间
```

### 📋 部署检查清单

#### 部署前检查
- [ ] 代码审查完成
- [ ] 所有测试通过
- [ ] 配置文件验证
- [ ] 依赖项安装确认
- [ ] 数据库迁移脚本准备
- [ ] 监控仪表板配置
- [ ] 回滚方案准备

#### 部署中检查
- [ ] 服务健康检查通过
- [ ] 配置加载成功
- [ ] 数据库连接正常
- [ ] 缓存服务可用
- [ ] 基础功能验证

#### 部署后验证
- [ ] 端到端测试通过
- [ ] 性能指标正常
- [ ] 错误率在预期范围
- [ ] 用户体验无异常
- [ ] 监控告警正常

### 🎯 成功指标

#### 30天后评估标准

```yaml
success_metrics:
  technical:
    - uptime: ">99.9%"
    - avg_response_time: "<1.5s"
    - error_rate: "<0.01"
  
  business:
    - scoring_accuracy: ">90%"
    - risk_mitigation: "+30%"
    - operational_efficiency: "+25%"
  
  user_satisfaction:
    - system_reliability: ">95%"
    - feature_adoption: ">80%"
    - user_feedback_score: ">4.5/5"
```

---

## 📞 支持联系

**技术支持团队：**
- 开发负责人：Dy-Flow 开发团队
- 运维支持：系统运维团队
- 紧急联系：24/7 技术热线

**文档更新：**
- 最后更新：2025-06-03
- 版本：v1.0
- 状态：生产就绪

---

*这个部署指南将随着生产环境的反馈持续更新和改进。*
-- Dy-Flow v3 Supabase 數據庫 Schema
-- 支援多鏈自動做市系統的完整數據結構

-- 啟用必要的擴展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- === 1. 原始池子數據表 ===
CREATE TABLE IF NOT EXISTS pools_raw (
    id VARCHAR(255) PRIMARY KEY,
    chain VARCHAR(20) NOT NULL,
    dex VARCHAR(50) NOT NULL,
    token0_address VARCHAR(255) NOT NULL,
    token0_symbol VARCHAR(50) NOT NULL,
    token0_decimals INTEGER NOT NULL DEFAULT 18,
    token1_address VARCHAR(255) NOT NULL,
    token1_symbol VARCHAR(50) NOT NULL,
    token1_decimals INTEGER NOT NULL DEFAULT 18,
    fee_tier INTEGER NOT NULL,
    tvl_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    volume24h_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    fee24h_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    fee_tvl DECIMAL(10,6) NOT NULL DEFAULT 0,
    price_token0 DECIMAL(30,10) NOT NULL DEFAULT 0,
    price_token1 DECIMAL(30,10) NOT NULL DEFAULT 0,
    liquidity_token0 DECIMAL(30,10) NOT NULL DEFAULT 0,
    liquidity_token1 DECIMAL(30,10) NOT NULL DEFAULT 0,
    tick_current INTEGER,
    tick_lower INTEGER,
    tick_upper INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_pools_raw_chain ON pools_raw(chain);
CREATE INDEX IF NOT EXISTS idx_pools_raw_tvl ON pools_raw(tvl_usd DESC);
CREATE INDEX IF NOT EXISTS idx_pools_raw_fee_tvl ON pools_raw(fee_tvl DESC);
CREATE INDEX IF NOT EXISTS idx_pools_raw_created_at ON pools_raw(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_pools_raw_tokens ON pools_raw(token0_symbol, token1_symbol);

-- === 2. 評分池子數據表 ===
CREATE TABLE IF NOT EXISTS pools_scored (
    id VARCHAR(255) PRIMARY KEY,
    pool_id VARCHAR(255) NOT NULL REFERENCES pools_raw(id),
    score DECIMAL(5,4) NOT NULL DEFAULT 0,
    score_breakdown JSONB NOT NULL DEFAULT '{}',
    hedgeable BOOLEAN NOT NULL DEFAULT FALSE,
    hedge_tokens TEXT[] DEFAULT '{}',
    risk_level VARCHAR(20) DEFAULT 'medium',
    strategy_recommendation VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_pools_scored_score ON pools_scored(score DESC);
CREATE INDEX IF NOT EXISTS idx_pools_scored_hedgeable ON pools_scored(hedgeable);
CREATE INDEX IF NOT EXISTS idx_pools_scored_strategy ON pools_scored(strategy_recommendation);
CREATE INDEX IF NOT EXISTS idx_pools_scored_created_at ON pools_scored(created_at DESC);

-- === 3. 策略計劃表 ===
CREATE TABLE IF NOT EXISTS plans (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pool_id VARCHAR(255) NOT NULL,
    strategy VARCHAR(50) NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('enter', 'exit', 'rebalance', 'hedge')),
    params JSONB NOT NULL DEFAULT '{}',
    priority INTEGER NOT NULL DEFAULT 0,
    needs_llm BOOLEAN NOT NULL DEFAULT FALSE,
    llm_reason TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed', 'failed', 'cancelled')),
    estimated_gas DECIMAL(15,0),
    estimated_slippage DECIMAL(5,4),
    expected_return DECIMAL(10,6),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    executed_at TIMESTAMP WITH TIME ZONE
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_plans_status ON plans(status);
CREATE INDEX IF NOT EXISTS idx_plans_strategy ON plans(strategy);
CREATE INDEX IF NOT EXISTS idx_plans_pool_id ON plans(pool_id);
CREATE INDEX IF NOT EXISTS idx_plans_priority ON plans(priority DESC);
CREATE INDEX IF NOT EXISTS idx_plans_created_at ON plans(created_at DESC);

-- === 4. 頭寸管理表 ===
CREATE TABLE IF NOT EXISTS positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pool_id VARCHAR(255) NOT NULL,
    chain VARCHAR(20) NOT NULL,
    strategy VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'closed', 'liquidated', 'error')),
    
    -- LP 頭寸信息
    lp_token_id BIGINT,
    lp_amount DECIMAL(30,10) NOT NULL DEFAULT 0,
    lp_value_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    tick_lower INTEGER,
    tick_upper INTEGER,
    
    -- Token 分佈
    token0_amount DECIMAL(30,10) NOT NULL DEFAULT 0,
    token1_amount DECIMAL(30,10) NOT NULL DEFAULT 0,
    token0_value_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    token1_value_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    
    -- 對沖信息
    hedge_enabled BOOLEAN NOT NULL DEFAULT FALSE,
    hedge_ratio DECIMAL(5,4) DEFAULT 0,
    hedge_positions JSONB DEFAULT '[]',
    
    -- PnL 追蹤
    entry_price_token0 DECIMAL(30,10),
    entry_price_token1 DECIMAL(30,10),
    current_price_token0 DECIMAL(30,10),
    current_price_token1 DECIMAL(30,10),
    unrealized_pnl_usd DECIMAL(20,2) DEFAULT 0,
    realized_pnl_usd DECIMAL(20,2) DEFAULT 0,
    fees_earned_usd DECIMAL(20,2) DEFAULT 0,
    gas_spent_usd DECIMAL(20,2) DEFAULT 0,
    
    -- 時間追蹤
    opened_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    last_rebalance_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status);
CREATE INDEX IF NOT EXISTS idx_positions_chain ON positions(chain);
CREATE INDEX IF NOT EXISTS idx_positions_strategy ON positions(strategy);
CREATE INDEX IF NOT EXISTS idx_positions_pool_id ON positions(pool_id);
CREATE INDEX IF NOT EXISTS idx_positions_opened_at ON positions(opened_at DESC);

-- === 5. 對沖頭寸表 ===
CREATE TABLE IF NOT EXISTS hedges (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    position_id UUID NOT NULL REFERENCES positions(id) ON DELETE CASCADE,
    exchange VARCHAR(50) NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    side VARCHAR(10) NOT NULL CHECK (side IN ('long', 'short')),
    size DECIMAL(30,10) NOT NULL,
    entry_price DECIMAL(30,10) NOT NULL,
    current_price DECIMAL(30,10),
    leverage DECIMAL(5,2) NOT NULL DEFAULT 1.0,
    margin_usd DECIMAL(20,2) NOT NULL,
    unrealized_pnl_usd DECIMAL(20,2) DEFAULT 0,
    funding_fees_usd DECIMAL(20,2) DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'closed', 'liquidated', 'error')),
    external_order_id VARCHAR(255),
    opened_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_hedges_position_id ON hedges(position_id);
CREATE INDEX IF NOT EXISTS idx_hedges_status ON hedges(status);
CREATE INDEX IF NOT EXISTS idx_hedges_exchange ON hedges(exchange);
CREATE INDEX IF NOT EXISTS idx_hedges_symbol ON hedges(symbol);

-- === 6. 交易記錄表 ===
CREATE TABLE IF NOT EXISTS txs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    plan_id UUID REFERENCES plans(id),
    position_id UUID REFERENCES positions(id),
    chain VARCHAR(20) NOT NULL,
    tx_hash VARCHAR(255) UNIQUE,
    tx_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'failed', 'reverted')),
    
    -- Transaction 詳情
    from_address VARCHAR(255),
    to_address VARCHAR(255),
    value_eth DECIMAL(30,18) DEFAULT 0,
    gas_limit BIGINT,
    gas_used BIGINT,
    gas_price DECIMAL(30,0),
    gas_cost_usd DECIMAL(10,2),
    
    -- 操作詳情
    action VARCHAR(20) NOT NULL,
    token_amounts JSONB DEFAULT '{}',
    slippage_actual DECIMAL(5,4),
    price_impact DECIMAL(5,4),
    
    -- 時間追蹤
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    block_number BIGINT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_txs_hash ON txs(tx_hash);
CREATE INDEX IF NOT EXISTS idx_txs_status ON txs(status);
CREATE INDEX IF NOT EXISTS idx_txs_chain ON txs(chain);
CREATE INDEX IF NOT EXISTS idx_txs_position_id ON txs(position_id);
CREATE INDEX IF NOT EXISTS idx_txs_submitted_at ON txs(submitted_at DESC);

-- === 7. 收益審計表 ===
CREATE TABLE IF NOT EXISTS earnings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    position_id UUID NOT NULL REFERENCES positions(id),
    reporting_date DATE NOT NULL,
    
    -- 收益明細
    lp_fees_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    trading_pnl_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    hedge_pnl_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    funding_fees_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    gas_costs_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    net_pnl_usd DECIMAL(20,2) NOT NULL DEFAULT 0,
    
    -- 績效指標
    roi_percent DECIMAL(8,4) DEFAULT 0,
    apr_percent DECIMAL(8,4) DEFAULT 0,
    sharpe_ratio DECIMAL(8,4) DEFAULT 0,
    max_drawdown_percent DECIMAL(8,4) DEFAULT 0,
    
    -- 統計數據
    total_volume_usd DECIMAL(20,2) DEFAULT 0,
    trade_count INTEGER DEFAULT 0,
    win_rate DECIMAL(5,4) DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(position_id, reporting_date)
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_earnings_position_id ON earnings(position_id);
CREATE INDEX IF NOT EXISTS idx_earnings_date ON earnings(reporting_date DESC);
CREATE INDEX IF NOT EXISTS idx_earnings_net_pnl ON earnings(net_pnl_usd DESC);
CREATE INDEX IF NOT EXISTS idx_earnings_roi ON earnings(roi_percent DESC);

-- === 8. 風險事件表 ===
CREATE TABLE IF NOT EXISTS risk_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pool_id VARCHAR(255),
    position_id UUID REFERENCES positions(id),
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    
    -- 事件詳情
    description TEXT NOT NULL,
    risk_factors JSONB DEFAULT '{}',
    current_values JSONB DEFAULT '{}',
    threshold_values JSONB DEFAULT '{}',
    
    -- 響應狀態
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'ignored')),
    action_taken VARCHAR(100),
    resolution_notes TEXT,
    
    -- 時間追蹤
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_risk_events_severity ON risk_events(severity);
CREATE INDEX IF NOT EXISTS idx_risk_events_status ON risk_events(status);
CREATE INDEX IF NOT EXISTS idx_risk_events_type ON risk_events(event_type);
CREATE INDEX IF NOT EXISTS idx_risk_events_detected_at ON risk_events(detected_at DESC);
CREATE INDEX IF NOT EXISTS idx_risk_events_pool_id ON risk_events(pool_id);

-- === 9. 用戶過濾配置表 ===
CREATE TABLE IF NOT EXISTS user_filters (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    filter_name VARCHAR(100) NOT NULL,
    filter_type VARCHAR(50) NOT NULL,
    
    -- 過濾條件
    conditions JSONB NOT NULL DEFAULT '{}',
    
    -- 狀態管理
    enabled BOOLEAN NOT NULL DEFAULT TRUE,
    priority INTEGER NOT NULL DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(user_id, filter_name)
);

-- 索引優化
CREATE INDEX IF NOT EXISTS idx_user_filters_user_id ON user_filters(user_id);
CREATE INDEX IF NOT EXISTS idx_user_filters_type ON user_filters(filter_type);
CREATE INDEX IF NOT EXISTS idx_user_filters_enabled ON user_filters(enabled);

-- === 10. 系統配置表 ===
CREATE TABLE IF NOT EXISTS system_config (
    key VARCHAR(100) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(50),
    updated_by VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- === 觸發器：自動更新 updated_at ===
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 為所有表添加自動更新觸發器
DO $$
DECLARE
    t text;
BEGIN
    FOR t IN 
        SELECT table_name 
        FROM information_schema.columns 
        WHERE column_name = 'updated_at' 
        AND table_schema = 'public'
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS update_%I_updated_at ON %I', t, t);
        EXECUTE format('CREATE TRIGGER update_%I_updated_at 
                       BEFORE UPDATE ON %I 
                       FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()', t, t);
    END LOOP;
END;
$$;

-- === Row Level Security (RLS) 配置 ===
-- 啟用 RLS（生產環境建議）
-- ALTER TABLE pools_raw ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE pools_scored ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE plans ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE positions ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE hedges ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE txs ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE earnings ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE risk_events ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE user_filters ENABLE ROW LEVEL SECURITY;

-- === 視圖：常用查詢 ===
-- 活躍頭寸總覽
CREATE OR REPLACE VIEW v_active_positions AS
SELECT 
    p.*,
    pr.token0_symbol,
    pr.token1_symbol,
    pr.dex,
    ps.score as current_score,
    ps.risk_level
FROM positions p
LEFT JOIN pools_raw pr ON p.pool_id = pr.id
LEFT JOIN pools_scored ps ON p.pool_id = ps.pool_id
WHERE p.status = 'active';

-- 風險事件總覽
CREATE OR REPLACE VIEW v_risk_dashboard AS
SELECT 
    event_type,
    severity,
    COUNT(*) as event_count,
    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
    MAX(detected_at) as latest_event
FROM risk_events
WHERE detected_at >= NOW() - INTERVAL '24 hours'
GROUP BY event_type, severity
ORDER BY severity DESC, event_count DESC;

-- 績效總覽
CREATE OR REPLACE VIEW v_performance_summary AS
SELECT 
    DATE_TRUNC('day', reporting_date) as date,
    COUNT(DISTINCT position_id) as active_positions,
    SUM(net_pnl_usd) as daily_pnl,
    AVG(roi_percent) as avg_roi,
    SUM(lp_fees_usd) as total_fees,
    SUM(gas_costs_usd) as total_gas_costs
FROM earnings
WHERE reporting_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', reporting_date)
ORDER BY date DESC;

-- === 數據保留策略函數 ===
CREATE OR REPLACE FUNCTION cleanup_old_data(retention_days INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- 清理舊的原始池子數據
    DELETE FROM pools_raw 
    WHERE created_at < NOW() - (retention_days || ' days')::INTERVAL;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 清理舊的風險事件
    DELETE FROM risk_events 
    WHERE created_at < NOW() - (retention_days || ' days')::INTERVAL 
    AND status = 'resolved';
    GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- === 初始化系統配置 ===
INSERT INTO system_config (key, value, description, category) VALUES
('risk.k_drop', '2.5', 'kDrop 風險倍數', 'risk'),
('risk.min_drop_pct', '0.06', '最小跌幅閾值', 'risk'),
('risk.atr_window', '14', 'ATR 計算窗口', 'risk'),
('strategy.max_positions', '10', '最大同時持倉數', 'strategy'),
('system.version', '"3.0.0"', '系統版本', 'system')
ON CONFLICT (key) DO NOTHING;

-- 完成提示
DO $$
BEGIN
    RAISE NOTICE 'Dy-Flow v3 數據庫 Schema 初始化完成！';
    RAISE NOTICE '已創建 % 個表，% 個視圖，% 個函數', 10, 3, 2;
END;
$$;
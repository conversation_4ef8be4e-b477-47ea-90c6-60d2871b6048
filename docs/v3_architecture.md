# Dy-Flow v3 Architecture

This directory contains a lightweight skeleton implementation of the Dy-Flow v3
agentic framework.  Agents live under `src/v3/agents` and are orchestrated by
`AGNOSchedulerV3`.

The current code only provides lightweight agents that match the design outlined
in the technical plan.  A minimal `LpMonitorAgent` example tracks pools and
allows other agents to adapt their check intervals based on simple risk scores.
Full strategy logic, database integration and blockchain interaction still need
to be implemented by the development team.

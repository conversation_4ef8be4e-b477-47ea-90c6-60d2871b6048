#!/usr/bin/env python3
"""
DyFlow CLI UI - 交互式LP监控界面
基于Rich库的美观CLI界面
"""

import asyncio
import time
from datetime import datetime
from typing import Dict, List, Any, Optional

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.align import Align
from rich.columns import Columns

from src.tools.pool_scanner_tool import PoolScannerToolSync

console = Console()


class DyFlowCLI:
    """DyFlow CLI界面类"""
    
    def __init__(self):
        self.scanner = PoolScannerToolSync()
        self.bsc_pools = []
        self.sol_pools = []
        self.last_update = None
        
    def create_header(self) -> Panel:
        """创建头部面板"""
        header_text = Text()
        header_text.append("🌟 DyFlow LP Monitor", style="bold blue")
        header_text.append(" | ", style="white")
        header_text.append("Real-time LP Pool Analytics", style="italic cyan")
        
        if self.last_update:
            header_text.append(f"\nLast Update: {self.last_update}", style="dim white")
        
        return Panel(
            Align.center(header_text),
            style="blue",
            padding=(1, 2)
        )
    
    def create_chain_table(self, chain: str, pools: List[Dict[str, Any]]) -> Table:
        """创建链的池子表格"""
        # 链的颜色主题
        chain_colors = {
            "BSC": "yellow",
            "SOL": "purple"
        }
        
        color = chain_colors.get(chain, "white")
        
        table = Table(
            title=f"🔗 {chain} Top Pools",
            title_style=f"bold {color}",
            border_style=color,
            show_header=True,
            header_style=f"bold {color}"
        )
        
        # 添加列
        table.add_column("Rank", style="dim", width=4)
        table.add_column("Pair", style="bold", width=10)
        table.add_column("TVL", style="green", width=8)
        table.add_column("24h Vol", style="blue", width=8)
        table.add_column("APR", style="red", width=7)
        table.add_column("Fee", style="cyan", width=6)
        
        # 添加数据行
        for i, pool in enumerate(pools[:3], 1):  # 只显示前3个
            tvl = self.format_currency(pool.get('tvl_usd', 0))
            volume = self.format_currency(pool.get('volume_24h', 0))
            apr = f"{pool.get('fee_tvl', 0):.2f}%"
            fee_rate = f"{pool.get('fee_rate', 0)*100:.2f}%"
            
            # 根据APR设置颜色
            apr_color = "green" if pool.get('fee_tvl', 0) > 20 else "yellow" if pool.get('fee_tvl', 0) > 10 else "red"
            
            table.add_row(
                f"#{i}",
                pool.get('pair_name', 'N/A'),
                tvl,
                volume,
                f"[{apr_color}]{apr}[/{apr_color}]",
                fee_rate
            )
        
        return table
    
    def format_currency(self, amount: float) -> str:
        """格式化货币显示"""
        if amount >= 1e12:
            return f"${amount/1e12:.1f}T"
        elif amount >= 1e9:
            return f"${amount/1e9:.1f}B"
        elif amount >= 1e6:
            return f"${amount/1e6:.1f}M"
        elif amount >= 1e3:
            return f"${amount/1e3:.1f}K"
        else:
            return f"${amount:.0f}"
    
    def create_stats_panel(self) -> Panel:
        """创建统计面板"""
        if not self.bsc_pools and not self.sol_pools:
            return Panel("No data available", title="📊 Statistics")
        
        # 计算统计数据
        total_pools = len(self.bsc_pools) + len(self.sol_pools)
        
        bsc_total_tvl = sum(p.get('tvl_usd', 0) for p in self.bsc_pools[:3])
        sol_total_tvl = sum(p.get('tvl_usd', 0) for p in self.sol_pools[:3])
        total_tvl = bsc_total_tvl + sol_total_tvl
        
        bsc_avg_apr = sum(p.get('fee_tvl', 0) for p in self.bsc_pools[:3]) / max(len(self.bsc_pools[:3]), 1)
        sol_avg_apr = sum(p.get('fee_tvl', 0) for p in self.sol_pools[:3]) / max(len(self.sol_pools[:3]), 1)
        
        stats_text = f"""
[bold cyan]📊 Portfolio Overview[/bold cyan]

[yellow]BSC Pools:[/yellow] {len(self.bsc_pools[:3])} | [purple]SOL Pools:[/purple] {len(self.sol_pools[:3])}
[green]Total TVL:[/green] {self.format_currency(total_tvl)}
[blue]Avg APR:[/blue] BSC {bsc_avg_apr:.2f}% | SOL {sol_avg_apr:.2f}%

[dim]Monitoring {total_pools} pools across 2 chains[/dim]
        """
        
        return Panel(
            stats_text.strip(),
            title="📊 Statistics",
            border_style="green",
            padding=(1, 2)
        )
    
    async def fetch_data(self) -> None:
        """获取池子数据"""
        try:
            # 获取BSC数据
            bsc_result = self.scanner.scan_pools('bsc', {'max_pools': 5})
            if bsc_result.get('pools'):
                self.bsc_pools = bsc_result['pools']
            
            # 获取Solana数据
            sol_result = self.scanner.scan_pools('solana', {'max_pools': 5})
            if sol_result.get('pools'):
                self.sol_pools = sol_result['pools']
            
            self.last_update = datetime.now().strftime("%H:%M:%S")
            
        except Exception as e:
            console.print(f"[red]Error fetching data: {e}[/red]")
    
    def create_layout(self) -> Layout:
        """创建主布局"""
        layout = Layout()
        
        # 分割布局
        layout.split_column(
            Layout(name="header", size=5),
            Layout(name="main"),
            Layout(name="footer", size=8)
        )
        
        # 主区域分割为两列
        layout["main"].split_row(
            Layout(name="bsc"),
            Layout(name="sol")
        )
        
        # 填充内容
        layout["header"].update(self.create_header())
        layout["bsc"].update(self.create_chain_table("BSC", self.bsc_pools))
        layout["sol"].update(self.create_chain_table("SOL", self.sol_pools))
        layout["footer"].update(self.create_stats_panel())
        
        return layout
    
    async def run_live_monitor(self, refresh_interval: int = 30):
        """运行实时监控"""
        console.print("[bold green]🚀 Starting DyFlow Live Monitor...[/bold green]")
        console.print(f"[dim]Refresh interval: {refresh_interval} seconds[/dim]")
        console.print("[dim]Press Ctrl+C to exit[/dim]\n")
        
        try:
            with Live(self.create_layout(), refresh_per_second=1, screen=True) as live:
                while True:
                    # 显示加载状态
                    with Progress(
                        SpinnerColumn(),
                        TextColumn("[progress.description]{task.description}"),
                        console=console,
                        transient=True
                    ) as progress:
                        task = progress.add_task("Fetching pool data...", total=None)
                        await self.fetch_data()
                    
                    # 更新布局
                    live.update(self.create_layout())
                    
                    # 等待下次刷新
                    await asyncio.sleep(refresh_interval)
                    
        except KeyboardInterrupt:
            console.print("\n[yellow]👋 DyFlow Monitor stopped by user[/yellow]")
        except Exception as e:
            console.print(f"\n[red]❌ Error: {e}[/red]")


@click.group()
def cli():
    """🌟 DyFlow - LP Pool Monitor CLI"""
    pass


@cli.command()
@click.option('--refresh', '-r', default=30, help='Refresh interval in seconds')
@click.option('--no-live', is_flag=True, help='Disable live updates (single refresh)')
def monitor(refresh, no_live):
    """🔍 Start live pool monitoring"""
    dyflow_cli = DyFlowCLI()

    if no_live:
        # 单次刷新模式
        console.print("[bold blue]🔍 Single refresh mode[/bold blue]")
        asyncio.run(dyflow_cli.fetch_data())
        layout = dyflow_cli.create_layout()
        console.print(layout)
    else:
        # 实时监控模式
        asyncio.run(dyflow_cli.run_live_monitor(refresh))


@cli.command()
def snapshot():
    """📸 Take a one-time snapshot of pools"""
    dyflow_cli = DyFlowCLI()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        task = progress.add_task("Fetching pool data...", total=None)
        asyncio.run(dyflow_cli.fetch_data())
    
    # 显示结果
    layout = dyflow_cli.create_layout()
    console.print(layout)


@cli.command()
@click.argument('chain', type=click.Choice(['bsc', 'solana', 'both']))
@click.option('--count', '-c', default=5, help='Number of pools to show')
def pools(chain, count):
    """📋 List top pools for specific chain"""
    dyflow_cli = DyFlowCLI()
    
    console.print(f"[bold blue]Fetching top {count} pools for {chain.upper()}...[/bold blue]")
    
    if chain in ['bsc', 'both']:
        result = dyflow_cli.scanner.scan_pools('bsc', {'max_pools': count})
        if result.get('pools'):
            table = dyflow_cli.create_chain_table("BSC", result['pools'])
            console.print(table)
    
    if chain in ['solana', 'both']:
        result = dyflow_cli.scanner.scan_pools('solana', {'max_pools': count})
        if result.get('pools'):
            table = dyflow_cli.create_chain_table("SOL", result['pools'])
            console.print(table)


@cli.command()
def demo():
    """🎬 Show CLI demo and usage examples"""
    console.print(Panel.fit(
        """[bold cyan]🌟 DyFlow CLI Demo[/bold cyan]

[yellow]Available Commands:[/yellow]
• [green]snapshot[/green]     - Take a quick snapshot of top pools
• [green]monitor[/green]      - Start live monitoring (updates every 30s)
• [green]pools bsc[/green]    - Show BSC pools only
• [green]pools solana[/green] - Show Solana pools only
• [green]pools both[/green]   - Show both chains

[yellow]Examples:[/yellow]
[dim]python dyflow_cli.py snapshot[/dim]
[dim]python dyflow_cli.py monitor --refresh 60[/dim]
[dim]python dyflow_cli.py pools bsc --count 5[/dim]
[dim]python dyflow_cli.py pools solana -c 3[/dim]

[yellow]Features:[/yellow]
• 🔴 Real-time data from PancakeSwap V3 & Meteora
• 📊 APR calculations and risk indicators
• 🎨 Beautiful colored tables and live updates
• ⚡ Fast GraphQL API integration
        """,
        title="📖 Usage Guide",
        border_style="cyan"
    ))


if __name__ == "__main__":
    cli()

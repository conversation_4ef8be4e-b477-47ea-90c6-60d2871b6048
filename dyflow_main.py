#!/usr/bin/env python3
"""
DyFlow主程序 - 基于Agno Framework的LP监控和自动调整系统
整合了Teams和Workflows，专注于BSC和Solana LP管理
"""

import asyncio
import sys
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import structlog

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

# 设置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.dev.ConsoleRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class DyFlowSystem:
    """DyFlow主系统 - 整合Teams和Workflows"""
    
    def __init__(self):
        self.monitoring_team = None
        self.adjustment_team = None
        self.risk_team = None
        self.session_id = f"dyflow_{int(datetime.now().timestamp())}"
        
        # 系统配置
        self.config = {
            'supported_chains': ['bsc', 'solana'],
            'default_monitoring_pools': 10,
            'monitoring_interval_minutes': 5,
            'risk_check_interval_minutes': 2,
            'auto_adjustment_enabled': True,
            'max_slippage_percent': 1.0,
            'min_tvl_threshold': 50000
        }
        
    async def initialize(self) -> bool:
        """初始化DyFlow系统"""
        try:
            print("🚀 初始化DyFlow系统...")
            
            # 检查Agno Framework
            try:
                from agno import Team
                from agno.storage.sqlite import SqliteStorage
                print("✅ Agno Framework可用")
            except ImportError:
                print("❌ Agno Framework不可用，请安装: pip install agno")
                return False
            
            # 初始化Teams
            from src.teams.lp_monitoring_team import LPMonitoringTeam
            
            self.monitoring_team = LPMonitoringTeam(
                name="lp_monitoring_team",
                description="LP池子监控和数据收集团队",
                debug_mode=False
            )
            
            print("✅ LP监控团队初始化完成")
            
            # 设置监控上下文
            self.monitoring_team.setup_lp_monitoring_context(
                chains=self.config['supported_chains']
            )
            
            logger.info("dyflow_system_initialized", 
                       session_id=self.session_id,
                       supported_chains=self.config['supported_chains'])
            
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            logger.error("dyflow_system_init_failed", error=str(e))
            return False
    
    async def run_monitoring_cycle(self, chains: List[str] = None, max_pools: int = None) -> Dict[str, Any]:
        """运行监控周期"""
        if not self.monitoring_team:
            raise RuntimeError("监控团队未初始化")
        
        chains = chains or self.config['supported_chains']
        max_pools = max_pools or self.config['default_monitoring_pools']
        
        try:
            print(f"\n🔍 开始监控周期 - {datetime.now().strftime('%H:%M:%S')}")
            print(f"   目标链: {', '.join(chains)}")
            print(f"   最大池子数: {max_pools}")
            
            # 执行监控
            result = await self.monitoring_team.monitor_pools(chains, max_pools)
            
            if result.get('status') != 'failed':
                print("✅ 监控周期完成")
                
                # 显示简要结果
                if 'pool_analysis' in result:
                    print("📊 池子分析完成")
                if 'market_monitoring' in result:
                    print("📈 市场监控完成")
                
                return result
            else:
                print(f"❌ 监控周期失败: {result.get('error', '未知错误')}")
                return result
                
        except Exception as e:
            print(f"❌ 监控周期异常: {e}")
            logger.error("monitoring_cycle_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def analyze_specific_pools(self, pool_ids: List[str]) -> Dict[str, Any]:
        """分析特定池子"""
        if not self.monitoring_team:
            raise RuntimeError("监控团队未初始化")
        
        try:
            print(f"\n🎯 分析特定池子...")
            print(f"   池子数量: {len(pool_ids)}")
            
            result = await self.monitoring_team.analyze_specific_pools(pool_ids)
            
            if result.get('status') == 'completed':
                print("✅ 池子分析完成")
            else:
                print(f"❌ 池子分析失败: {result.get('error', '未知错误')}")
            
            return result
            
        except Exception as e:
            print(f"❌ 池子分析异常: {e}")
            logger.error("pool_analysis_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """获取市场总结"""
        if not self.monitoring_team:
            raise RuntimeError("监控团队未初始化")
        
        try:
            print(f"\n📋 获取市场总结...")
            
            result = await self.monitoring_team.get_market_summary()
            
            if result.get('status') == 'completed':
                print("✅ 市场总结完成")
            else:
                print(f"❌ 市场总结失败: {result.get('error', '未知错误')}")
            
            return result
            
        except Exception as e:
            print(f"❌ 市场总结异常: {e}")
            logger.error("market_summary_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def run_continuous_monitoring(self, duration_minutes: int = 60):
        """运行持续监控"""
        print(f"\n🔄 开始持续监控 - 时长: {duration_minutes} 分钟")
        
        start_time = datetime.now()
        cycle_count = 0
        
        try:
            while True:
                cycle_count += 1
                cycle_start = datetime.now()
                
                # 检查是否超时
                elapsed = (cycle_start - start_time).total_seconds() / 60
                if elapsed >= duration_minutes:
                    break
                
                print(f"\n--- 监控周期 #{cycle_count} ---")
                
                # 执行监控周期
                result = await self.run_monitoring_cycle()
                
                # 保存结果
                report_file = f"data/reports/monitoring_cycle_{cycle_count}_{int(cycle_start.timestamp())}.json"
                os.makedirs(os.path.dirname(report_file), exist_ok=True)
                
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"💾 结果已保存: {report_file}")
                
                # 等待下一个周期
                wait_seconds = self.config['monitoring_interval_minutes'] * 60
                remaining_time = duration_minutes - elapsed
                
                if remaining_time > wait_seconds / 60:
                    print(f"⏳ 等待 {wait_seconds} 秒直到下次监控...")
                    await asyncio.sleep(wait_seconds)
                else:
                    break
            
            print(f"\n🎉 持续监控完成!")
            print(f"   总周期数: {cycle_count}")
            print(f"   运行时长: {elapsed:.1f} 分钟")
            
        except KeyboardInterrupt:
            print(f"\n⏹️ 监控被用户中断")
            print(f"   已完成周期: {cycle_count}")
        except Exception as e:
            print(f"\n❌ 持续监控失败: {e}")
            logger.error("continuous_monitoring_failed", error=str(e))
    
    def save_session_report(self, data: Dict[str, Any]):
        """保存会话报告"""
        report_file = f"data/reports/dyflow_session_{self.session_id}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        session_report = {
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "config": self.config,
            "data": data
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(session_report, f, indent=2, ensure_ascii=False)
        
        print(f"💾 会话报告已保存: {report_file}")


async def main():
    """主函数"""
    print("🌟 DyFlow - LP监控和自动调整系统")
    print("基于Agno Framework Teams架构")
    print("=" * 50)
    
    # 创建系统实例
    dyflow = DyFlowSystem()
    
    # 初始化系统
    if not await dyflow.initialize():
        print("❌ 系统初始化失败，退出")
        return False
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "monitor":
            # 单次监控
            result = await dyflow.run_monitoring_cycle()
            dyflow.save_session_report(result)
            
        elif command == "continuous":
            # 持续监控
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            await dyflow.run_continuous_monitoring(duration)
            
        elif command == "market":
            # 市场总结
            result = await dyflow.get_market_summary()
            dyflow.save_session_report(result)
            
        elif command == "analyze":
            # 分析特定池子
            if len(sys.argv) > 2:
                pool_ids = sys.argv[2].split(',')
                result = await dyflow.analyze_specific_pools(pool_ids)
                dyflow.save_session_report(result)
            else:
                print("❌ 请提供池子ID，用逗号分隔")
                return False
        else:
            print(f"❌ 未知命令: {command}")
            return False
    else:
        # 默认执行单次监控
        print("执行默认监控...")
        result = await dyflow.run_monitoring_cycle()
        dyflow.save_session_report(result)
    
    return True


if __name__ == "__main__":
    print("使用方法:")
    print("  python dyflow_main.py monitor          # 单次监控")
    print("  python dyflow_main.py continuous 120   # 持续监控120分钟")
    print("  python dyflow_main.py market           # 获取市场总结")
    print("  python dyflow_main.py analyze pool1,pool2  # 分析特定池子")
    print()
    
    success = asyncio.run(main())
    exit(0 if success else 1)

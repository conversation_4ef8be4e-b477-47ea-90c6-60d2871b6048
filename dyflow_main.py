#!/usr/bin/env python3
"""
DyFlow主程序 - 基于Agno Framework的LP监控和自动调整系统
使用Agno Workflow和Teams架构，专注于BSC和Solana LP管理
"""

import asyncio
import sys
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import structlog

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

# 设置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.dev.ConsoleRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class DyFlowSystem:
    """DyFlow主系统 - 基于Agno Framework"""

    def __init__(self):
        self.master_workflow = None
        self.lp_monitoring_workflow = None
        self.lp_analysis_workflow = None
        self.lp_execution_workflow = None
        self.session_id = f"dyflow_{int(datetime.now().timestamp())}"

        # 系统配置
        self.config = {
            'supported_chains': ['bsc', 'solana'],
            'default_monitoring_pools': 10,
            'monitoring_interval_minutes': 5,
            'continuous_monitoring_enabled': True,
            'auto_execution_enabled': False,  # 自动执行开关
            'risk_monitoring_enabled': True,
            'analysis_enabled': True,
            'execution_enabled': True
        }
        
    async def initialize(self) -> bool:
        """初始化DyFlow系统"""
        try:
            print("🚀 初始化DyFlow系统 (基于Agno Framework)...")

            # 检查Agno Framework
            try:
                from agno.workflow import Workflow
                from agno.storage.sqlite import SqliteStorage
                print("✅ Agno Framework可用")
            except ImportError as e:
                print(f"❌ Agno Framework不可用: {e}")
                print("请安装: pip install agno")
                return False

            # 创建数据目录
            os.makedirs("data/agno_memory", exist_ok=True)
            os.makedirs("data/reports", exist_ok=True)

            # 初始化LP监控工作流程
            from src.workflows.lp_monitoring_workflow import LPMonitoringWorkflow
            from src.workflows.lp_analysis_workflow import LPAnalysisWorkflow
            from src.workflows.lp_execution_workflow import LPExecutionWorkflow

            self.lp_monitoring_workflow = LPMonitoringWorkflow(
                session_id=f"lp_monitoring_{self.session_id}",
                storage=SqliteStorage(
                    table_name="lp_monitoring_workflows",
                    db_file="data/agno_memory/lp_monitoring.db",
                    auto_upgrade_schema=True,
                    mode='workflow'
                ),
                debug_mode=False
            )

            self.lp_analysis_workflow = LPAnalysisWorkflow(
                session_id=f"lp_analysis_{self.session_id}",
                storage=SqliteStorage(
                    table_name="lp_analysis_workflows",
                    db_file="data/agno_memory/lp_analysis.db",
                    auto_upgrade_schema=True,
                    mode='workflow'
                ),
                debug_mode=False
            )

            self.lp_execution_workflow = LPExecutionWorkflow(
                session_id=f"lp_execution_{self.session_id}",
                storage=SqliteStorage(
                    table_name="lp_execution_workflows",
                    db_file="data/agno_memory/lp_execution.db",
                    auto_upgrade_schema=True,
                    mode='workflow'
                ),
                debug_mode=False
            )

            print("✅ 完整工作流程架构初始化完成")
            print(f"   监控工作流程: 3个Agent (DataScout, PoolAnalyzer, RiskAssessor)")
            print(f"   分析工作流程: 3个Agent (MarketAnalyst, PerformanceAnalyst, OpportunityScout)")
            print(f"   执行工作流程: 3个Agent (StrategyExecutor, RiskController, ExecutionMonitor)")
            print(f"   总计: 9个专业Agent")

            # 更新配置
            self.lp_monitoring_workflow.update_monitoring_config(**self.config)
            self.lp_analysis_workflow.update_analysis_config(**self.config)
            self.lp_execution_workflow.update_execution_config(**self.config)
            
            logger.info("dyflow_system_initialized", 
                       session_id=self.session_id,
                       supported_chains=self.config['supported_chains'])
            
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            logger.error("dyflow_system_init_failed", error=str(e))
            return False
    
    async def run_monitoring_cycle(self, chains: List[str] = None, max_pools: int = None) -> Dict[str, Any]:
        """运行监控周期"""
        if not self.lp_monitoring_workflow:
            raise RuntimeError("LP监控工作流程未初始化")
        
        chains = chains or self.config['supported_chains']
        max_pools = max_pools or self.config['default_monitoring_pools']
        
        try:
            print(f"\n🔍 开始监控周期 - {datetime.now().strftime('%H:%M:%S')}")
            print(f"   目标链: {', '.join(chains)}")
            print(f"   最大池子数: {max_pools}")
            
            # 执行Agno工作流程
            result = self.lp_monitoring_workflow.monitor_pools(chains, max_pools)
            
            if result.get('status') == 'completed':
                print("✅ 监控周期完成")

                # 显示简要结果
                results = result.get('results', {})
                if 'data_collection' in results:
                    print("📡 数据收集完成")
                if 'pool_analysis' in results:
                    print("📊 池子分析完成")
                if 'risk_assessment' in results:
                    print("🛡️ 风险评估完成")
                
                return result
            else:
                print(f"❌ 监控周期失败: {result.get('error', '未知错误')}")
                return result
                
        except Exception as e:
            print(f"❌ 监控周期异常: {e}")
            logger.error("monitoring_cycle_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def analyze_specific_pools(self, pool_ids: List[str]) -> Dict[str, Any]:
        """分析特定池子"""
        try:
            print(f"\n🎯 分析特定池子...")
            print(f"   池子数量: {len(pool_ids)}")

            # 使用工作流程进行分析
            result = self.lp_monitoring_workflow.monitor_pools()

            print("✅ 池子分析完成")
            return result

        except Exception as e:
            print(f"❌ 池子分析异常: {e}")
            logger.error("pool_analysis_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """获取市场总结"""
        if not self.monitoring_team:
            raise RuntimeError("监控团队未初始化")
        
        try:
            print(f"\n📋 获取市场总结...")
            
            result = await self.monitoring_team.get_market_summary()
            
            if result.get('status') == 'completed':
                print("✅ 市场总结完成")
            else:
                print(f"❌ 市场总结失败: {result.get('error', '未知错误')}")
            
            return result
            
        except Exception as e:
            print(f"❌ 市场总结异常: {e}")
            logger.error("market_summary_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def run_continuous_monitoring(self, duration_minutes: int = 60):
        """运行持续监控"""
        print(f"\n🔄 开始持续监控 - 时长: {duration_minutes} 分钟")
        
        start_time = datetime.now()
        cycle_count = 0
        
        try:
            while True:
                cycle_count += 1
                cycle_start = datetime.now()
                
                # 检查是否超时
                elapsed = (cycle_start - start_time).total_seconds() / 60
                if elapsed >= duration_minutes:
                    break
                
                print(f"\n--- 监控周期 #{cycle_count} ---")
                
                # 执行监控周期
                result = await self.run_monitoring_cycle()
                
                # 保存结果
                report_file = f"data/reports/monitoring_cycle_{cycle_count}_{int(cycle_start.timestamp())}.json"
                os.makedirs(os.path.dirname(report_file), exist_ok=True)
                
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"💾 结果已保存: {report_file}")
                
                # 等待下一个周期
                wait_seconds = self.config['monitoring_interval_minutes'] * 60
                remaining_time = duration_minutes - elapsed
                
                if remaining_time > wait_seconds / 60:
                    print(f"⏳ 等待 {wait_seconds} 秒直到下次监控...")
                    await asyncio.sleep(wait_seconds)
                else:
                    break
            
            print(f"\n🎉 持续监控完成!")
            print(f"   总周期数: {cycle_count}")
            print(f"   运行时长: {elapsed:.1f} 分钟")
            
        except KeyboardInterrupt:
            print(f"\n⏹️ 监控被用户中断")
            print(f"   已完成周期: {cycle_count}")
        except Exception as e:
            print(f"\n❌ 持续监控失败: {e}")
            logger.error("continuous_monitoring_failed", error=str(e))
    
    async def run_full_cycle(self) -> Dict[str, Any]:
        """运行完整的LP管理周期 (监控+分析+执行)"""
        try:
            print(f"\n🚀 开始完整LP管理周期...")

            # 如果有主控制工作流程，使用它
            if self.master_workflow:
                result = self.master_workflow.execute_full_cycle()
                print("✅ 完整周期完成 (使用主控制工作流程)")
                return result
            else:
                # 否则依次执行各个阶段
                print("📡 阶段1: 监控")
                monitoring_result = await self.run_monitoring_cycle()

                print("📊 阶段2: 分析")
                analysis_result = await self.run_analysis_only()

                print("⚙️ 阶段3: 执行计划制定")
                # 模拟执行计划
                adjustment_plan = {"type": "rebalance", "based_on": "analysis_results"}
                execution_result = await self.run_execution_only(adjustment_plan)

                combined_result = {
                    "full_cycle_id": f"full_{int(datetime.now().timestamp())}",
                    "timestamp": datetime.now().isoformat(),
                    "status": "completed",
                    "phases": {
                        "monitoring": monitoring_result,
                        "analysis": analysis_result,
                        "execution": execution_result
                    }
                }

                print("✅ 完整周期完成 (分阶段执行)")
                return combined_result

        except Exception as e:
            print(f"❌ 完整周期失败: {e}")
            logger.error("full_cycle_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def run_analysis_only(self) -> Dict[str, Any]:
        """只执行分析阶段"""
        try:
            print(f"\n📊 开始深度分析...")

            # 如果有分析工作流程，使用它
            if self.lp_analysis_workflow:
                # 先获取一些池子数据
                monitoring_result = await self.run_monitoring_cycle()
                pools_data = monitoring_result.get('results', {}).get('data_collection', {}).get('content', [])

                result = self.lp_analysis_workflow.analyze_pools(pools_data)
                print("✅ 深度分析完成")
                return result
            else:
                # 使用监控工作流程的分析功能
                result = await self.run_monitoring_cycle()
                print("✅ 基础分析完成 (使用监控工作流程)")
                return result

        except Exception as e:
            print(f"❌ 分析失败: {e}")
            logger.error("analysis_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def run_execution_only(self, adjustment_plan: Dict[str, Any]) -> Dict[str, Any]:
        """只执行调整阶段"""
        try:
            print(f"\n⚙️ 开始执行调整...")
            print(f"   调整计划: {adjustment_plan}")

            # 如果有执行工作流程，使用它
            if self.lp_execution_workflow:
                result = self.lp_execution_workflow.execute_adjustments(adjustment_plan)
                print("✅ 执行调整完成")
                return result
            else:
                # 模拟执行
                result = {
                    "execution_id": f"exec_{int(datetime.now().timestamp())}",
                    "timestamp": datetime.now().isoformat(),
                    "status": "simulated",
                    "adjustment_plan": adjustment_plan,
                    "message": "执行工作流程未初始化，仅模拟执行"
                }
                print("✅ 模拟执行完成")
                return result

        except Exception as e:
            print(f"❌ 执行失败: {e}")
            logger.error("execution_failed", error=str(e))
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def save_session_report(self, data: Dict[str, Any]):
        """保存会话报告"""
        report_file = f"data/reports/dyflow_session_{self.session_id}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)

        session_report = {
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "config": self.config,
            "data": data
        }

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(session_report, f, indent=2, ensure_ascii=False)

        print(f"💾 会话报告已保存: {report_file}")


def print_usage():
    """打印使用说明"""
    print("\n使用方法:")
    print("  python dyflow_main.py monitor          # 单次监控")
    print("  python dyflow_main.py continuous 120   # 持续监控120分钟")
    print("  python dyflow_main.py market           # 获取市场总结")
    print("  python dyflow_main.py analyze pool1,pool2  # 分析特定池子")
    print("  python dyflow_main.py full             # 完整周期 (监控+分析+执行)")
    print("  python dyflow_main.py analysis         # 只执行深度分析")
    print("  python dyflow_main.py execute pool1,pool2  # 只执行调整")


async def main():
    """主函数"""
    print("🌟 DyFlow - LP监控和自动调整系统")
    print("基于Agno Framework完整架构")
    print("=" * 50)
    
    # 创建系统实例
    dyflow = DyFlowSystem()
    
    # 初始化系统
    if not await dyflow.initialize():
        print("❌ 系统初始化失败，退出")
        return False
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "monitor":
            # 单次监控
            result = await dyflow.run_monitoring_cycle()
            dyflow.save_session_report(result)
            
        elif command == "continuous":
            # 持续监控
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            await dyflow.run_continuous_monitoring(duration)
            
        elif command == "market":
            # 市场总结
            result = await dyflow.get_market_summary()
            dyflow.save_session_report(result)
            
        elif command == "analyze":
            # 分析特定池子
            if len(sys.argv) > 2:
                pool_ids = sys.argv[2].split(',')
                result = await dyflow.analyze_specific_pools(pool_ids)
                dyflow.save_session_report(result)
            else:
                print("❌ 请提供池子ID，用逗号分隔")
                return False

        elif command == "full":
            # 完整周期 (监控+分析+执行)
            result = await dyflow.run_full_cycle()
            dyflow.save_session_report(result)

        elif command == "analysis":
            # 只执行分析
            result = await dyflow.run_analysis_only()
            dyflow.save_session_report(result)

        elif command == "execute":
            # 只执行调整 (需要提供调整计划)
            if len(sys.argv) > 2:
                # 简化的调整计划
                adjustment_plan = {"type": "rebalance", "pools": sys.argv[2].split(',')}
                result = await dyflow.run_execution_only(adjustment_plan)
                dyflow.save_session_report(result)
            else:
                print("❌ 请提供要调整的池子，用逗号分隔")
                return False

        else:
            print(f"❌ 未知命令: {command}")
            print_usage()
            return False
    else:
        # 默认执行单次监控
        print("执行默认监控...")
        result = await dyflow.run_monitoring_cycle()
        dyflow.save_session_report(result)
    
    return True


if __name__ == "__main__":
    print_usage()
    print()

    success = asyncio.run(main())
    exit(0 if success else 1)

#!/usr/bin/env python3
"""
DyFlow简化版 - 整合现有组件的LP监控和自动调整系统
使用现有的Agent和工具，专注于BSC和Solana LP管理
"""

import asyncio
import sys
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import structlog

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

# 设置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.dev.ConsoleRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class DyFlowSimplifiedSystem:
    """DyFlow简化系统 - 使用现有组件"""
    
    def __init__(self):
        self.pancake_api = None
        self.pool_scanner = None
        self.scorer_agent = None
        self.risk_agent = None
        self.session_id = f"dyflow_{int(datetime.now().timestamp())}"
        
        # 系统配置
        self.config = {
            'supported_chains': ['bsc'],  # 目前只支持BSC
            'default_monitoring_pools': 10,
            'monitoring_interval_minutes': 5,
            'min_tvl_threshold': 50000,
            'min_volume_24h': 10000
        }
        
    async def initialize(self) -> bool:
        """初始化DyFlow系统"""
        try:
            print("🚀 初始化DyFlow简化系统...")
            
            # 1. 初始化PancakeSwap API
            try:
                from src.integrations.pancakeswap import PancakeSwapV3Integration
                self.pancake_api = PancakeSwapV3Integration({})
                print("✅ PancakeSwap V3 API初始化完成")
            except Exception as e:
                print(f"⚠️ PancakeSwap API初始化失败: {e}")
            
            # 2. 初始化池子扫描工具
            try:
                from agno_tools.pool_scanner_tool import PoolScannerTool
                self.pool_scanner = PoolScannerTool()
                print("✅ 池子扫描工具初始化完成")
            except Exception as e:
                print(f"⚠️ 池子扫描工具初始化失败: {e}")
            
            # 3. 初始化评分Agent
            try:
                from src.agents.scorer_v2_agno import ScorerV2AgnoAgent
                from src.utils.config import Config
                from src.utils.database import Database
                
                config = Config()
                database = Database(config)
                
                self.scorer_agent = ScorerV2AgnoAgent("scorer_v2_agno", config, database)
                await self.scorer_agent.initialize()
                print("✅ 评分Agent初始化完成")
            except Exception as e:
                print(f"⚠️ 评分Agent初始化失败: {e}")
            
            # 4. 初始化风险监控Agent
            try:
                from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
                
                self.risk_agent = RiskSentinelAgnoAgent("risk_sentinel_agno", config, database)
                await self.risk_agent.initialize()
                print("✅ 风险监控Agent初始化完成")
            except Exception as e:
                print(f"⚠️ 风险监控Agent初始化失败: {e}")
            
            logger.info("dyflow_simplified_system_initialized", 
                       session_id=self.session_id)
            
            return True
            
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            logger.error("dyflow_simplified_system_init_failed", error=str(e))
            return False
    
    async def collect_pool_data(self, max_pools: int = 10) -> List[Dict[str, Any]]:
        """收集池子数据"""
        pools_data = []
        
        try:
            # 1. 使用PancakeSwap API获取数据
            if self.pancake_api:
                print("📡 从PancakeSwap V3获取池子数据...")
                async with self.pancake_api as api:
                    pools = await api.get_top_pools(limit=max_pools)
                    if pools:
                        pools_data.extend(pools)
                        print(f"✅ 获取到 {len(pools)} 个PancakeSwap池子")
            
            # 2. 使用池子扫描工具补充数据
            if self.pool_scanner and len(pools_data) < max_pools:
                print("🔍 使用池子扫描工具补充数据...")
                try:
                    result = await self.pool_scanner.run(
                        chain='bsc',
                        filters={
                            'min_tvl': self.config['min_tvl_threshold'],
                            'min_volume_24h': self.config['min_volume_24h'],
                            'max_pools': max_pools - len(pools_data)
                        }
                    )
                    
                    if result and 'pools' in result:
                        additional_pools = result['pools']
                        pools_data.extend(additional_pools)
                        print(f"✅ 扫描工具补充 {len(additional_pools)} 个池子")
                except Exception as e:
                    print(f"⚠️ 池子扫描失败: {e}")
            
            print(f"📊 总共收集到 {len(pools_data)} 个池子数据")
            return pools_data
            
        except Exception as e:
            print(f"❌ 数据收集失败: {e}")
            logger.error("pool_data_collection_failed", error=str(e))
            return []
    
    async def analyze_pools(self, pools_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析池子"""
        try:
            print("🧠 开始AI池子分析...")
            
            analysis_results = {
                "timestamp": datetime.now().isoformat(),
                "pools_analyzed": len(pools_data),
                "scoring_results": None,
                "risk_assessment": None
            }
            
            # 1. 评分分析
            if self.scorer_agent:
                print("📊 执行池子评分...")
                try:
                    scoring_result = await self.scorer_agent.execute()
                    if scoring_result.status == "success":
                        analysis_results["scoring_results"] = {
                            "status": "completed",
                            "data": scoring_result.data,
                            "timestamp": scoring_result.timestamp.isoformat()
                        }
                        print("✅ 池子评分完成")
                    else:
                        print(f"⚠️ 池子评分失败: {scoring_result.status}")
                except Exception as e:
                    print(f"⚠️ 池子评分异常: {e}")
            
            # 2. 风险评估
            if self.risk_agent:
                print("🛡️ 执行风险评估...")
                try:
                    risk_result = await self.risk_agent.execute()
                    if risk_result.status == "success":
                        analysis_results["risk_assessment"] = {
                            "status": "completed",
                            "alerts": risk_result.data if risk_result.data else [],
                            "timestamp": risk_result.timestamp.isoformat()
                        }
                        print("✅ 风险评估完成")
                    else:
                        print(f"⚠️ 风险评估失败: {risk_result.status}")
                except Exception as e:
                    print(f"⚠️ 风险评估异常: {e}")
            
            return analysis_results
            
        except Exception as e:
            print(f"❌ 池子分析失败: {e}")
            logger.error("pool_analysis_failed", error=str(e))
            return {
                "timestamp": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e)
            }
    
    async def run_monitoring_cycle(self, max_pools: int = None) -> Dict[str, Any]:
        """运行监控周期"""
        max_pools = max_pools or self.config['default_monitoring_pools']
        
        try:
            print(f"\n🔍 开始监控周期 - {datetime.now().strftime('%H:%M:%S')}")
            print(f"   最大池子数: {max_pools}")
            
            # 1. 收集数据
            pools_data = await self.collect_pool_data(max_pools)
            
            if not pools_data:
                return {
                    "status": "failed",
                    "error": "未能收集到池子数据",
                    "timestamp": datetime.now().isoformat()
                }
            
            # 2. 分析池子
            analysis_results = await self.analyze_pools(pools_data)
            
            # 3. 整合结果
            monitoring_result = {
                "monitoring_id": f"monitor_{int(datetime.now().timestamp())}",
                "timestamp": datetime.now().isoformat(),
                "status": "completed",
                "pools_data": pools_data[:3],  # 只保存前3个作为示例
                "pools_count": len(pools_data),
                "analysis_results": analysis_results,
                "config": self.config
            }
            
            print("✅ 监控周期完成")
            return monitoring_result
            
        except Exception as e:
            print(f"❌ 监控周期失败: {e}")
            logger.error("monitoring_cycle_failed", error=str(e))
            return {
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def run_continuous_monitoring(self, duration_minutes: int = 60):
        """运行持续监控"""
        print(f"\n🔄 开始持续监控 - 时长: {duration_minutes} 分钟")
        
        start_time = datetime.now()
        cycle_count = 0
        
        try:
            while True:
                cycle_count += 1
                cycle_start = datetime.now()
                
                # 检查是否超时
                elapsed = (cycle_start - start_time).total_seconds() / 60
                if elapsed >= duration_minutes:
                    break
                
                print(f"\n--- 监控周期 #{cycle_count} ---")
                
                # 执行监控周期
                result = await self.run_monitoring_cycle()
                
                # 保存结果
                report_file = f"data/reports/monitoring_cycle_{cycle_count}_{int(cycle_start.timestamp())}.json"
                os.makedirs(os.path.dirname(report_file), exist_ok=True)
                
                with open(report_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"💾 结果已保存: {report_file}")
                
                # 等待下一个周期
                wait_seconds = self.config['monitoring_interval_minutes'] * 60
                remaining_time = duration_minutes - elapsed
                
                if remaining_time > wait_seconds / 60:
                    print(f"⏳ 等待 {wait_seconds} 秒直到下次监控...")
                    await asyncio.sleep(wait_seconds)
                else:
                    break
            
            print(f"\n🎉 持续监控完成!")
            print(f"   总周期数: {cycle_count}")
            print(f"   运行时长: {elapsed:.1f} 分钟")
            
        except KeyboardInterrupt:
            print(f"\n⏹️ 监控被用户中断")
            print(f"   已完成周期: {cycle_count}")
        except Exception as e:
            print(f"\n❌ 持续监控失败: {e}")
            logger.error("continuous_monitoring_failed", error=str(e))
    
    def save_session_report(self, data: Dict[str, Any]):
        """保存会话报告"""
        report_file = f"data/reports/dyflow_session_{self.session_id}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        session_report = {
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "config": self.config,
            "data": data
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(session_report, f, indent=2, ensure_ascii=False)
        
        print(f"💾 会话报告已保存: {report_file}")


async def main():
    """主函数"""
    print("🌟 DyFlow简化版 - LP监控和自动调整系统")
    print("基于现有Agent和工具的整合架构")
    print("=" * 50)
    
    # 创建系统实例
    dyflow = DyFlowSimplifiedSystem()
    
    # 初始化系统
    if not await dyflow.initialize():
        print("❌ 系统初始化失败，退出")
        return False
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "monitor":
            # 单次监控
            result = await dyflow.run_monitoring_cycle()
            dyflow.save_session_report(result)
            
        elif command == "continuous":
            # 持续监控
            duration = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            await dyflow.run_continuous_monitoring(duration)
            
        else:
            print(f"❌ 未知命令: {command}")
            return False
    else:
        # 默认执行单次监控
        print("执行默认监控...")
        result = await dyflow.run_monitoring_cycle()
        dyflow.save_session_report(result)
    
    return True


if __name__ == "__main__":
    print("使用方法:")
    print("  python dyflow_simplified.py monitor          # 单次监控")
    print("  python dyflow_simplified.py continuous 120   # 持续监控120分钟")
    print()
    
    success = asyncio.run(main())
    exit(0 if success else 1)

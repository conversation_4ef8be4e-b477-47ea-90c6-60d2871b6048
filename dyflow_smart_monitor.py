#!/usr/bin/env python3
"""
DyFlow Smart Monitor - 智能LP监控系统
持续监控新池子，自动Agent分析，提供投资策略建议
"""

import asyncio
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, asdict

import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.live import Live
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.align import Align
from rich.columns import Columns
from rich.status import Status

from src.tools.pool_scanner_tool import PoolScannerToolSync
from src.workflows.lp_analysis_workflow import LPAnalysisWorkflow
from src.workflows.lp_execution_workflow import LPExecutionWorkflow

console = Console()


@dataclass
class PoolAlert:
    """池子警报数据结构"""
    pool_id: str
    chain: str
    pair_name: str
    tvl_usd: float
    apr: float
    volume_24h: float
    discovered_at: datetime
    analysis_status: str = "pending"  # pending, analyzing, completed, error
    strategy_recommendation: Optional[str] = None
    entry_range: Optional[Dict[str, float]] = None
    risk_level: Optional[str] = None
    # 新增：Agent分析过程
    agent_reasoning: Optional[Dict[str, Any]] = None
    market_analysis: Optional[str] = None
    performance_analysis: Optional[str] = None
    opportunity_analysis: Optional[str] = None
    analysis_duration: Optional[float] = None


class SmartPoolMonitor:
    """智能池子监控器"""
    
    def __init__(self):
        self.scanner = PoolScannerToolSync()
        self.analysis_workflow = None
        self.execution_workflow = None
        
        # 监控状态
        self.known_pools: Set[str] = set()
        self.new_pool_alerts: List[PoolAlert] = []
        self.current_pools: Dict[str, List[Dict[str, Any]]] = {"bsc": [], "solana": []}
        self.last_update = None
        self.monitoring_stats = {
            "total_scanned": 0,
            "new_discovered": 0,
            "analyzed": 0,
            "recommendations": 0
        }
        
        # 监控配置
        self.config = {
            "min_tvl": 50000,           # 最小TVL $50K
            "min_apr": 10.0,            # 最小APR 10%
            "max_risk_score": 7,        # 最大风险评分
            "scan_interval": 60,        # 扫描间隔60秒
            "max_pools_per_chain": 20,  # 每链最大池子数
            "auto_analysis": True,      # 自动分析新池子
            "alert_threshold_apr": 25.0 # APR警报阈值
        }
        
        # 初始化Agno工作流程
        self._init_workflows()
    
    def _init_workflows(self):
        """初始化Agno工作流程"""
        try:
            from agno.storage.sqlite import SqliteStorage
            
            # 分析工作流程
            self.analysis_workflow = LPAnalysisWorkflow(
                session_id=f"smart_monitor_{int(datetime.now().timestamp())}",
                storage=SqliteStorage(
                    table_name="smart_monitor_analysis",
                    db_file="data/agno_memory/smart_monitor.db",
                    auto_upgrade_schema=True,
                    mode='workflow'
                ),
                debug_mode=False
            )
            
            # 执行工作流程
            self.execution_workflow = LPExecutionWorkflow(
                session_id=f"smart_execution_{int(datetime.now().timestamp())}",
                storage=SqliteStorage(
                    table_name="smart_monitor_execution",
                    db_file="data/agno_memory/smart_execution.db",
                    auto_upgrade_schema=True,
                    mode='workflow'
                ),
                debug_mode=False
            )
            
        except Exception as e:
            console.print(f"[yellow]Warning: Agno workflows not available: {e}[/yellow]")
    
    async def scan_for_new_pools(self) -> List[PoolAlert]:
        """扫描新池子"""
        new_alerts = []
        
        for chain in ["bsc", "solana"]:
            try:
                # 扫描池子
                result = self.scanner.scan_pools(chain, {
                    'max_pools': self.config['max_pools_per_chain'],
                    'min_tvl': self.config['min_tvl'],
                    'min_fee_tvl': self.config['min_apr']
                })
                
                if result.get('pools'):
                    pools = result['pools']
                    self.current_pools[chain] = pools
                    self.monitoring_stats["total_scanned"] += len(pools)
                    
                    # 检查新池子
                    for pool in pools:
                        pool_id = pool.get('id')
                        if pool_id and pool_id not in self.known_pools:
                            # 发现新池子
                            alert = PoolAlert(
                                pool_id=pool_id,
                                chain=chain.upper(),
                                pair_name=pool.get('pair_name', 'Unknown'),
                                tvl_usd=pool.get('tvl_usd', 0),
                                apr=pool.get('fee_tvl', 0),
                                volume_24h=pool.get('volume_24h', 0),
                                discovered_at=datetime.now()
                            )
                            
                            # 检查是否符合警报条件
                            if alert.apr >= self.config['alert_threshold_apr']:
                                new_alerts.append(alert)
                                self.monitoring_stats["new_discovered"] += 1
                            
                            self.known_pools.add(pool_id)
                            
            except Exception as e:
                console.print(f"[red]Error scanning {chain}: {e}[/red]")
        
        return new_alerts
    
    async def analyze_pool_with_agent(self, alert: PoolAlert) -> PoolAlert:
        """使用Agent分析池子 - 记录详细推理过程"""
        if not self.analysis_workflow:
            alert.analysis_status = "error"
            alert.strategy_recommendation = "Analysis workflow not available"
            return alert

        try:
            import time
            start_time = time.time()
            alert.analysis_status = "analyzing"

            # 构造池子数据用于分析
            pool_data = [{
                'id': alert.pool_id,
                'chain': alert.chain,
                'pair_name': alert.pair_name,
                'tvl_usd': alert.tvl_usd,
                'fee_tvl': alert.apr,
                'volume_24h': alert.volume_24h
            }]

            console.print(f"[dim]🤖 Starting AI analysis for {alert.pair_name}...[/dim]")

            # 调用分析工作流程
            analysis_result = self.analysis_workflow.analyze_pools(pool_data, "investment_strategy")

            # 记录分析时间
            alert.analysis_duration = time.time() - start_time

            if analysis_result.get('status') == 'completed':
                # 提取详细的Agent分析结果
                results = analysis_result.get('results', {})

                # 市场分析
                market_analysis = results.get('market_analysis', {}).get('content', '')
                alert.market_analysis = market_analysis

                # 性能分析
                performance_analysis = results.get('performance_analysis', {}).get('content', '')
                alert.performance_analysis = performance_analysis

                # 机会分析
                opportunity_analysis = results.get('opportunity_analysis', {}).get('content', '')
                alert.opportunity_analysis = opportunity_analysis

                # 整合Agent推理过程
                alert.agent_reasoning = {
                    'market_insights': self._extract_key_insights(market_analysis),
                    'performance_metrics': self._extract_performance_metrics(performance_analysis),
                    'opportunity_assessment': self._extract_opportunity_assessment(opportunity_analysis),
                    'decision_factors': self._extract_decision_factors(opportunity_analysis)
                }

                # 基于详细分析做出策略决策
                strategy_decision = self._make_strategy_decision(alert)
                alert.strategy_recommendation = strategy_decision['recommendation']
                alert.risk_level = strategy_decision['risk_level']
                alert.entry_range = strategy_decision['entry_range']

                alert.analysis_status = "completed"
                self.monitoring_stats["analyzed"] += 1

                if alert.strategy_recommendation.startswith("BUY"):
                    self.monitoring_stats["recommendations"] += 1

                console.print(f"[green]✅ Analysis completed for {alert.pair_name} in {alert.analysis_duration:.1f}s[/green]")
                console.print(f"[dim]   Strategy: {alert.strategy_recommendation}[/dim]")

            else:
                alert.analysis_status = "error"
                alert.strategy_recommendation = "Analysis failed"
                console.print(f"[red]❌ Analysis failed for {alert.pair_name}[/red]")

        except Exception as e:
            alert.analysis_status = "error"
            alert.strategy_recommendation = f"Error: {str(e)}"
            console.print(f"[red]❌ Analysis error for {alert.pair_name}: {e}[/red]")

        return alert

    def _extract_key_insights(self, market_analysis: str) -> List[str]:
        """从市场分析中提取关键洞察"""
        insights = []
        if "trend" in market_analysis.lower():
            insights.append("Market trend analysis available")
        if "volatility" in market_analysis.lower():
            insights.append("Volatility assessment included")
        if "liquidity" in market_analysis.lower():
            insights.append("Liquidity analysis performed")
        return insights

    def _extract_performance_metrics(self, performance_analysis: str) -> Dict[str, str]:
        """从性能分析中提取关键指标"""
        metrics = {}
        if "apr" in performance_analysis.lower():
            metrics["apr_analysis"] = "APR evaluation completed"
        if "risk" in performance_analysis.lower():
            metrics["risk_assessment"] = "Risk factors identified"
        if "return" in performance_analysis.lower():
            metrics["return_analysis"] = "Return potential assessed"
        return metrics

    def _extract_opportunity_assessment(self, opportunity_analysis: str) -> Dict[str, Any]:
        """从机会分析中提取评估结果"""
        assessment = {
            "investment_potential": "Unknown",
            "key_factors": [],
            "concerns": []
        }

        content_lower = opportunity_analysis.lower()

        if "high potential" in content_lower or "excellent" in content_lower:
            assessment["investment_potential"] = "High"
        elif "moderate" in content_lower or "medium" in content_lower:
            assessment["investment_potential"] = "Medium"
        elif "low" in content_lower or "poor" in content_lower:
            assessment["investment_potential"] = "Low"

        # 提取关键因素
        if "high apr" in content_lower:
            assessment["key_factors"].append("High APR")
        if "stable" in content_lower:
            assessment["key_factors"].append("Stable performance")
        if "volume" in content_lower:
            assessment["key_factors"].append("Trading volume")

        # 提取关注点
        if "risk" in content_lower:
            assessment["concerns"].append("Risk factors present")
        if "volatile" in content_lower:
            assessment["concerns"].append("High volatility")
        if "impermanent loss" in content_lower:
            assessment["concerns"].append("Impermanent loss risk")

        return assessment

    def _extract_decision_factors(self, opportunity_analysis: str) -> List[str]:
        """提取决策因素"""
        factors = []
        content_lower = opportunity_analysis.lower()

        if "recommend" in content_lower:
            factors.append("Agent recommendation available")
        if "avoid" in content_lower:
            factors.append("Avoidance recommendation")
        if "buy" in content_lower:
            factors.append("Purchase recommendation")
        if "hold" in content_lower:
            factors.append("Hold recommendation")

        return factors

    def _make_strategy_decision(self, alert: PoolAlert) -> Dict[str, Any]:
        """基于Agent分析做出策略决策"""
        # 综合分析结果
        opportunity_content = alert.opportunity_analysis or ""
        content_lower = opportunity_content.lower()

        # 决策逻辑
        if alert.apr > 100:  # 超高APR可能有风险
            return {
                "recommendation": "AVOID - Extremely high APR (potential risk)",
                "risk_level": "High",
                "entry_range": None
            }
        elif alert.apr > 50 and alert.tvl_usd > 1000000:  # 高APR + 高TVL
            if "stable" in content_lower or "recommend" in content_lower:
                return {
                    "recommendation": "BUY - High APR with good fundamentals",
                    "risk_level": "Medium",
                    "entry_range": {
                        "min_allocation": 0.05,
                        "max_allocation": 0.15,
                        "target_apr": alert.apr * 0.8  # 保守估计
                    }
                }
            else:
                return {
                    "recommendation": "HOLD - Monitor for stability",
                    "risk_level": "Medium-High",
                    "entry_range": None
                }
        elif alert.apr > 25 and alert.tvl_usd > 500000:  # 中等APR + 中等TVL
            return {
                "recommendation": "BUY - Balanced risk-reward",
                "risk_level": "Medium",
                "entry_range": {
                    "min_allocation": 0.10,
                    "max_allocation": 0.25,
                    "target_apr": alert.apr * 0.9
                }
            }
        else:
            return {
                "recommendation": "AVOID - Insufficient metrics",
                "risk_level": "High",
                "entry_range": None
            }
    
    def create_header_panel(self) -> Panel:
        """创建头部面板"""
        current_time = datetime.now().strftime("%H:%M:%S")
        
        header_text = Text()
        header_text.append("🤖 DyFlow Smart Monitor", style="bold cyan")
        header_text.append(" | ", style="white")
        header_text.append("AI-Powered LP Discovery & Analysis", style="italic green")
        header_text.append(f"\n⏰ {current_time}", style="dim white")
        
        if self.last_update:
            header_text.append(f" | Last Scan: {self.last_update}", style="dim white")
        
        return Panel(
            Align.center(header_text),
            style="cyan",
            padding=(1, 2)
        )
    
    def create_stats_panel(self) -> Panel:
        """创建统计面板"""
        stats_text = f"""
[bold yellow]📊 Monitoring Statistics[/bold yellow]

[green]Pools Scanned:[/green] {self.monitoring_stats['total_scanned']}
[blue]New Discovered:[/blue] {self.monitoring_stats['new_discovered']}
[purple]AI Analyzed:[/purple] {self.monitoring_stats['analyzed']}
[red]Buy Recommendations:[/red] {self.monitoring_stats['recommendations']}

[dim]Active Alerts: {len([a for a in self.new_pool_alerts if a.analysis_status != 'completed'])}[/dim]
        """
        
        return Panel(
            stats_text.strip(),
            title="📈 Stats",
            border_style="yellow",
            padding=(1, 2)
        )
    
    def create_alerts_table(self) -> Table:
        """创建警报表格"""
        table = Table(
            title="🚨 New Pool Alerts & AI Analysis",
            title_style="bold red",
            border_style="red",
            show_header=True,
            header_style="bold red"
        )
        
        table.add_column("Time", style="dim", width=8)
        table.add_column("Chain", style="bold", width=5)
        table.add_column("Pair", style="cyan", width=12)
        table.add_column("TVL", style="green", width=8)
        table.add_column("APR", style="red", width=7)
        table.add_column("Status", style="yellow", width=10)
        table.add_column("Strategy", style="purple", width=15)
        
        # 显示最近的10个警报
        recent_alerts = sorted(self.new_pool_alerts, key=lambda x: x.discovered_at, reverse=True)[:10]
        
        for alert in recent_alerts:
            time_str = alert.discovered_at.strftime("%H:%M:%S")
            tvl_str = self.format_currency(alert.tvl_usd)
            apr_str = f"{alert.apr:.1f}%"
            
            # 状态颜色
            status_color = {
                "pending": "yellow",
                "analyzing": "blue", 
                "completed": "green",
                "error": "red"
            }.get(alert.analysis_status, "white")
            
            status_text = f"[{status_color}]{alert.analysis_status}[/{status_color}]"
            
            # 策略颜色
            strategy = alert.strategy_recommendation or "Pending..."
            if strategy.startswith("BUY"):
                strategy = f"[green]{strategy}[/green]"
            elif strategy.startswith("AVOID"):
                strategy = f"[red]{strategy}[/red]"
            else:
                strategy = f"[yellow]{strategy}[/yellow]"
            
            table.add_row(
                time_str,
                alert.chain,
                alert.pair_name,
                tvl_str,
                apr_str,
                status_text,
                strategy
            )
        
        return table

    def show_agent_reasoning(self, alert: PoolAlert):
        """显示Agent的详细推理过程"""
        if not alert.agent_reasoning:
            console.print("[yellow]No detailed reasoning available for this pool[/yellow]")
            return

        # 创建详细的推理过程面板
        reasoning_content = f"""[bold cyan]🤖 AI Agent Analysis for {alert.pair_name}[/bold cyan]

[yellow]📊 Pool Metrics:[/yellow]
• Chain: {alert.chain}
• TVL: {self.format_currency(alert.tvl_usd)}
• APR: {alert.apr:.2f}%
• 24h Volume: {self.format_currency(alert.volume_24h)}
• Analysis Duration: {alert.analysis_duration:.1f}s

[yellow]🔍 Market Insights:[/yellow]
{chr(10).join(f"• {insight}" for insight in alert.agent_reasoning.get('market_insights', []))}

[yellow]📈 Performance Metrics:[/yellow]
{chr(10).join(f"• {k}: {v}" for k, v in alert.agent_reasoning.get('performance_metrics', {}).items())}

[yellow]💡 Opportunity Assessment:[/yellow]
• Investment Potential: {alert.agent_reasoning.get('opportunity_assessment', {}).get('investment_potential', 'Unknown')}
• Key Factors: {', '.join(alert.agent_reasoning.get('opportunity_assessment', {}).get('key_factors', []))}
• Concerns: {', '.join(alert.agent_reasoning.get('opportunity_assessment', {}).get('concerns', []))}

[yellow]🎯 Decision Factors:[/yellow]
{chr(10).join(f"• {factor}" for factor in alert.agent_reasoning.get('decision_factors', []))}

[yellow]📋 Final Recommendation:[/yellow]
• Strategy: [bold]{alert.strategy_recommendation}[/bold]
• Risk Level: {alert.risk_level}
• Entry Range: {alert.entry_range if alert.entry_range else 'Not specified'}
        """

        # 显示详细分析内容
        if alert.market_analysis:
            reasoning_content += f"""

[yellow]🌍 Detailed Market Analysis:[/yellow]
[dim]{alert.market_analysis[:500]}{'...' if len(alert.market_analysis) > 500 else ''}[/dim]
            """

        if alert.performance_analysis:
            reasoning_content += f"""

[yellow]📊 Detailed Performance Analysis:[/yellow]
[dim]{alert.performance_analysis[:500]}{'...' if len(alert.performance_analysis) > 500 else ''}[/dim]
            """

        if alert.opportunity_analysis:
            reasoning_content += f"""

[yellow]🎯 Detailed Opportunity Analysis:[/yellow]
[dim]{alert.opportunity_analysis[:500]}{'...' if len(alert.opportunity_analysis) > 500 else ''}[/dim]
            """

        reasoning_panel = Panel(
            reasoning_content.strip(),
            title=f"🧠 Agent Reasoning - {alert.pair_name}",
            border_style="cyan",
            padding=(1, 2)
        )

        console.print(reasoning_panel)
    
    def format_currency(self, amount: float) -> str:
        """格式化货币"""
        if amount >= 1e9:
            return f"${amount/1e9:.1f}B"
        elif amount >= 1e6:
            return f"${amount/1e6:.1f}M"
        elif amount >= 1e3:
            return f"${amount/1e3:.1f}K"
        else:
            return f"${amount:.0f}"
    
    def create_layout(self) -> Layout:
        """创建主布局"""
        layout = Layout()
        
        layout.split_column(
            Layout(name="header", size=5),
            Layout(name="main"),
            Layout(name="footer", size=10)
        )
        
        layout["main"].split_row(
            Layout(name="alerts", ratio=3),
            Layout(name="stats", ratio=1)
        )
        
        # 填充内容
        layout["header"].update(self.create_header_panel())
        layout["alerts"].update(self.create_alerts_table())
        layout["stats"].update(self.create_stats_panel())
        
        # 配置信息
        config_text = f"""
[bold blue]⚙️ Monitor Config[/bold blue]

[dim]Min TVL: ${self.config['min_tvl']:,}
Min APR: {self.config['min_apr']}%
Alert APR: {self.config['alert_threshold_apr']}%
Scan Interval: {self.config['scan_interval']}s
Auto Analysis: {'✅' if self.config['auto_analysis'] else '❌'}[/dim]
        """
        
        layout["footer"].update(Panel(
            config_text.strip(),
            title="🔧 Configuration",
            border_style="blue"
        ))
        
        return layout

    async def run_smart_monitoring(self):
        """运行智能监控主循环"""
        console.print("[bold green]🚀 Starting DyFlow Smart Monitor...[/bold green]")
        console.print(f"[dim]Scan interval: {self.config['scan_interval']} seconds[/dim]")
        console.print("[dim]Press Ctrl+C to exit[/dim]\n")

        try:
            with Live(self.create_layout(), refresh_per_second=2, screen=True) as live:
                while True:
                    # 扫描新池子
                    with Status("[bold blue]Scanning for new pools...", console=console):
                        new_alerts = await self.scan_for_new_pools()
                        self.last_update = datetime.now().strftime("%H:%M:%S")

                    # 处理新发现的池子
                    if new_alerts:
                        self.new_pool_alerts.extend(new_alerts)
                        console.print(f"[green]🔍 Discovered {len(new_alerts)} new high-APR pools![/green]")

                        # 自动分析新池子
                        if self.config['auto_analysis']:
                            for alert in new_alerts:
                                if alert.analysis_status == "pending":
                                    console.print(f"[blue]🤖 Analyzing {alert.pair_name}...[/blue]")
                                    analyzed_alert = await self.analyze_pool_with_agent(alert)

                                    # 更新列表中的警报
                                    for i, existing_alert in enumerate(self.new_pool_alerts):
                                        if existing_alert.pool_id == analyzed_alert.pool_id:
                                            self.new_pool_alerts[i] = analyzed_alert
                                            break

                                    # 如果是买入建议，显示特殊通知
                                    if analyzed_alert.strategy_recommendation and "BUY" in analyzed_alert.strategy_recommendation:
                                        console.print(f"[bold green]💰 BUY SIGNAL: {analyzed_alert.pair_name} - {analyzed_alert.strategy_recommendation}[/bold green]")

                    # 更新布局
                    live.update(self.create_layout())

                    # 等待下次扫描
                    await asyncio.sleep(self.config['scan_interval'])

        except KeyboardInterrupt:
            console.print("\n[yellow]👋 Smart Monitor stopped by user[/yellow]")

            # 保存会话报告
            self.save_session_report()

        except Exception as e:
            console.print(f"\n[red]❌ Error: {e}[/red]")

    def save_session_report(self):
        """保存监控会话报告"""
        try:
            import os
            os.makedirs("data/reports", exist_ok=True)

            report = {
                "session_id": f"smart_monitor_{int(datetime.now().timestamp())}",
                "timestamp": datetime.now().isoformat(),
                "monitoring_stats": self.monitoring_stats,
                "config": self.config,
                "alerts": [asdict(alert) for alert in self.new_pool_alerts],
                "total_pools_known": len(self.known_pools)
            }

            report_file = f"data/reports/smart_monitor_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)

            console.print(f"[green]💾 Session report saved: {report_file}[/green]")

        except Exception as e:
            console.print(f"[red]Failed to save report: {e}[/red]")


@click.group()
def cli():
    """🤖 DyFlow Smart Monitor - AI-Powered LP Discovery"""
    pass


@cli.command()
@click.option('--scan-interval', '-s', default=60, help='Scan interval in seconds')
@click.option('--min-apr', '-a', default=10.0, help='Minimum APR threshold')
@click.option('--alert-apr', '-t', default=25.0, help='APR alert threshold')
@click.option('--no-analysis', is_flag=True, help='Disable auto AI analysis')
def monitor(scan_interval, min_apr, alert_apr, no_analysis):
    """🔍 Start smart pool monitoring with AI analysis"""

    smart_monitor = SmartPoolMonitor()

    # 更新配置
    smart_monitor.config.update({
        'scan_interval': scan_interval,
        'min_apr': min_apr,
        'alert_threshold_apr': alert_apr,
        'auto_analysis': not no_analysis
    })

    asyncio.run(smart_monitor.run_smart_monitoring())


@cli.command()
@click.option('--count', '-c', default=5, help='Number of pools to analyze')
def discover(count):
    """🔍 One-time discovery and analysis of new pools"""

    smart_monitor = SmartPoolMonitor()

    async def run_discovery():
        console.print(f"[bold blue]🔍 Discovering and analyzing top {count} pools...[/bold blue]")

        # 扫描池子
        new_alerts = await smart_monitor.scan_for_new_pools()

        if new_alerts:
            console.print(f"[green]Found {len(new_alerts)} high-APR pools![/green]")

            # 分析前几个
            for i, alert in enumerate(new_alerts[:count]):
                console.print(f"[blue]Analyzing {i+1}/{min(count, len(new_alerts))}: {alert.pair_name}...[/blue]")
                analyzed = await smart_monitor.analyze_pool_with_agent(alert)
                smart_monitor.new_pool_alerts.append(analyzed)

        # 显示结果
        layout = smart_monitor.create_layout()
        console.print(layout)

    asyncio.run(run_discovery())


@cli.command()
@click.option('--detailed', '-d', is_flag=True, help='Show detailed agent reasoning')
@click.option('--count', '-c', default=3, help='Number of pools to analyze')
def analyze(detailed, count):
    """🧠 Analyze pools and show AI reasoning process"""

    smart_monitor = SmartPoolMonitor()

    async def run_detailed_analysis():
        console.print(f"[bold blue]🧠 Detailed AI Analysis Mode[/bold blue]")
        console.print(f"[dim]Analyzing {count} pools with detailed reasoning...[/dim]\n")

        # 扫描池子
        new_alerts = await smart_monitor.scan_for_new_pools()

        if new_alerts:
            console.print(f"[green]Found {len(new_alerts)} high-APR pools![/green]\n")

            # 分析前几个并显示详细推理
            for i, alert in enumerate(new_alerts[:count]):
                console.print(f"[bold blue]📊 Analyzing Pool {i+1}/{min(count, len(new_alerts))}[/bold blue]")

                # 执行分析
                analyzed = await smart_monitor.analyze_pool_with_agent(alert)

                # 显示详细推理过程
                if detailed and analyzed.agent_reasoning:
                    smart_monitor.show_agent_reasoning(analyzed)
                else:
                    # 显示简化结果
                    console.print(f"[cyan]Pool: {analyzed.pair_name}[/cyan]")
                    console.print(f"[green]Strategy: {analyzed.strategy_recommendation}[/green]")
                    console.print(f"[yellow]Risk: {analyzed.risk_level}[/yellow]")
                    if analyzed.entry_range:
                        console.print(f"[blue]Entry Range: {analyzed.entry_range}[/blue]")

                console.print()  # 空行分隔
        else:
            console.print("[yellow]No high-APR pools found at this time[/yellow]")

    asyncio.run(run_detailed_analysis())


@cli.command()
def config():
    """⚙️ Show current configuration"""
    smart_monitor = SmartPoolMonitor()

    config_panel = Panel.fit(
        f"""[bold cyan]🤖 Smart Monitor Configuration[/bold cyan]

[yellow]Scanning Settings:[/yellow]
• Min TVL: ${smart_monitor.config['min_tvl']:,}
• Min APR: {smart_monitor.config['min_apr']}%
• Alert APR Threshold: {smart_monitor.config['alert_threshold_apr']}%
• Scan Interval: {smart_monitor.config['scan_interval']} seconds
• Max Pools per Chain: {smart_monitor.config['max_pools_per_chain']}

[yellow]AI Analysis:[/yellow]
• Auto Analysis: {'✅ Enabled' if smart_monitor.config['auto_analysis'] else '❌ Disabled'}
• Max Risk Score: {smart_monitor.config['max_risk_score']}/10

[yellow]Supported Chains:[/yellow]
• 🟡 BSC (PancakeSwap V3)
• 🟣 Solana (Meteora)
        """,
        title="⚙️ Configuration",
        border_style="cyan"
    )

    console.print(config_panel)


if __name__ == "__main__":
    cli()

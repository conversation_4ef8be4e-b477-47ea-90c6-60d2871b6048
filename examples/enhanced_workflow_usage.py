import asyncio
from src.workflows.enhanced_lp_monitoring_workflow import EnhancedLPMonitoringWorkflow
from src.agents.enhanced_planner_with_tools import EnhancedPlannerAgent

def sync_monitor_example():
    workflow = EnhancedLPMonitoringWorkflow()
    result = workflow.monitor_pools_with_tools(['bsc', 'solana'])
    print("同步监控结果：", result)

async def async_monitor_example():
    workflow = EnhancedLPMonitoringWorkflow()
    result = await workflow.monitor_pools_with_tools_async(['bsc', 'solana'])
    print("异步监控结果：", result)

def sync_planner_example():
    planner = EnhancedPlannerAgent()
    plan = planner.create_comprehensive_plan(capital=100000)
    print("同步规划结果：", plan)

async def async_planner_example():
    planner = EnhancedPlannerAgent()
    plan = await planner.create_comprehensive_plan_async(capital=100000)
    print("异步规划结果：", plan)

if __name__ == "__main__":
    print("=== 增强版 LP 监控工作流（同步） ===")
    sync_monitor_example()
    print("\n=== 增强版 LP 监控工作流（异步） ===")
    asyncio.run(async_monitor_example())

    print("\n=== 增强版综合规划 Agent（同步） ===")
    sync_planner_example()
    print("\n=== 增强版综合规划 Agent（异步） ===")
    asyncio.run(async_planner_example())

    print("\n=== 对比传统方式优势 ===")
    print("1. Agent 可主动调用工具，获取实时链上数据和评分。")
    print("2. 支持同步与异步，适配不同业务场景。")
    print("3. 过程透明、易于调试和扩展。")

#!/usr/bin/env python3
"""
Dy-Flow AI Agent 主程序入口
整合所有组件，启动 AI Agent 系统
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Optional
import structlog
from rich.console import Console
from aiohttp import web
import json

# 添加源代码路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.agno_scheduler import AGNOScheduler
from src.ui.dashboard import Dashboard
from src.data.bsc_provider import BSCProvider
from src.data.solana_provider import SolanaProvider
from src.data.aggregator import DataAggregator
from src.agents import (
    PoolHunter, RiskSentinel, Scorer, Planner, 
    Executor, EarningsAuditor, CLIReporter
)
from src.utils.config import Config
from src.utils.exceptions import DyFlowException

# 配置日志
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)


class DyFlowApplication:
    """Dy-Flow 主应用程序"""
    
    def __init__(self):
        self.console = Console()
        self.config: Optional[Config] = None
        self.scheduler: Optional[AGNOScheduler] = None
        self.dashboard: Optional[Dashboard] = None
        self.data_aggregator: Optional[DataAggregator] = None
        self.agents = {}
        self.data_providers = {}
        self.is_running = False
        self.health_server = None
        self.health_app = None
        
    async def initialize(self):
        """初始化系统组件"""
        try:
            logger.info("dyflow_system_initializing")
            
            # 加载配置
            await self._load_config()
            
            # 初始化数据提供者
            await self._initialize_data_providers()
            
            # 初始化数据聚合器
            await self._initialize_data_aggregator()
            
            # 初始化所有 Agent
            await self._initialize_agents()
            
            # 初始化调度器
            await self._initialize_scheduler()
            
            # 初始化仪表板
            await self._initialize_dashboard()
            
            # 设置健康检查服务器
            await self._setup_health_server()
            
            logger.info("dyflow_system_initialized")
            
        except Exception as e:
            logger.error("dyflow_system_init_failed", error=str(e))
            raise DyFlowException(f"系统初始化失败: {e}")
    
    async def _load_config(self):
        """加载配置"""
        try:
            config_paths = [
                "config/default.yaml",
                "config/networks.yaml",
                "config/strategies.yaml",
                "config/pools.yaml"
            ]
            
            self.config = Config()
            for config_path in config_paths:
                if Path(config_path).exists():
                    await self.config.load_from_file(config_path)
            
            logger.info("config_loaded")
            
        except Exception as e:
            logger.error("config_load_failed", error=str(e))
            raise
    
    async def _initialize_data_providers(self):
        """初始化数据提供者"""
        try:
            # BSC 提供者
            bsc_provider = BSCProvider(self.config, 'bsc')
            await bsc_provider.initialize()
            self.data_providers['bsc'] = bsc_provider
            
            # Solana 提供者
            solana_provider = SolanaProvider(self.config, 'solana')
            await solana_provider.initialize()
            self.data_providers['solana'] = solana_provider
            
            logger.info("data_providers_initialized", 
                       providers=list(self.data_providers.keys()))
            
        except Exception as e:
            logger.error("data_providers_init_failed", error=str(e))
            raise
    
    async def _initialize_data_aggregator(self):
        """初始化数据聚合器"""
        try:
            self.data_aggregator = DataAggregator(
                self.config, 
                list(self.data_providers.values())
            )
            await self.data_aggregator.initialize()
            
            logger.info("data_aggregator_initialized")
            
        except Exception as e:
            logger.error("data_aggregator_init_failed", error=str(e))
            raise
    
    async def _initialize_agents(self):
        """初始化所有 Agent"""
        try:
            # 池子猎手
            pool_hunter = PoolHunter(self.config, self.data_aggregator)
            await pool_hunter.initialize()
            self.agents['pool_hunter'] = pool_hunter
            
            # 风险哨兵
            risk_sentinel = RiskSentinel(self.config, self.data_aggregator)
            await risk_sentinel.initialize()
            self.agents['risk_sentinel'] = risk_sentinel
            
            # 评分器
            scorer = Scorer(self.config, self.data_aggregator)
            await scorer.initialize()
            self.agents['scorer'] = scorer
            
            # 规划器
            planner = Planner(self.config, self.data_aggregator)
            await planner.initialize()
            self.agents['planner'] = planner
            
            # 执行器
            executor = Executor(self.config, self.data_aggregator)
            await executor.initialize()
            self.agents['executor'] = executor
            
            # 收益审计员
            earnings_auditor = EarningsAuditor(self.config, self.data_aggregator)
            await earnings_auditor.initialize()
            self.agents['earnings_auditor'] = earnings_auditor
            
            # CLI 报告器
            cli_reporter = CLIReporter(self.config, self.data_aggregator)
            await cli_reporter.initialize()
            self.agents['cli_reporter'] = cli_reporter
            
            logger.info("agents_initialized", 
                       agents=list(self.agents.keys()))
            
        except Exception as e:
            logger.error("agents_init_failed", error=str(e))
            raise
    
    async def _initialize_scheduler(self):
        """初始化调度器"""
        try:
            self.scheduler = AGNOScheduler(self.config)
            
            # 注册所有 Agent
            for agent_name, agent in self.agents.items():
                await self.scheduler.register_agent(agent)
            
            await self.scheduler.initialize()
            
            logger.info("scheduler_initialized")
            
        except Exception as e:
            logger.error("scheduler_init_failed", error=str(e))
            raise
    
    async def _initialize_dashboard(self):
        """初始化仪表板"""
        try:
            dashboard_config = {
                'dashboard': {
                    'update_interval': 5,
                    'max_positions': 5,
                    'max_opportunities': 10
                }
            }
            
            self.dashboard = Dashboard(dashboard_config)
            await self.dashboard.initialize()
            
            logger.info("dashboard_initialized")
            
        except Exception as e:
            logger.error("dashboard_init_failed", error=str(e))
            raise

    async def _setup_health_server(self):
        """设置健康检查服务器"""
        try:
            self.health_app = web.Application()
            
            # 健康检查端点
            self.health_app.router.add_get('/health', self._health_check_handler)
            self.health_app.router.add_get('/status', self._status_handler)
            self.health_app.router.add_get('/metrics', self._metrics_handler)
            
            # 启动服务器
            runner = web.AppRunner(self.health_app)
            await runner.setup()
            
            site = web.TCPSite(runner, '0.0.0.0', 8080)
            await site.start()
            
            logger.info("health_server_started", port=8080)
            
        except Exception as e:
            logger.error("health_server_setup_failed", error=str(e))
            raise

    async def _health_check_handler(self, request):
        """健康检查处理器"""
        try:
            health_status = {
                "status": "healthy" if self.is_running else "starting",
                "timestamp": asyncio.get_event_loop().time(),
                "components": {
                    "scheduler": self.scheduler is not None and hasattr(self.scheduler, 'is_running') and self.scheduler.is_running,
                    "data_aggregator": self.data_aggregator is not None,
                    "agents": len(self.agents),
                    "data_providers": len(self.data_providers)
                }
            }
            
            return web.json_response(health_status)
            
        except Exception as e:
            logger.error("health_check_failed", error=str(e))
            return web.json_response(
                {"status": "unhealthy", "error": str(e)},
                status=500
            )

    async def _status_handler(self, request):
        """状态信息处理器"""
        try:
            status_info = {
                "system": {
                    "running": self.is_running,
                    "uptime": asyncio.get_event_loop().time() if self.is_running else 0
                },
                "agents": {name: "active" for name in self.agents.keys()},
                "data_providers": {name: "connected" for name in self.data_providers.keys()},
                "scheduler": {
                    "active": self.scheduler is not None,
                    "jobs": len(getattr(self.scheduler, 'jobs', [])) if self.scheduler else 0
                }
            }
            
            return web.json_response(status_info)
            
        except Exception as e:
            logger.error("status_handler_failed", error=str(e))
            return web.json_response(
                {"error": str(e)},
                status=500
            )

    async def _metrics_handler(self, request):
        """Prometheus 格式指标处理器"""
        try:
            metrics = []
            
            # 系统指标
            metrics.append(f'dyflow_system_running {1 if self.is_running else 0}')
            metrics.append(f'dyflow_agents_count {len(self.agents)}')
            metrics.append(f'dyflow_data_providers_count {len(self.data_providers)}')
            
            # Agent 指标
            for agent_name in self.agents.keys():
                metrics.append(f'dyflow_agent_active{{agent="{agent_name}"}} 1')
            
            metrics_text = '\n'.join(metrics) + '\n'
            
            return web.Response(
                text=metrics_text,
                content_type='text/plain; version=0.0.4; charset=utf-8'
            )
            
        except Exception as e:
            logger.error("metrics_handler_failed", error=str(e))
            return web.Response(
                text=f'# ERROR: {str(e)}\n',
                content_type='text/plain',
                status=500
            )
    
    async def start(self):
        """启动系统"""
        if self.is_running:
            return
        
        try:
            logger.info("dyflow_system_starting")
            self.is_running = True
            
            # 显示启动信息
            self.dashboard.show_startup_message()
            
            # 启动数据聚合器
            await self.data_aggregator.start()
            
            # 启动调度器
            await self.scheduler.start()
            
            # 启动仪表板
            await self.dashboard.start()
            
            # 设置数据更新任务
            asyncio.create_task(self._data_update_task())
            
            logger.info("dyflow_system_started")
            
        except Exception as e:
            logger.error("dyflow_system_start_failed", error=str(e))
            await self.stop()
            raise
    
    async def stop(self):
        """停止系统"""
        if not self.is_running:
            return
        
        try:
            logger.info("dyflow_system_stopping")
            self.is_running = False
            
            # 停止仪表板
            if self.dashboard:
                await self.dashboard.stop()
            
            # 停止调度器
            if self.scheduler:
                await self.scheduler.stop()
            
            # 停止数据聚合器
            if self.data_aggregator:
                await self.data_aggregator.stop()
            
            # 停止所有数据提供者
            for provider in self.data_providers.values():
                await provider.cleanup()
            
            # 显示关闭信息
            if self.dashboard:
                self.dashboard.show_shutdown_message()
            
            logger.info("dyflow_system_stopped")
            
        except Exception as e:
            logger.error("dyflow_system_stop_failed", error=str(e))
    
    async def _data_update_task(self):
        """数据更新任务"""
        while self.is_running:
            try:
                # 获取最新池子数据
                pool_metrics = await self.data_aggregator.get_all_pool_metrics()
                
                # 更新仪表板
                if self.dashboard:
                    await self.dashboard.update_pool_data(pool_metrics)
                
                # 等待下次更新
                await asyncio.sleep(30)  # 每30秒更新一次
                
            except Exception as e:
                logger.error("data_update_task_failed", error=str(e))
                await asyncio.sleep(60)  # 出错时等待1分钟
    
    async def run(self):
        """运行主循环"""
        try:
            await self.initialize()
            await self.start()
            
            # 等待中断信号
            await self._wait_for_shutdown()
            
        except KeyboardInterrupt:
            logger.info("received_keyboard_interrupt")
        except Exception as e:
            logger.error("dyflow_main_run_failed", error=str(e))
            raise
        finally:
            await self.stop()
    
    async def _wait_for_shutdown(self):
        """等待关闭信号"""
        stop_event = asyncio.Event()
        
        def signal_handler():
            logger.info("shutdown_signal_received")
            stop_event.set()
        
        # 注册信号处理器
        if sys.platform != 'win32':
            loop = asyncio.get_running_loop()
            for sig in (signal.SIGTERM, signal.SIGINT):
                loop.add_signal_handler(sig, signal_handler)
        
        try:
            await stop_event.wait()
        except KeyboardInterrupt:
            logger.info("keyboard_interrupt_received")


async def main():
    """主函数"""
    console = Console()
    
    try:
        console.print("🚀 启动 Dy-Flow AI Agent 系统...\n", style="bold blue")
        
        app = DyFlowApplication()
        await app.run()
        
    except KeyboardInterrupt:
        console.print("\n⚡ 收到中断信号，正在关闭系统...", style="bold yellow")
    except Exception as e:
        console.print(f"\n❌ 系统运行失败: {e}", style="bold red")
        logger.error("main_failed", error=str(e))
        sys.exit(1)
    
    console.print("✅ 系统已安全关闭", style="bold green")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚡ 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)
# Decision Log

本文件記錄架構和實現決策，使用列表格式。

最後更新: 2025-06-04 12:17:00

## 決策記錄

### [2025-06-04 12:17:00] - 全面引入 Agno Framework 進行 AI 能力增強

**決策**
決定全面採用 Agno Framework (AI Agent Framework) 來增強 Dy-Flow v3 系統的 AI 能力，包括智能決策、知識庫集成和多代理協作。

**理由**
1.  **先進性與成熟度**: Agno 是一個現代化的 AI Agent 開發框架，具有輕量級、高性能、企業級的特性。官方文檔豐富 (3326+ 代碼示例)，Trust Score 高達 9.5。
2.  **核心 AI 功能**:
    *   **多模型支持**: 無縫集成 OpenAI (gpt-4o), Anthropic (claude-3-sonnet), 本地模型 (Ollama Llama3.1) 等。
    *   **推理能力**: 內建 `reasoning=True` 選項及 `ReasoningTools`, `ThinkingTools`，適用於複雜決策場景。
    *   **知識庫集成 (Agentic RAG)**: 支持 `PDFUrlKnowledgeBase`, `UrlKnowledge` 與 `PgVector` 等向量數據庫結合，實現智能知識檢索與過濾。
    *   **多代理系統**: `Team` 模塊支持 `coordinate`, `collaborate`, `sequential` 等多種協作模式。
3.  **與 Dy-Flow v3 的兼容性**:
    *   可通過 `AgnoWrapperAgent` 模式與現有 `BaseAgent` 架構平滑集成。
    *   支持異步操作 (`async/await`)，符合 Dy-Flow v3 的性能要求。
4.  **企業級特性**:
    *   **持久化存儲**: 支持 `SqliteStorage` 和 `PostgresStorage` 進行會話管理。
    *   **結構化輸出**: 通過 Pydantic `response_model` 強制輸出格式。
    *   **用戶界面**: `Playground` 提供便捷的 Web UI 進行代理管理和測試。
    *   **調試與監控**: 提供 `debug_mode`, `show_tool_calls`, `stream_intermediate_steps` 等調試功能。
5.  **漸進式遷移策略**: 允許逐步將現有 Agent 遷移或增強至 Agno，降低集成風險。

**影響範圍**
-   核心 Agent (Scorer, Risk Sentinel, Planner) 將逐步使用 Agno 進行重構或增強。
-   將引入新的協作模式 (多代理團隊) 和知識管理方式 (Agentic RAG)。
-   開發流程中將包含 Agno Agent 的設計、測試和調優。

**風險控制**
-   保持 `BaseAgent` 兼容性，確保 Agno 集成失敗時可回退。
-   分階段實施，優先增強價值最高的 Agent。
-   充分利用 Agno 的調試工具和文檔。

### [2025-06-04 12:17:00] - Agno 框架分階段實施策略

**決策**
Agno 框架的集成將分階段進行，以確保穩定性和逐步釋放價值。

**理由**
-   降低一次性大規模重構的風險。
-   允許團隊逐步熟悉 Agno 框架。
-   優先解決當前系統的痛點，快速驗證 Agno 的價值。

**實施階段與內容**
1.  **第一階段：核心 Agent 增強 (本週)**
    *   **Scorer Agent 智能化升級**: 使用 Agno 的 `ReasoningTools` 和結構化輸出。
    *   **Risk Sentinel 推理增強**: 利用 Agno 進行複雜風險場景分析。
    *   **Planner 決策智能化**: 引入 Agno 的推理和初步知識庫能力。
2.  **第二階段：多代理協作 (下週)**
    *   **Scout-Scorer-Planner 團隊**: 使用 `agno.team.Team` 構建協調型多代理團隊。
    *   **智能決策流程測試**: 驗證團隊協作效率和決策質量。
3.  **第三階段：知識庫建設 (2週內)**
    *   **DeFi 知識庫構建**: 利用 `PDFUrlKnowledgeBase`, `UrlKnowledge` 和 `PgVector` 建立 DeFi 領域的 Agentic RAG。
    *   **歷史數據知識庫**: 探索將歷史交易和策略績效數據整合為知識。
4.  **第四階段：UI 和監控 (1個月內)**
    *   **Agno Playground 部署**: 作為開發、測試和管理界面。
    *   **監控和調試系統集成**: 利用 Agno 的監控特性。

**衡量指標**
-   每個階段完成後進行評估，包括響應時間、決策準確率、推理質量等。
-   業務指標如策略績效、風險控制能力的提升。

### [2025-06-04 12:17:00] - Agno 集成技術選型

**決策**
在 Agno 框架集成過程中，針對特定功能採用以下技術選型。

**理由**
-   基於 Agno 官方推薦和 Dy-Flow v3 的現有需求。

**具體選型**
-   **主力 LLM 模型**: `OpenAIChat(id="gpt-4o")` 和 `Claude(id="claude-3-7-sonnet-latest")`。
-   **本地備用模型**: `Ollama(id="llama3.1:8b")`。
-   **向量數據庫**: `PgVector` (與現有 PostgreSQL 兼容性好)。
-   **Embedding 模型**: `OpenAIEmbedder(id="text-embedding-3-small")`。
-   **知識源**: `PDFUrlKnowledgeBase` 和 `UrlKnowledge`。
-   **持久化存儲**: 初期可使用 `SqliteStorage`，生產環境考慮 `PostgresStorage`。
-   **結構化輸出**: 全面採用 Pydantic `response_model`。

---
*(舊有決策記錄保持不變)*

### [2025-06-03 11:13:00] - 实施评分算法优化升级 (ScorerV2Agent)
...

### [2025-05-30 20:24:00] - Dy-Flow v3 完整架構整合決策
...

### [2025-05-30 20:24:00] - 確認 Agno Framework 作為核心調度引擎
*(註：此處 "Agno Framework" 指的是項目早期對一個通用調度概念的稱呼，與現在引入的 Agno (AI Agent Framework) 是不同的。現有 Agno 框架將增強或替換部分原有規劃中的 "agno_scheduler.py" 的智能決策部分，而非調度本身。)*
...

### [2025-05-30 20:24:00] - 三大核心策略架構確立
...

### [2025-05-30 20:24:00] - 企業級監控和部署架構決策
...

### [2025-05-29 19:16] - v3 配置系統架構決策
...

### [2025-05-29 19:16] - 工具函數和數據庫設計決策
...

### [2025-05-29 18:44:10] - 採用 Dy-Flow v3 架構作為重構基礎
...

### [2025-05-29 18:44:10] - 採用 Memory Bank 系統進行項目上下文管理
...

### [2025-05-29 18:44:10] - 保持現有項目結構穩定性
...

### [2025-06-05 11:29:00] - PoolScannerTool 工具转换架构决策

**决策**
创建 PoolScannerTool 作为 Agno Framework 工具转换的第一个标准化组件，整合现有的 BSC 和 Meteora 扫描功能。

**理由**
1. **工具转换需求**: 用户明确要求恢复被中断的 PoolScannerTool 创建工作，这是 Agno Framework 重构项目阶段一的核心任务。
2. **代码复用与整合**: 现有的 `src/agents/scout/bsc.py` 和 `src/agents/scout/meteora.py` 包含成熟的扫描逻辑，需要整合为统一的工具接口。
3. **Agno Framework 标准化**: 采用 `agno.sdk.Tool` 基类，提供标准的异步接口和错误处理机制。
4. **双链支持**: 统一 BSC PancakeSwap 和 Solana Meteora 的数据获取和处理逻辑。

**技术选型**
- **基础框架**: 继承 `agno.sdk.Tool`，提供 `async def run()` 标准接口
- **HTTP 客户端**: 使用 `aiohttp` 进行异步 API 调用
- **数据结构**: 统一的池子数据格式，包含 TVL、交易量、费率等关键指标
- **错误处理**: 多层级异常捕获，优雅降级机制
- **日志记录**: 使用 `structlog` 进行结构化日志

**影响范围**
- 新建 `agno_tools/` 包，作为 Agno 工具集的标准目录
- 为后续工具转换建立了架构模式和代码标准
- 提供了 BSC 和 Solana 双链数据扫描的统一接口

**风险控制**
- **向后兼容**: 提供 `PoolScannerToolSync` 同步版本，支持非 Agno 环境
- **优雅降级**: Agno Framework 不可用时自动切换到基础实现
- **数据验证**: 完善的输入验证和异常处理机制

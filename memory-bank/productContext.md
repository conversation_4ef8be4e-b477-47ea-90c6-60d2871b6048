# Product Context - Dy-Flow v3

本文件提供項目的高層概述，基於 Dy-Flow v3 架構文檔和項目簡介。

最後更新: 2025-06-04 12:17:00

## 項目目標

### 核心使命
"交付一個 24/7 自動做市＋動態避險的多鏈錢包管家，在 BSC PancakeSwap 與 Solana Meteora 上持續撈費，同時將風險封頂。"
**Agno 增強**: 通過 Agno 框架的 AI 能力，提升自動化水平、決策智能和風險識別的精準度。

### 核心 KPI
- **年化費率目標**：≥ 25% (Δ-Neutral) / ≥ 50% (Ladder)
- **最大單日回撤**：< 8%
- **風險退出耗時**：≤ 2 分鐘
- **執行錯誤率**：< 0.5%/天
- **Fatal error 告警**：0 次/天
**Agno 增強指標**:
- **AI 決策準確率**: > 90%
- **知識庫輔助決策覆蓋率**: > 70% 複雜場景
- **多代理協同任務成功率**: > 95%

## 關鍵功能

### 三大使用場景
1.  **A. 藍籌對沖** (BTCB-BUSD、ETH-USDT)
    *   策略：S-1 delta_neutral_lp
    *   目標：賺手續費又怕方向風險
    *   **Agno 增強**: 更精準的市場判斷和對沖時機選擇。
2.  **B. Meme 火箭** (PEPE2.0-BUSD、JARGON-SOL)
    *   策略：S-2 ladder_single_sided
    *   目標：捕捉 10× 爆衝機會
    *   **Agno 增強**: 結合知識庫和推理能力，識別潛力 Meme 並動態調整策略。
3.  **C. 穩定池躺平** (USDT-USDC、DAI-BUSD)
    *   策略：S-3 passive_high_tvl
    *   目標：穩定年化 5–10%
    *   **Agno 增強**: 通過 Agentic RAG 監控協議風險，確保資金安全。

### 核心策略
- **S-1 Δ-Neutral LP**：LP + Perp 空單對沖
- **S-2 Ladder SS-LP**：多層樓梯單邊 LP，trailing 移區
- **S-3 Passive High-TVL**：大池被動持倉
**Agno 增強**: 所有策略的參數配置、執行時機和風險管理都將由 Agno 增強的 Agent (Planner, Risk Sentinel) 進行優化。

## 總體架構

### Agent DAG 架構
```
Data Layer → Scorer (Agno) → Planner (Agno) → Executor → Auditor
     ↓         ↓               ↓ (Agno Team)   ↓        ↓
  Scout-BSC   Risk Sentinel (Agno) Plans      LP/Hedge   Reports
  Scout-SOL  (Agno Knowledge)                 Manager
```
**Agno 集成點**: Scorer, Planner, Risk Sentinel 將優先集成 Agno。Scout-Scorer-Planner 將組成 Agno 多代理團隊。

### 核心模組
- **Scout 層**：BSC (5min) + Meteora (1min) 數據收集
- **Scorer (Agno 增強)**：基於 Agno 推理的六因子動態評分系統。
- **Risk Sentinel (Agno 增強)**：利用 Agno 推理和知識庫進行1分鐘實時風險監控和預警。
- **Planner (Agno 增強)**：基於 Agno 推理、知識庫和多代理協作的規則決策與策略規劃。
- **Executor**：LP + Hedge 操作執行
- **Auditor**：6小時收益審計

### 技術棧
- **後端**：Python + FastAPI + Supabase/Postgres
- **AI Agent 框架**: **Agno Framework** (核心 AI 能力)
- **調度**：現有 Agno DAG 系統 (agno_scheduler.py) + Agno Framework 內部的 Agent 執行邏輯
- **區塊鏈**：Web3.py (BSC) + Solana SDK
- **前端**：React Dashboard + CLI Reporter + **Agno Playground (開發/管理 UI)**
- **部署**：Docker Compose + Nginx

### AI 能力與 LLM 使用邊界 (Agno Framework 主導)
- **規則決策（常態）**：由 Agno Agent 的 `instructions` 和 `reasoning` 模塊主導，基於閾值/因子，並可通過結構化輸出保證。
- **複雜決策與例外診斷**：Agno Agent 利用 `ReasoningTools` 和 `ThinkingTools` 處理，`Team` 模式協同處理複雜任務。
- **知識庫支持 (Agentic RAG)**：Agno Agent 通過 `knowledge` 參數集成 DeFi 知識庫，輔助決策。
- **LLM 模型選擇**: Agno 支持 OpenAI GPT-4o, Anthropic Claude-3 Sonnet, Ollama Llama3.1 等。
- **Critic/報表**：部分可由 Agno Agent 生成結構化分析報告，核心統計數據仍依賴系統。

### 風控機制 (Agno 增強)
- **價格閾值**：k_drop=2.5, min_drop_pct=0.06 (Agno Risk Sentinel 可動態調整或提供更優建議)
- **TVL 快跌**：30分鐘跌幅 >40% 觸發退出 (Agno Risk Sentinel 監控)
- **Hedge 失效**：失敗≥3次或 delta>30% 觸發警報 (Agno Risk Sentinel 監控和分析)
**Agno 增強**: Risk Sentinel Agent 將利用 Agno 的推理能力，結合多種數據源和知識庫，提供更全面的風險評估和預警。

## Dy-Flow v3 架構升級 (2025-05-30 更新)
*(此部分為先前內容，保持不變，僅作上下文參考)*
```mermaid
flowchart LR
  subgraph Data
    BSCScout[Scout-BSC<br>5 min] --> PoolsRaw
    SolScout[Scout-Meteora<br>1 min] --> PoolsRaw
  end

  PoolsRaw --> Scorer[Scorer<br>因子打分]
  Scorer --> PoolsRanked

  Risk[Risk-Sentinel<br>1 min] --> Alerts

  PoolsRanked ---> Planner[Planner<br>規則]
  Alerts ------> Planner
  Planner -. needs_llm .-> LLM[Planner-LLM<br>(例外)]
  Planner --> Plans
  Plans --> Executor[Executor]
  Executor -->|LP ops| LPManager
  Executor -->|Hedge ops| HedgeManager
  Executor --> Tx

  Tx --> Auditor[Auditor 6 h]
  Auditor --> DailyRpt

  subgraph UI
    CLI[CLI Reporter 10 s]
    Dash[React Dashboard]
  end
  PoolsRanked -->|Sub| CLI & Dash
  DailyRpt --> CLI & Dash
  Alerts --> CLI & Dash
```

### Agent 節點完整定義
| Node | Kind | Schedule/Needs | 產出 |
|------|------|----------------|------|
| scout_bsc | tool | every 5m | pools_raw_bsc |
| scout_meteora | tool | every 1m | pools_raw_sol |
| scorer | tool | needs: pools_raw_* | pools_scored |
| risk_sentinel | tool | every 1m | risk_alerts |
| planner | tool | needs: pools_scored, risk_alerts | plans (flag needs_llm) |
| planner_llm | llm | needs: plans?needs_llm | plans_fixed |
| executor | tool | needs: plans / plans_fixed | tx_receipts |
| auditor | tool | every 6h | daily_report |
| cli_reporter | tool | every 10s | — |

### 核心數據類別 (Dataclass)
```python
from dataclasses import dataclass
from typing import Literal # Added for Plan.action
from datetime import datetime # Added for PoolRaw.created_at

@dataclass
class PoolRaw:
    id: str
    chain: str
    tvl_usd: float
    fee24h: float
    fee_tvl: float
    created_at: datetime

@dataclass
class PoolScore:
    id: str
    score: float
    hedgeable: bool

@dataclass
class Plan:
    pool_id: str
    strategy: str
    action: Literal["enter","exit","rebalance"]
    params: dict
    needs_llm: bool = False
```

### 資料庫 ER 模型 (Supabase/Postgres)
- **pools**: 池子基礎資料和評分
- **positions**: LP 持倉記錄
- **hedges**: 對沖操作記錄
- **txs**: 交易記錄
- **earnings**: 收益記錄
- **risk_events**: 風險事件記錄
- **user_filter**: 用戶過濾規則

### 風控公式升級
```python
# 價格閾值 (升級版)
# if abs(P_now - P_60m)/P_60m >= max(k_drop*ATR14, min_drop_pct):
#     alert = EXIT if not hedgeable else HEDGE

# TVL 快跌檢測
# if TVL_now / TVL_30m < 0.6:
#     alert = EXIT

# Hedge 失效檢測
# if hedge_fail >= 3 or delta_pct > 0.3:
#     alert = HEDGE_RISK
```
**預設參數**: k_drop=2.5, min_drop_pct=0.06

### 六週開發里程碑
| 週次 | 交付內容 |
|------|----------|
| 1 | Scout + DB schema + pytest baseline |
| 2 | Scorer + CLI MVP |
| 3 | Ladder SS-LP 全鏈路 + Risk Sentinel |
| 4 | Delta-Neutral + Hedge(Binance) |
| 5 | Auditor + Dashboard Overview |
| 6 | 回測驗證 + Prometheus metrics + 文檔鎖版 |

**[2025-05-30 20:22:45] - 根據完整 Dy-Flow v3 架構文檔更新產品上下文**

**[2025-06-04 12:17:00] - Agno Framework 全面整合，提升產品 AI 核心能力**

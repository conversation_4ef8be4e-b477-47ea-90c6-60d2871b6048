# System Patterns

本文件記錄項目中使用的重複模式和標準。可選文件，建議隨項目發展更新。

最後更新: 2025-06-04 12:17:00

## 編碼模式

### Agent 模組模式 (Dy-Flow v3 BaseAgent)
```python
# 標準 Agent 結構 (Dy-Flow v3)
from dataclasses import dataclass
from typing import Any, Literal
from datetime import datetime

@dataclass
class AgentResult:
    data: Any
    timestamp: datetime
    status: Literal["success", "error", "warning"]
    metadata: dict

class BaseAgent: # Dy-Flow v3 基類
    def __init__(self, config: dict, agent_id: str, agent_name: str, agent_role: str):
        self.config = config
        self.agent_id = agent_id
        self.agent_name = agent_name
        self.agent_role = agent_role
        # ... 其他初始化 ...
        
    async def initialize(self) -> None:
        # 初始化資源
        pass

    async def execute(self) -> AgentResult: # 或 Dict[str, Any]
        # 統一的執行介面
        pass

    async def cleanup(self) -> None:
        # 清理資源
        pass
```

### 數據類別模式 (Dy-Flow v3)
```python
from dataclasses import dataclass
from typing import Literal
from datetime import datetime

@dataclass
class PoolRaw:
    id: str
    chain: str
    tvl_usd: float
    fee24h: float
    fee_tvl: float
    created_at: datetime

@dataclass  
class PoolScore:
    id: str
    score: float
    hedgeable: bool

@dataclass
class Plan:
    pool_id: str
    strategy: str
    action: Literal["enter", "exit", "rebalance"]
    params: dict
    needs_llm: bool = False # 舊模式，將被 Agno 推理替代或增強
```

### 錯誤處理模式 (Dy-Flow v3)
```python
# 統一錯誤處理 (示例)
# try:
#     result = await agent.execute()
# except Web3Exception as e:
#     logger.error(f"Blockchain error: {e}")
#     # 重試邏輯
# except APIException as e:
#     logger.error(f"API error: {e}")
#     # 降級處理
```

## Agno Framework 模式 (新增)

### Agno Agent 初始化模式
```python
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.models.anthropic import Claude
from agno.models.ollama import Ollama
from agno.tools.reasoning import ReasoningTools
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase # 示例知識庫
# from agno.vectordb.pgvector import PgVector # 示例向量數據庫
# from agno.embedder.openai import OpenAIEmbedder # 示例 Embedder

# 1. 基礎 Agent
basic_agent = Agent(
    model=OpenAIChat(id="gpt-4o"),
    instructions=["Your primary instruction."],
    markdown=True, # 推薦用於 Playground
    # tools=[YourCustomTool()], # 可選
)

# 2. 推理 Agent
reasoning_agent = Agent(
    model=Claude(id="claude-3-7-sonnet-latest"),
    instructions=["Think step-by-step to solve the problem."],
    tools=[ReasoningTools(add_instructions=True)], # 自動添加推理指令
    reasoning=True, # 啟用內建推理循環
    show_tool_calls=True, # 調試時有用
)

# 3. 知識庫 (Agentic RAG) Agent
# knowledge_base_instance = PDFUrlKnowledgeBase(...) # 需先實例化
# rag_agent = Agent(
#     model=OpenAIChat(id="gpt-4o"),
#     knowledge=knowledge_base_instance,
#     search_knowledge=True, # 啟用知識庫搜索
#     enable_agentic_knowledge_filters=True, # 自動過濾搜索結果
#     instructions=["Answer questions based on the provided documents."],
# )

# 4. Agent 設計最佳實踐
well_designed_agent = Agent(
    name="Specific Agent Name", # 清晰命名
    role="Clear role definition for this agent.", # 明確角色
    agent_id="unique-agent-identifier", # 唯一ID
    model=OpenAIChat(id="gpt-4o"),
    instructions=[
        "Instruction 1: Be very specific.",
        "Instruction 2: Use a structured JSON format for your final answer.",
        "Instruction 3: If a tool fails, try an alternative approach or state inability.",
    ],
    add_datetime_to_instructions=True, # 自動添加時間戳到指令
    add_name_to_instructions=True, # 自動添加 Agent 名稱到指令
    markdown=True,
)
```

### Agno 與 Dy-Flow v3 `BaseAgent` 集成模式
```python
# from agno.agent import Agent # 已 import
# from ..base_agent import BaseAgent # 假設的 Dy-Flow v3 BaseAgent 路徑
# from agno.models.openai import OpenAIChat # 已 import
# from typing import Dict, Any

class AgnoWrapperAgent(BaseAgent): # 繼承 Dy-Flow v3 BaseAgent
    def __init__(self, config: dict, agent_id: str, agent_name: str, agent_role: str):
        super().__init__(config, agent_id, agent_name, agent_role)
        self.agno_agent_instance: Agent = None # Agno Agent 實例

    async def initialize(self) -> None:
        await super().initialize()
        # 初始化 Agno Agent
        self.agno_agent_instance = Agent(
            name=self.agent_name,
            role=self.agent_role,
            agent_id=self.agent_id,
            model=OpenAIChat(id="gpt-4o"), # 示例模型
            instructions=[f"You are {self.agent_name}. Your role is: {self.agent_role}"],
            # tools=[...],
            # knowledge=...,
            # storage=...,
            # reasoning=True,
        )
        # print(f"AgnoWrapperAgent {self.agent_name} initialized with Agno.")

    async def execute(self, query: str) -> Dict[str, Any]: # 假設輸入為 query 字符串
        if not self.agno_agent_instance:
            # print(f"Error: Agno agent for {self.agent_name} not initialized.")
            return {"error": "Agno agent not initialized"}

        # response = await self.agno_agent_instance.arun(query) # 異步運行
        # return {"content": response.content, "tool_calls": response.tool_calls}
        # 實際返回結構需根據 Dy-Flow v3 AgentResult 或 Dict[str, Any] 調整
        # 示例返回，需要根據 BaseAgent 的 execute 簽名調整
        # result_data = {"content": response.content}
        # return AgentResult(data=result_data, timestamp=datetime.now(), status="success", metadata={"tool_calls": response.tool_calls})
        pass # 佔位符

    async def cleanup(self) -> None:
        # print(f"Cleaning up AgnoWrapperAgent {self.agent_name}")
        # Agno Agent 通常不需要顯式 cleanup，但可在此處處理相關資源
        await super().cleanup()
```

### Agno 多代理團隊 (Multi-Agent Teams) 模式
```python
from agno.team.team import Team
# from agno.agent import Agent # 已 import
# from agno.models.openai import OpenAIChat # 已 import
# from agno.tools.reasoning import ReasoningTools # 已 import

# 1. 定義成員 Agents (示例)
# scout_agent = Agent(name="Scout", role="Gather information", model=OpenAIChat(id="gpt-4o-mini"))
# analysis_agent = Agent(name="Analyst", role="Analyze data", model=OpenAIChat(id="gpt-4o"), tools=[ReasoningTools()])

# 2. 創建團隊
# research_team = Team(
#     name="Research Team",
#     mode="coordinate", # 協調模式 (leader 分配任務)
#     # mode="collaborate", # 協作模式 (成員平等合作)
#     # mode="sequential", # 順序模式 (按順序執行)
#     model=OpenAIChat(id="gpt-4o"), # 團隊協調者/領導者使用的模型
#     members=[scout_agent, analysis_agent],
#     tools=[ReasoningTools()], # 團隊領導者可用的工具
#     instructions=["Coordinate the scout and analyst to produce a research report."],
#     enable_agentic_context=True, # 允許成員共享上下文
# )

# 3. 執行團隊任務
# task = "Research the impact of AI on climate change and provide a summary."
# result = await research_team.arun(task, stream_intermediate_steps=True)
# print(result.content)
```

### Agno 知識庫 (Agentic RAG) 模式
```python
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.knowledge.url import UrlKnowledge # 處理網頁
from agno.vectordb.pgvector import PgVector, SearchType # 或其他如 Chroma, LanceDB. Added SearchType
from agno.embedder.openai import OpenAIEmbedder # 或其他 embedding provider
# from agno.agent import Agent # 已 import
# from agno.models.openai import OpenAIChat # 已 import

# 1. 配置 VectorDB 和 Embedder
# pgvector_db = PgVector(
#     table_name="my_documents",
#     db_url="postgresql://user:pass@host:port/dbname", # 替換為您的 DB URL
#     search_type=SearchType.hybrid, # 混合搜索
#     embedder=OpenAIEmbedder(id="text-embedding-3-small"),
# )

# 2. 創建知識庫實例
# pdf_knowledge = PDFUrlKnowledgeBase(
#     urls=["https://example.com/document1.pdf", "https://example.com/document2.pdf"],
#     vector_db=pgvector_db,
# )
# web_knowledge = UrlKnowledge(
#     urls=["https://example.com/article1", "https://example.com/article2"],
#     vector_db=pgvector_db,
# )

# 3. 加載知識 (異步)
# await pdf_knowledge.load(recreate=False) # recreate=True 會重建表
# await web_knowledge.load(recreate=False)

# 4. 創建使用知識庫的 Agent
# knowledge_agent = Agent(
#     name="Knowledgeable Agent",
#     model=OpenAIChat(id="gpt-4o"),
#     knowledge=[pdf_knowledge, web_knowledge], # 可以傳入知識庫列表
#     search_knowledge=True, # 啟用 RAG
#     enable_agentic_knowledge_filters=True, # 讓 Agent 決定是否使用檢索到的知識
#     instructions=["Answer questions based on the provided PDF documents and web articles."],
# )

# 5. 查詢
# response = await knowledge_agent.arun("What is the main topic of document1.pdf?")
# print(response.content)
```

### Agno 結構化輸出模式
```python
from pydantic import BaseModel, Field
from typing import List
# from agno.agent import Agent # 已 import
# from agno.models.openai import OpenAIChat # 已 import

# 1. 定義 Pydantic 模型
class MyStructuredResponse(BaseModel):
    summary: str = Field(..., description="A brief summary of the input.")
    key_points: List[str] = Field(..., description="A list of key points.")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence in the response (0.0 to 1.0).")

# 2. Agent 初始化時指定 response_model
# structured_output_agent = Agent(
#     model=OpenAIChat(id="gpt-4o"), # 確保模型支持 function calling / tool use
#     response_model=MyStructuredResponse,
#     instructions=["Analyze the text and provide a summary, key points, and confidence score."],
# )

# 3. 執行並獲取結構化響應
# response = await structured_output_agent.arun("Some long text to analyze...")
# if response.structured_content:
#     structured_data: MyStructuredResponse = response.structured_content
#     print(f"Summary: {structured_data.summary}")
#     print(f"Key Points: {structured_data.key_points}")
#     print(f"Confidence: {structured_data.confidence_score}")
# else:
#     print(f"Raw content: {response.content}")
```

### Agno 持久化存儲模式
```python
from agno.storage.sqlite import SqliteStorage
from agno.storage.postgres import PostgresStorage
# from agno.agent import Agent # 已 import
# from agno.models.openai import OpenAIChat # 已 import

# 1. 配置存儲後端
# sqlite_store = SqliteStorage(
#     table_name="agent_chat_history",
#     db_file="tmp/my_agent_sessions.db", # 指定 SQLite 文件路徑
#     auto_upgrade_schema=True, # 自動更新表結構
# )
# postgres_store = PostgresStorage(
#     table_name="agent_chat_history_pg",
#     db_url="postgresql://user:pass@host:port/dbname", # 替換為您的 DB URL
#     auto_upgrade_schema=True,
# )

# 2. Agent 初始化時指定 storage
# persistent_agent = Agent(
#     model=OpenAIChat(id="gpt-4o"),
#     storage=sqlite_store, # 或 postgres_store
#     session_id="user123_chat_session_abc", # 指定會話 ID
#     add_history_to_messages=True, # 自動將歷史記錄添加到消息中
#     num_history_responses=5, # 保留最近5條歷史記錄
# )

# 3. 交互 (歷史記錄會自動保存和加載)
# await persistent_agent.arun("Hello, who are you?")
# await persistent_agent.arun("What did I just ask you?") # Agent 應能記住上一問
```

### Agno Playground UI 模式
```python
from agno.playground import Playground, serve_playground_app
# from agno.agent import Agent # 已 import (假設已定義 agent_one, agent_two)
# from agno.models.openai import OpenAIChat # Added for example
# from agno.models.anthropic import Claude # Added for example

# 1. 創建 Playground App
# agent_one = Agent(name="Agent One", model=OpenAIChat(id="gpt-4o-mini"))
# agent_two = Agent(name="Agent Two", model=Claude(id="claude-3-haiku-20240307")) # 注意 Anthropic 模型ID可能變化
# app = Playground(agents=[agent_one, agent_two]).get_app()

# 2. 運行 Playground (通常在 if __name__ == "__main__": 中)
# if __name__ == "__main__":
#     # 假設此代碼在名為 my_playground_app.py 的文件中
#     # serve_playground_app("my_playground_app:app", reload=True, port=8000)
#     # 訪問 http://localhost:8000 即可使用 Playground
#     pass # Placeholder
```

## 架構模式 (舊有 Dy-Flow v3)

### Agent DAG 模式
- **數據流向**：Scout → Scorer → Planner → Executor → Auditor
- **時間觸發**：每個 Agent 有獨立的調度週期
- **依賴管理**：使用 `needs:` 聲明依賴關係
- **例外處理**：`needs_llm` 標記異常情況 (此模式將被 Agno 的高級推理和工具使用替代或增強)

### 三層策略模式
1.  **Data Layer**: Scout 收集原始數據
2.  **Decision Layer**: Scorer + Planner 決策 (此層將被 Agno Agent 大幅增強)
3.  **Execution Layer**: Executor + Manager 執行

### 風控模式
```python
# 統一風控檢查 (舊模式，可被 Agno Risk Sentinel 增強)
# def check_risk_threshold(current_value, baseline, threshold_pct):
#     change_pct = abs(current_value - baseline) / baseline
#     return change_pct >= threshold_pct
```
... (保留其他舊有測試、配置、API 整合模式作為參考) ...

### Dy-Flow v3 企業級架構模式 (2025-05-30 更新)
*(此部分為先前內容，保持不變，僅作上下文參考)*
```python
# 標準 Agno DAG 節點定義 (此處 "Agno" 指早期通用調度概念)
# nodes:
#   scout_bsc:
#     kind: tool
#     schedule: "every 5m"
#     outputs: ["pools_raw_bsc"]
    
#   scout_meteora:
#     kind: tool 
#     schedule: "every 1m"
#     outputs: ["pools_raw_sol"]
    
#   scorer:
#     kind: tool
#     needs: ["pools_raw_bsc", "pools_raw_sol"]
#     outputs: ["pools_scored"]
    
#   planner:
#     kind: tool
#     needs: ["pools_scored", "risk_alerts"]
#     outputs: ["plans"]
    
#   planner_llm:
#     kind: llm
#     needs: ["plans?needs_llm"]
#     outputs: ["plans_fixed"]
```

```python
# S-1 Delta-Neutral LP 策略 (舊模式)
# @dataclass
# class DeltaNeutralStrategy:
#     atr_k: float = 3.0
#     hedge_ratio: float = 1.0
#     target_annual_return: float = 0.25
#     max_delta_threshold: float = 0.3
```
... (省略其他 Dy-Flow v3 企業級架構模式的詳細內容) ...

#!/usr/bin/env python3
"""
池子评估工作流程 - 整合现有Agent组件
使用现有的ScorerV2AgnoAgent和相关工具进行完整的池子评估
"""

import asyncio
import sys
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import structlog

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

logger = structlog.get_logger(__name__)


class PoolEvaluationWorkflow:
    """池子评估工作流程 - 整合现有组件"""
    
    def __init__(self):
        self.scout_agent = None
        self.scorer_agent = None
        self.risk_agent = None
        self.pool_scanner = None
        self.pool_scoring_tool = None
        
    async def initialize(self):
        """初始化所有评估组件"""
        try:
            # 1. 初始化数据收集Agent (Scout)
            from src.integrations.pancakeswap import PancakeSwapV3Integration
            self.pancake_api = PancakeSwapV3Integration({})
            
            # 2. 初始化评分Agent (ScorerV2Agno)
            from src.agents.scorer_v2_agno import ScorerV2AgnoAgent
            from src.utils.config import Config
            from src.utils.database import Database
            
            config = Config()
            database = Database(config)
            
            self.scorer_agent = ScorerV2AgnoAgent("scorer_v2_agno", config, database)
            await self.scorer_agent.initialize()
            
            # 3. 初始化风险监控Agent
            from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
            self.risk_agent = RiskSentinelAgnoAgent("risk_sentinel_agno", config, database)
            await self.risk_agent.initialize()
            
            # 4. 初始化池子扫描工具
            from agno_tools.pool_scanner_tool import PoolScannerTool
            self.pool_scanner = PoolScannerTool()
            
            # 5. 初始化池子评分工具
            from agno_tools.pool_scoring_tool import PoolScoringTool
            self.pool_scoring_tool = PoolScoringTool()
            
            logger.info("pool_evaluation_workflow_initialized")
            return True
            
        except Exception as e:
            logger.error("workflow_initialization_failed", error=str(e))
            return False
    
    async def evaluate_pools_complete_workflow(self, 
                                             chain: str = 'bsc',
                                             max_pools: int = 10) -> Dict[str, Any]:
        """
        完整的池子评估工作流程
        
        Args:
            chain: 链名称 ('bsc' 或 'solana')
            max_pools: 最大评估池子数量
            
        Returns:
            完整的评估结果
        """
        workflow_start = datetime.now()
        workflow_id = f"eval_{int(workflow_start.timestamp())}"
        
        logger.info("complete_pool_evaluation_started", 
                   workflow_id=workflow_id,
                   chain=chain,
                   max_pools=max_pools)
        
        try:
            # 第1步: 数据收集 - 获取真实池子数据
            print(f"\n🔍 第1步: 数据收集 ({chain.upper()})")
            pools_data = await self._collect_pool_data(chain, max_pools)
            
            if not pools_data:
                return {
                    "workflow_id": workflow_id,
                    "status": "failed",
                    "error": "未能获取池子数据",
                    "timestamp": workflow_start.isoformat()
                }
            
            print(f"✅ 收集到 {len(pools_data)} 个池子数据")
            
            # 第2步: 池子扫描和过滤
            print(f"\n📊 第2步: 池子扫描和过滤")
            filtered_pools = await self._scan_and_filter_pools(chain, pools_data)
            print(f"✅ 过滤后剩余 {len(filtered_pools)} 个优质池子")
            
            # 第3步: 详细评分分析
            print(f"\n🎯 第3步: AI增强评分分析")
            scoring_results = await self._perform_detailed_scoring(filtered_pools)
            print(f"✅ 完成 {len(scoring_results)} 个池子的详细评分")
            
            # 第4步: 风险评估
            print(f"\n🛡️ 第4步: 风险评估和监控")
            risk_assessment = await self._perform_risk_assessment(scoring_results)
            print(f"✅ 完成风险评估，检测到 {len(risk_assessment.get('alerts', []))} 个风险点")
            
            # 第5步: 综合分析和推荐
            print(f"\n🧠 第5步: AI综合分析和投资推荐")
            final_recommendations = await self._generate_final_recommendations(
                scoring_results, risk_assessment
            )
            print(f"✅ 生成 {len(final_recommendations)} 个投资推荐")
            
            # 第6步: 生成完整报告
            workflow_end = datetime.now()
            duration = (workflow_end - workflow_start).total_seconds()
            
            final_report = {
                "workflow_id": workflow_id,
                "status": "success",
                "chain": chain,
                "execution_time": f"{duration:.2f}秒",
                "timestamp": workflow_start.isoformat(),
                
                # 数据统计
                "data_summary": {
                    "pools_collected": len(pools_data),
                    "pools_filtered": len(filtered_pools),
                    "pools_scored": len(scoring_results),
                    "recommendations_generated": len(final_recommendations)
                },
                
                # 详细结果
                "pool_data": pools_data[:3],  # 只显示前3个原始数据
                "filtered_pools": filtered_pools,
                "scoring_results": scoring_results,
                "risk_assessment": risk_assessment,
                "final_recommendations": final_recommendations,
                
                # 工作流程元数据
                "workflow_metadata": {
                    "agno_enhanced": self.scorer_agent.agno_available if self.scorer_agent else False,
                    "ai_analysis_enabled": True,
                    "risk_monitoring_enabled": True,
                    "evaluation_method": "comprehensive_v3"
                }
            }
            
            print(f"\n📋 评估工作流程完成!")
            print(f"   执行时间: {duration:.2f}秒")
            print(f"   处理池子: {len(pools_data)} → {len(final_recommendations)}")
            print(f"   推荐池子: {len([r for r in final_recommendations if r.get('recommendation') == 'BUY'])}")
            
            return final_report
            
        except Exception as e:
            logger.error("complete_workflow_failed", 
                        workflow_id=workflow_id,
                        error=str(e))
            return {
                "workflow_id": workflow_id,
                "status": "error",
                "error": str(e),
                "timestamp": workflow_start.isoformat()
            }
    
    async def _collect_pool_data(self, chain: str, max_pools: int) -> List[Dict[str, Any]]:
        """收集池子数据"""
        try:
            if chain.lower() == 'bsc':
                # 使用PancakeSwap V3 API
                async with self.pancake_api as api:
                    pools = await api.get_top_pools(limit=max_pools)
                    return pools
            else:
                # 使用池子扫描工具
                result = await self.pool_scanner.run(
                    chain=chain,
                    filters={
                        'min_tvl': 10000,
                        'min_volume_24h': 5000,
                        'max_pools': max_pools
                    }
                )
                return result.get('pools', [])
                
        except Exception as e:
            logger.error("pool_data_collection_failed", chain=chain, error=str(e))
            return []
    
    async def _scan_and_filter_pools(self, chain: str, pools_data: List[Dict]) -> List[Dict]:
        """扫描和过滤池子"""
        try:
            # 使用池子扫描工具进行过滤
            if not pools_data:
                return []
            
            # 应用基本过滤条件
            filtered = []
            for pool in pools_data:
                tvl = float(pool.get('totalValueLockedUSD', pool.get('tvl_usd', 0)))
                volume = float(pool.get('volumeUSD', pool.get('volume_24h', 0)))
                
                if tvl >= 50000 and volume >= 10000:  # 基本质量要求
                    filtered.append(pool)
            
            return filtered[:10]  # 限制数量
            
        except Exception as e:
            logger.error("pool_filtering_failed", error=str(e))
            return pools_data[:5]  # 返回前5个作为fallback
    
    async def _perform_detailed_scoring(self, pools: List[Dict]) -> List[Dict]:
        """执行详细评分"""
        try:
            if not self.scorer_agent or not pools:
                return []
            
            # 使用ScorerV2AgnoAgent进行评分
            # 注意：这里需要适配数据格式
            scoring_result = await self.scorer_agent.execute()
            
            if scoring_result.status == "success" and scoring_result.data:
                # 将评分结果与池子数据结合
                scored_pools = []
                for i, pool in enumerate(pools[:len(scoring_result.data)]):
                    score_data = scoring_result.data[i] if i < len(scoring_result.data) else None
                    
                    scored_pool = {
                        **pool,
                        "evaluation_score": score_data.score if score_data else 0,
                        "hedgeable": score_data.hedgeable if score_data else False,
                        "scoring_timestamp": datetime.now().isoformat()
                    }
                    scored_pools.append(scored_pool)
                
                return scored_pools
            
            return pools  # 如果评分失败，返回原始数据
            
        except Exception as e:
            logger.error("detailed_scoring_failed", error=str(e))
            return pools
    
    async def _perform_risk_assessment(self, scored_pools: List[Dict]) -> Dict[str, Any]:
        """执行风险评估"""
        try:
            if not self.risk_agent:
                return {"alerts": [], "overall_risk": "unknown"}
            
            # 使用RiskSentinelAgnoAgent进行风险评估
            risk_result = await self.risk_agent.execute()
            
            return {
                "alerts": risk_result.data if risk_result.data else [],
                "overall_risk": "low" if len(risk_result.data or []) == 0 else "medium",
                "risk_timestamp": datetime.now().isoformat(),
                "risk_agent_status": risk_result.status
            }
            
        except Exception as e:
            logger.error("risk_assessment_failed", error=str(e))
            return {"alerts": [], "overall_risk": "unknown", "error": str(e)}
    
    async def _generate_final_recommendations(self, 
                                            scored_pools: List[Dict],
                                            risk_assessment: Dict) -> List[Dict]:
        """生成最终投资推荐"""
        try:
            recommendations = []
            
            for pool in scored_pools:
                score = pool.get('evaluation_score', 0)
                hedgeable = pool.get('hedgeable', False)
                tvl = float(pool.get('totalValueLockedUSD', pool.get('tvl_usd', 0)))
                
                # 生成推荐
                if score >= 80 and hedgeable and tvl >= 100000:
                    recommendation = "BUY"
                    confidence = 0.9
                elif score >= 60 and tvl >= 50000:
                    recommendation = "HOLD"
                    confidence = 0.7
                else:
                    recommendation = "AVOID"
                    confidence = 0.8
                
                # 风险调整
                risk_alerts = len(risk_assessment.get('alerts', []))
                if risk_alerts > 3:
                    confidence *= 0.8
                
                token0 = pool.get('token0', {})
                token1 = pool.get('token1', {})
                token0_symbol = token0.get('symbol', '') if isinstance(token0, dict) else str(token0)
                token1_symbol = token1.get('symbol', '') if isinstance(token1, dict) else str(token1)
                
                recommendation_data = {
                    "pool_id": pool.get('id', ''),
                    "pool_name": f"{token0_symbol}/{token1_symbol}",
                    "recommendation": recommendation,
                    "confidence": confidence,
                    "score": score,
                    "tvl_usd": tvl,
                    "volume_24h": float(pool.get('volumeUSD', pool.get('volume_24h', 0))),
                    "hedgeable": hedgeable,
                    "risk_level": "low" if risk_alerts <= 1 else "medium" if risk_alerts <= 3 else "high",
                    "recommendation_timestamp": datetime.now().isoformat()
                }
                
                recommendations.append(recommendation_data)
            
            # 按评分排序
            recommendations.sort(key=lambda x: x['score'], reverse=True)
            
            return recommendations
            
        except Exception as e:
            logger.error("recommendation_generation_failed", error=str(e))
            return []


async def main():
    """主函数 - 演示完整的评估工作流程"""
    print("🚀 DyFlow池子评估工作流程")
    print("=" * 50)
    
    # 创建工作流程
    workflow = PoolEvaluationWorkflow()
    
    # 初始化
    print("🔧 初始化评估组件...")
    if not await workflow.initialize():
        print("❌ 初始化失败")
        return False
    
    print("✅ 初始化完成")
    
    # 执行完整评估
    result = await workflow.evaluate_pools_complete_workflow(
        chain='bsc',
        max_pools=5
    )
    
    # 显示结果摘要
    if result['status'] == 'success':
        print(f"\n🎉 评估成功完成!")
        print(f"📊 数据统计:")
        for key, value in result['data_summary'].items():
            print(f"   {key}: {value}")
        
        print(f"\n💡 投资推荐:")
        for rec in result['final_recommendations'][:3]:
            print(f"   {rec['pool_name']}: {rec['recommendation']} (评分: {rec['score']:.1f}, 信心: {rec['confidence']:.1f})")
        
        # 保存详细报告
        report_file = f"pool_evaluation_report_{result['workflow_id']}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\n💾 详细报告已保存: {report_file}")
        
        return True
    else:
        print(f"❌ 评估失败: {result.get('error', '未知错误')}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

#!/bin/bash
# Dy-Flow AI Agent 一键安装脚本

set -e

echo "🚀 开始安装 Dy-Flow AI Agent..."

# 检查 Python 版本
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        echo "✅ 发现 Python $PYTHON_VERSION"
        
        # 检查版本是否 >= 3.9
        if python3 -c "import sys; exit(0 if sys.version_info >= (3, 9) else 1)"; then
            echo "✅ Python 版本满足要求 (>= 3.9)"
        else
            echo "❌ Python 版本过低，需要 3.9 或更高版本"
            exit 1
        fi
    else
        echo "❌ 未找到 Python3，请先安装 Python 3.9+"
        exit 1
    fi
}

# 创建虚拟环境
create_venv() {
    echo "📦 创建虚拟环境..."
    python3 -m venv venv
    source venv/bin/activate
    echo "✅ 虚拟环境创建完成"
}

# 安装依赖
install_dependencies() {
    echo "📦 安装 Python 依赖..."
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    echo "✅ 依赖安装完成"
}

# 创建必要目录
create_directories() {
    echo "📁 创建必要目录..."
    mkdir -p data/logs
    mkdir -p data/state
    mkdir -p data/cache
    echo "✅ 目录创建完成"
}

# 复制配置文件
setup_config() {
    echo "⚙️ 设置配置文件..."
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "✅ 已创建 .env 文件，请编辑并填入您的配置"
    else
        echo "ℹ️ .env 文件已存在，跳过"
    fi
}

# 检查 Docker (可选)
check_docker() {
    if command -v docker &> /dev/null; then
        echo "✅ 发现 Docker，可以使用容器化部署"
        if command -v docker-compose &> /dev/null; then
            echo "✅ 发现 Docker Compose"
        else
            echo "⚠️ 未发现 Docker Compose，请安装以使用容器化部署"
        fi
    else
        echo "ℹ️ 未发现 Docker，将使用本地部署模式"
    fi
}

# 主安装流程
main() {
    echo "==============================================="
    echo "       Dy-Flow AI Agent 安装程序"
    echo "==============================================="
    
    check_python
    create_venv
    install_dependencies
    create_directories
    setup_config
    check_docker
    
    echo ""
    echo "🎉 安装完成！"
    echo ""
    echo "📋 接下来的步骤："
    echo "1. 编辑 .env 文件，填入您的私钥和配置"
    echo "2. 激活虚拟环境: source venv/bin/activate"
    echo "3. 运行系统: python main.py"
    echo ""
    echo "📚 更多信息请查看 README.md"
    echo ""
    echo "🐳 Docker 部署 (可选):"
    echo "   docker-compose up -d"
    echo ""
}

# 运行主函数
main "$@"
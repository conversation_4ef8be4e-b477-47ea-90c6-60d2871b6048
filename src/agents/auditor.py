"""
收益审计师 Agent
统计日化收益和投资组合表现
每2小时执行，生成收益报告和性能分析
"""

import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
import structlog

from .base_agent import BaseAgent
from ..utils.config import Config
from ..utils.database import Database
from ..utils.models import PerformanceMetrics
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class EarningsAuditorAgent(BaseAgent):
    """收益审计师 Agent - 统计和分析投资收益"""
    
    def __init__(self, name: str, config: Config, database: Database): # Add name parameter
        super().__init__(name, config, database) # Pass name to super
        
        # 计算配置
        self.metrics_to_calculate = self.agent_config.get('metrics_calculation', [
            'daily_return',
            'total_pnl',
            'realized_pnl',
            'unrealized_pnl',
            'fee_earnings',
            'impermanent_loss'
        ])
        
        # 报告间隔
        self.reporting_intervals = self.agent_config.get('reporting_intervals', {
            'hourly': True,
            'daily': True,
            'weekly': True,
            'monthly': True
        })
        
        # 性能基准
        self.benchmark_returns = {
            'btc': 0.0,  # BTC 基准收益率
            'eth': 0.0,  # ETH 基准收益率
            'defi_index': 0.0  # DeFi 指数基准
        }
        
        # 缓存的历史数据
        self.portfolio_history: List[Dict[str, Any]] = []
        self.price_history: Dict[str, List[Tuple[datetime, float]]] = {}
        
        # 收益统计
        self.earnings_stats = {
            'total_invested': 0.0,
            'current_value': 0.0,
            'total_fees_earned': 0.0,
            'total_gas_costs': 0.0,
            'net_profit': 0.0,
            'roi_percent': 0.0,
            'best_position': None,
            'worst_position': None
        }
    
    async def initialize(self) -> None:
        """初始化收益审计师"""
        try:
            logger.info("earnings_auditor_initializing")
            
            # 验证配置
            if not self.metrics_to_calculate:
                raise DyFlowException("metrics_to_calculate 不能为空")
            
            # 加载历史数据
            await self._load_historical_data()
            
            # 初始化基准数据
            await self._initialize_benchmarks()
            
            self.is_initialized = True
            logger.info("earnings_auditor_initialized")
            
        except Exception as e:
            logger.error("earnings_auditor_initialization_failed", error=str(e))
            raise DyFlowException(f"收益审计师初始化失败: {e}")
    
    async def execute(self) -> Dict[str, Any]:
        """执行收益统计任务"""
        try:
            logger.info("earnings_auditor_execution_started")
            
            # 1. 获取当前投资组合状态
            current_portfolio = await self._get_current_portfolio()
            
            # 2. 计算收益指标
            metrics = await self._calculate_performance_metrics(current_portfolio)
            
            # 3. 生成各时间间隔的报告
            reports = await self._generate_period_reports(metrics)
            
            # 4. 创建投资组合快照
            snapshot = await self._create_portfolio_snapshot(current_portfolio, metrics)
            
            # 5. 分析最佳和最差持仓
            position_analysis = await self._analyze_position_performance(current_portfolio)
            
            # 6. 计算风险调整收益
            risk_adjusted_metrics = await self._calculate_risk_adjusted_returns(metrics)
            
            # 7. 保存数据到数据库
            saved_reports = await self._save_reports_to_database(reports, snapshot)
            
            # 8. 更新统计信息
            self._update_earnings_stats(metrics, position_analysis)
            
            result = {
                'success': True,
                'portfolio_value': current_portfolio.get('total_value', 0),
                'total_pnl': metrics.get('total_pnl', 0),
                'daily_return': metrics.get('daily_return_percent', 0),
                'fee_earnings': metrics.get('fee_earnings', 0),
                'gas_costs': metrics.get('gas_costs', 0),
                'active_positions': len(current_portfolio.get('positions', [])),
                'reports_generated': len(reports),
                'reports_saved': saved_reports,
                'performance_summary': self._create_performance_summary(metrics),
                'timestamp': get_utc_timestamp()
            }
            
            logger.info("earnings_auditor_execution_completed",
                       portfolio_value=result['portfolio_value'],
                       total_pnl=result['total_pnl'],
                       daily_return=result['daily_return'])
            
            return result
            
        except Exception as e:
            logger.error("earnings_auditor_execution_failed", error=str(e))
            raise
    
    async def _get_current_portfolio(self) -> Dict[str, Any]:
        """获取当前投资组合状态"""
        try:
            # 获取活跃持仓
            positions = await self._get_active_positions()
            
            # 获取现金余额
            cash_balance = await self._get_cash_balance()
            
            # 计算总价值
            total_value = cash_balance
            for position in positions:
                total_value += position.get('current_value_usd', 0)
            
            portfolio = {
                'total_value': total_value,
                'cash_balance': cash_balance,
                'positions': positions,
                'position_count': len(positions),
                'last_updated': get_utc_timestamp()
            }
            
            return portfolio
            
        except Exception as e:
            logger.error("portfolio_retrieval_failed", error=str(e))
            return {'total_value': 0, 'positions': [], 'cash_balance': 0}
    
    async def _get_active_positions(self) -> List[Dict[str, Any]]:
        """获取活跃持仓"""
        try:
            # 模拟持仓数据
            positions = [
                {
                    'id': 'pos_1',
                    'pool_id': 'pool_high_1',
                    'chain': 'bsc',
                    'token_pair': 'WBNB/USDT',
                    'cost_usd': 10000,
                    'current_value_usd': 10800,
                    'pnl_usd': 800,
                    'pnl_percent': 8.0,
                    'fees_earned_usd': 150,
                    'entry_time': get_utc_timestamp() - timedelta(days=7),
                    'days_held': 7,
                    'lp_token_qty': 0.5,
                    'il_estimated': 50  # 估算的无常损失
                },
                {
                    'id': 'pos_2',
                    'pool_id': 'pool_high_2',
                    'chain': 'solana',
                    'token_pair': 'SOL/USDC',
                    'cost_usd': 5000,
                    'current_value_usd': 5200,
                    'pnl_usd': 200,
                    'pnl_percent': 4.0,
                    'fees_earned_usd': 80,
                    'entry_time': get_utc_timestamp() - timedelta(days=3),
                    'days_held': 3,
                    'lp_token_qty': 0.3,
                    'il_estimated': 25
                }
            ]
            
            return positions
            
        except Exception as e:
            logger.error("active_positions_retrieval_failed", error=str(e))
            return []
    
    async def _get_cash_balance(self) -> float:
        """获取现金余额"""
        try:
            # 这里应该从钱包或账户获取实际余额
            # 模拟数据
            return 35000.0
            
        except Exception as e:
            logger.error("cash_balance_retrieval_failed", error=str(e))
            return 0.0
    
    async def _calculate_performance_metrics(self, portfolio: Dict[str, Any]) -> Dict[str, Any]:
        """计算性能指标"""
        try:
            positions = portfolio.get('positions', [])
            total_value = portfolio.get('total_value', 0)
            
            # 基础指标
            total_invested = sum(pos.get('cost_usd', 0) for pos in positions)
            total_current_value = sum(pos.get('current_value_usd', 0) for pos in positions)
            total_pnl = sum(pos.get('pnl_usd', 0) for pos in positions)
            total_fees = sum(pos.get('fees_earned_usd', 0) for pos in positions)
            
            # 计算收益率
            roi_percent = (total_pnl / total_invested * 100) if total_invested > 0 else 0
            
            # 计算日化收益率
            daily_return = await self._calculate_daily_return(portfolio)
            
            # 计算年化收益率
            annualized_return = await self._calculate_annualized_return(portfolio)
            
            # 计算夏普比率
            sharpe_ratio = await self._calculate_sharpe_ratio(portfolio)
            
            # 计算最大回撤
            max_drawdown = await self._calculate_max_drawdown(portfolio)
            
            # 计算波动率
            volatility = await self._calculate_portfolio_volatility(portfolio)
            
            # 获取交易统计
            trade_stats = await self._get_trading_statistics()
            
            # 估算总 Gas 成本
            gas_costs = await self._calculate_total_gas_costs()
            
            metrics = {
                'timestamp': get_utc_timestamp(),
                'total_value': total_value,
                'total_invested': total_invested,
                'total_current_value': total_current_value,
                'total_pnl': total_pnl,
                'roi_percent': roi_percent,
                'daily_return_percent': daily_return,
                'annualized_return_percent': annualized_return,
                'total_fees_earned': total_fees,
                'gas_costs': gas_costs,
                'net_profit': total_pnl + total_fees - gas_costs,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown_percent': max_drawdown,
                'volatility_percent': volatility,
                'active_positions': len(positions),
                'trade_stats': trade_stats
            }
            
            return metrics
            
        except Exception as e:
            logger.error("performance_metrics_calculation_failed", error=str(e))
            return {}
    
    async def _calculate_daily_return(self, portfolio: Dict[str, Any]) -> float:
        """计算日化收益率"""
        try:
            # 获取昨天的投资组合价值
            yesterday_value = await self._get_portfolio_value_at_date(
                get_utc_timestamp() - timedelta(days=1)
            )
            
            current_value = portfolio.get('total_value', 0)
            
            if yesterday_value > 0:
                daily_return = (current_value - yesterday_value) / yesterday_value * 100
                return daily_return
            
            return 0.0
            
        except Exception as e:
            logger.error("daily_return_calculation_failed", error=str(e))
            return 0.0
    
    async def _calculate_annualized_return(self, portfolio: Dict[str, Any]) -> float:
        """计算年化收益率"""
        try:
            # 获取最早投资时间
            positions = portfolio.get('positions', [])
            if not positions:
                return 0.0
            
            earliest_entry = min(pos.get('entry_time', get_utc_timestamp()) for pos in positions)
            days_since_start = (get_utc_timestamp() - earliest_entry).days
            
            if days_since_start <= 0:
                return 0.0
            
            total_invested = sum(pos.get('cost_usd', 0) for pos in positions)
            current_value = sum(pos.get('current_value_usd', 0) for pos in positions)
            
            if total_invested > 0:
                total_return = (current_value - total_invested) / total_invested
                annualized_return = (1 + total_return) ** (365 / days_since_start) - 1
                return annualized_return * 100
            
            return 0.0
            
        except Exception as e:
            logger.error("annualized_return_calculation_failed", error=str(e))
            return 0.0
    
    async def _calculate_sharpe_ratio(self, portfolio: Dict[str, Any]) -> float:
        """计算夏普比率"""
        try:
            # 获取历史收益率数据
            daily_returns = await self._get_historical_daily_returns()
            
            if len(daily_returns) < 30:  # 需要至少30天数据
                return 0.0
            
            # 计算平均收益率和标准差
            avg_return = sum(daily_returns) / len(daily_returns)
            variance = sum((r - avg_return) ** 2 for r in daily_returns) / len(daily_returns)
            std_dev = variance ** 0.5
            
            # 假设无风险收益率为3%年化
            risk_free_rate = 0.03 / 365  # 日化无风险收益率
            
            if std_dev > 0:
                sharpe_ratio = (avg_return - risk_free_rate) / std_dev
                return sharpe_ratio * (365 ** 0.5)  # 年化夏普比率
            
            return 0.0
            
        except Exception as e:
            logger.error("sharpe_ratio_calculation_failed", error=str(e))
            return 0.0
    
    async def _calculate_max_drawdown(self, portfolio: Dict[str, Any]) -> float:
        """计算最大回撤"""
        try:
            # 获取历史投资组合价值
            value_history = await self._get_portfolio_value_history()
            
            if len(value_history) < 2:
                return 0.0
            
            max_value = value_history[0]
            max_drawdown = 0.0
            
            for value in value_history:
                if value > max_value:
                    max_value = value
                
                drawdown = (max_value - value) / max_value
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
            
            return max_drawdown * 100
            
        except Exception as e:
            logger.error("max_drawdown_calculation_failed", error=str(e))
            return 0.0
    
    async def _calculate_portfolio_volatility(self, portfolio: Dict[str, Any]) -> float:
        """计算投资组合波动率"""
        try:
            daily_returns = await self._get_historical_daily_returns()
            
            if len(daily_returns) < 10:
                return 0.0
            
            avg_return = sum(daily_returns) / len(daily_returns)
            variance = sum((r - avg_return) ** 2 for r in daily_returns) / len(daily_returns)
            volatility = (variance ** 0.5) * (365 ** 0.5) * 100  # 年化波动率
            
            return volatility
            
        except Exception as e:
            logger.error("portfolio_volatility_calculation_failed", error=str(e))
            return 0.0
    
    async def _get_trading_statistics(self) -> Dict[str, Any]:
        """获取交易统计"""
        try:
            # 这里应该从数据库查询交易记录
            # 模拟数据
            return {
                'total_trades': 15,
                'successful_trades': 12,
                'failed_trades': 3,
                'win_rate': 80.0,
                'avg_trade_size': 5000,
                'largest_win': 1200,
                'largest_loss': -300
            }
            
        except Exception as e:
            logger.error("trading_statistics_failed", error=str(e))
            return {}
    
    async def _calculate_total_gas_costs(self) -> float:
        """计算总 Gas 成本"""
        try:
            # 从数据库查询所有交易的 Gas 成本
            # 模拟数据
            return 250.0  # USD
            
        except Exception as e:
            logger.error("gas_costs_calculation_failed", error=str(e))
            return 0.0
    
    async def _generate_period_reports(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成各时间段报告"""
        try:
            reports = []
            current_time = get_utc_timestamp()
            
            # 生成小时报告
            if self.reporting_intervals.get('hourly'):
                hourly_report = await self._generate_hourly_report(metrics, current_time)
                if hourly_report:
                    reports.append(hourly_report)
            
            # 生成日报告
            if self.reporting_intervals.get('daily'):
                daily_report = await self._generate_daily_report(metrics, current_time)
                if daily_report:
                    reports.append(daily_report)
            
            # 生成周报告
            if self.reporting_intervals.get('weekly') and current_time.weekday() == 6:  # 周日
                weekly_report = await self._generate_weekly_report(metrics, current_time)
                if weekly_report:
                    reports.append(weekly_report)
            
            # 生成月报告
            if self.reporting_intervals.get('monthly') and current_time.day == 1:  # 月初
                monthly_report = await self._generate_monthly_report(metrics, current_time)
                if monthly_report:
                    reports.append(monthly_report)
            
            return reports
            
        except Exception as e:
            logger.error("period_reports_generation_failed", error=str(e))
            return []
    
    async def _generate_hourly_report(self, metrics: Dict[str, Any], timestamp: datetime) -> Optional[Dict[str, Any]]:
        """生成小时报告"""
        try:
            # 计算最近1小时的指标变化
            hour_ago = timestamp - timedelta(hours=1)
            
            return {
                'period': 'hourly',
                'start_time': hour_ago,
                'end_time': timestamp,
                'total_return': metrics.get('total_pnl', 0),
                'fees_earned': metrics.get('total_fees_earned', 0),
                'gas_costs': 10.0,  # 模拟1小时内的Gas成本
                'net_profit': metrics.get('net_profit', 0),
                'active_positions': metrics.get('active_positions', 0),
                'created_at': timestamp
            }
            
        except Exception as e:
            logger.error("hourly_report_generation_failed", error=str(e))
            return None
    
    async def _generate_daily_report(self, metrics: Dict[str, Any], timestamp: datetime) -> Optional[Dict[str, Any]]:
        """生成日报告"""
        try:
            day_ago = timestamp - timedelta(days=1)
            
            return {
                'period': 'daily',
                'start_time': day_ago,
                'end_time': timestamp,
                'total_return': metrics.get('total_pnl', 0),
                'daily_return': metrics.get('daily_return_percent', 0),
                'fees_earned': metrics.get('total_fees_earned', 0),
                'gas_costs': 50.0,
                'net_profit': metrics.get('net_profit', 0),
                'volatility': metrics.get('volatility_percent', 0),
                'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                'created_at': timestamp
            }
            
        except Exception as e:
            logger.error("daily_report_generation_failed", error=str(e))
            return None
    
    async def _generate_weekly_report(self, metrics: Dict[str, Any], timestamp: datetime) -> Optional[Dict[str, Any]]:
        """生成周报告"""
        try:
            week_ago = timestamp - timedelta(weeks=1)
            
            return {
                'period': 'weekly',
                'start_time': week_ago,
                'end_time': timestamp,
                'total_return': metrics.get('total_pnl', 0),
                'weekly_return': await self._calculate_period_return(week_ago, timestamp),
                'fees_earned': metrics.get('total_fees_earned', 0),
                'gas_costs': 300.0,
                'max_drawdown': metrics.get('max_drawdown_percent', 0),
                'best_day': await self._get_best_day_this_week(),
                'worst_day': await self._get_worst_day_this_week(),
                'created_at': timestamp
            }
            
        except Exception as e:
            logger.error("weekly_report_generation_failed", error=str(e))
            return None
    
    async def _generate_monthly_report(self, metrics: Dict[str, Any], timestamp: datetime) -> Optional[Dict[str, Any]]:
        """生成月报告"""
        try:
            month_ago = timestamp - timedelta(days=30)
            
            return {
                'period': 'monthly',
                'start_time': month_ago,
                'end_time': timestamp,
                'total_return': metrics.get('total_pnl', 0),
                'monthly_return': await self._calculate_period_return(month_ago, timestamp),
                'annualized_return': metrics.get('annualized_return_percent', 0),
                'fees_earned': metrics.get('total_fees_earned', 0),
                'gas_costs': 1200.0,
                'sharpe_ratio': metrics.get('sharpe_ratio', 0),
                'win_rate': metrics.get('trade_stats', {}).get('win_rate', 0),
                'total_trades': metrics.get('trade_stats', {}).get('total_trades', 0),
                'created_at': timestamp
            }
            
        except Exception as e:
            logger.error("monthly_report_generation_failed", error=str(e))
            return None
    
    async def _create_portfolio_snapshot(self, portfolio: Dict[str, Any], 
                                       metrics: Dict[str, Any]) -> Dict[str, Any]:
        """创建投资组合快照"""
        try:
            positions = portfolio.get('positions', [])
            
            # 计算持仓分布
            position_distribution = {}
            total_value = sum(pos.get('current_value_usd', 0) for pos in positions)
            
            for pos in positions:
                token_pair = pos.get('token_pair', 'Unknown')
                value = pos.get('current_value_usd', 0)
                percentage = (value / total_value * 100) if total_value > 0 else 0
                position_distribution[token_pair] = percentage
            
            # 获取前几大持仓
            top_positions = sorted(positions, 
                                 key=lambda x: x.get('current_value_usd', 0), 
                                 reverse=True)[:5]
            
            # 计算分散化评分
            diversification_score = await self._calculate_diversification_score(positions)
            
            # 计算风险评分
            risk_score = await self._calculate_portfolio_risk_score(positions, metrics)
            
            snapshot = {
                'total_value': portfolio.get('total_value', 0),
                'cash_balance': portfolio.get('cash_balance', 0),
                'position_count': len(positions),
                'active_chains': list(set(pos.get('chain') for pos in positions)),
                'top_positions': [
                    {
                        'token_pair': pos.get('token_pair'),
                        'value_usd': pos.get('current_value_usd'),
                        'pnl_percent': pos.get('pnl_percent')
                    } for pos in top_positions
                ],
                'position_distribution': position_distribution,
                'diversification_score': diversification_score,
                'risk_score': risk_score,
                'performance_summary': {
                    'total_pnl': metrics.get('total_pnl', 0),
                    'daily_return': metrics.get('daily_return_percent', 0),
                    'fees_earned': metrics.get('total_fees_earned', 0)
                },
                'timestamp': get_utc_timestamp()
            }
            
            return snapshot
            
        except Exception as e:
            logger.error("portfolio_snapshot_creation_failed", error=str(e))
            return {}
    
    async def _analyze_position_performance(self, portfolio: Dict[str, Any]) -> Dict[str, Any]:
        """分析持仓表现"""
        try:
            positions = portfolio.get('positions', [])
            if not positions:
                return {}
            
            # 找出最佳和最差持仓
            best_position = max(positions, key=lambda x: x.get('pnl_percent', 0))
            worst_position = min(positions, key=lambda x: x.get('pnl_percent', 0))
            
            # 计算平均持有时间
            avg_holding_days = sum(pos.get('days_held', 0) for pos in positions) / len(positions)
            
            # 分析链分布
            chain_distribution = {}
            for pos in positions:
                chain = pos.get('chain', 'unknown')
                if chain not in chain_distribution:
                    chain_distribution[chain] = {'count': 0, 'value': 0}
                chain_distribution[chain]['count'] += 1
                chain_distribution[chain]['value'] += pos.get('current_value_usd', 0)
            
            analysis = {
                'best_position': {
                    'token_pair': best_position.get('token_pair'),
                    'pnl_percent': best_position.get('pnl_percent'),
                    'pnl_usd': best_position.get('pnl_usd')
                },
                'worst_position': {
                    'token_pair': worst_position.get('token_pair'),
                    'pnl_percent': worst_position.get('pnl_percent'),
                    'pnl_usd': worst_position.get('pnl_usd')
                },
                'avg_holding_days': avg_holding_days,
                'chain_distribution': chain_distribution,
                'profitable_positions': len([p for p in positions if p.get('pnl_usd', 0) > 0]),
                'total_positions': len(positions)
            }
            
            return analysis
            
        except Exception as e:
            logger.error("position_performance_analysis_failed", error=str(e))
            return {}
    
    async def _calculate_risk_adjusted_returns(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """计算风险调整收益"""
        try:
            total_return = metrics.get('total_pnl', 0)
            volatility = metrics.get('volatility_percent', 1)
            max_drawdown = metrics.get('max_drawdown_percent', 1)
            
            # 计算卡尔马比率（Calmar Ratio）
            calmar_ratio = total_return / max_drawdown if max_drawdown > 0 else 0
            
            # 计算索提诺比率（Sortino Ratio）
            sortino_ratio = await self._calculate_sortino_ratio()
            
            # 计算信息比率（Information Ratio）
            information_ratio = await self._calculate_information_ratio()
            
            return {
                'calmar_ratio': calmar_ratio,
                'sortino_ratio': sortino_ratio,
                'information_ratio': information_ratio,
                'risk_adjusted_return': total_return / volatility if volatility > 0 else 0
            }
            
        except Exception as e:
            logger.error("risk_adjusted_returns_calculation_failed", error=str(e))
            return {}
    
    # 辅助方法（为了简化，这些返回模拟数据）
    async def _get_portfolio_value_at_date(self, date: datetime) -> float:
        """获取指定日期的投资组合价值"""
        return 49500.0  # 模拟数据
    
    async def _get_historical_daily_returns(self) -> List[float]:
        """获取历史日收益率"""
        # 模拟30天的日收益率数据
        import random
        return [random.uniform(-0.05, 0.05) for _ in range(30)]
    
    async def _get_portfolio_value_history(self) -> List[float]:
        """获取投资组合价值历史"""
        # 模拟历史价值数据
        base_value = 50000
        return [base_value + i * 100 + random.randint(-500, 500) for i in range(30)]
    
    async def _calculate_period_return(self, start_date: datetime, end_date: datetime) -> float:
        """计算指定期间的收益率"""
        return 5.2  # 模拟数据
    
    async def _get_best_day_this_week(self) -> Dict[str, Any]:
        """获取本周最佳交易日"""
        return {'date': '2024-01-15', 'return': 2.5}
    
    async def _get_worst_day_this_week(self) -> Dict[str, Any]:
        """获取本周最差交易日"""
        return {'date': '2024-01-12', 'return': -1.2}
    
    async def _calculate_diversification_score(self, positions: List[Dict[str, Any]]) -> float:
        """计算分散化评分"""
        if not positions:
            return 0.0
        
        # 简化的分散化评分：基于持仓数量和价值分布
        position_count = len(positions)
        if position_count == 1:
            return 0.2
        elif position_count <= 3:
            return 0.6
        elif position_count <= 5:
            return 0.8
        else:
            return 1.0
    
    async def _calculate_portfolio_risk_score(self, positions: List[Dict[str, Any]], 
                                            metrics: Dict[str, Any]) -> float:
        """计算投资组合风险评分"""
        volatility = metrics.get('volatility_percent', 0)
        max_drawdown = metrics.get('max_drawdown_percent', 0)
        
        # 基于波动率和最大回撤计算风险评分
        risk_score = (volatility + max_drawdown) / 100
        return min(1.0, max(0.0, risk_score))
    
    async def _calculate_sortino_ratio(self) -> float:
        """计算索提诺比率"""
        return 1.5  # 模拟数据
    
    async def _calculate_information_ratio(self) -> float:
        """计算信息比率"""
        return 0.8  # 模拟数据
    
    def _create_performance_summary(self, metrics: Dict[str, Any]) -> Dict[str, Any]:
        """创建性能摘要"""
        return {
            'roi_percent': metrics.get('roi_percent', 0),
            'daily_return_percent': metrics.get('daily_return_percent', 0),
            'sharpe_ratio': metrics.get('sharpe_ratio', 0),
            'max_drawdown_percent': metrics.get('max_drawdown_percent', 0),
            'total_fees_earned': metrics.get('total_fees_earned', 0),
            'net_profit': metrics.get('net_profit', 0)
        }
    
    async def _save_reports_to_database(self, reports: List[Dict[str, Any]], 
                                      snapshot: Dict[str, Any]) -> int:
        """保存报告到数据库"""
        try:
            saved_count = 0
            
            # 保存性能报告
            for report in reports:
                await self.save_to_database('performance_metrics', report)
                saved_count += 1
            
            # 保存投资组合快照
            if snapshot:
                await self.save_to_database('portfolio_snapshots', snapshot)
                saved_count += 1
            
            return saved_count
            
        except Exception as e:
            logger.error("reports_save_failed", error=str(e))
            return 0
    
    def _update_earnings_stats(self, metrics: Dict[str, Any], 
                             position_analysis: Dict[str, Any]) -> None:
        """更新收益统计"""
        try:
            self.earnings_stats.update({
                'total_invested': metrics.get('total_invested', 0),
                'current_value': metrics.get('total_current_value', 0),
                'total_fees_earned': metrics.get('total_fees_earned', 0),
                'total_gas_costs': metrics.get('gas_costs', 0),
                'net_profit': metrics.get('net_profit', 0),
                'roi_percent': metrics.get('roi_percent', 0),
                'best_position': position_analysis.get('best_position'),
                'worst_position': position_analysis.get('worst_position')
            })
            
        except Exception as e:
            logger.error("earnings_stats_update_failed", error=str(e))
    
    async def _load_historical_data(self) -> None:
        """加载历史数据"""
        try:
            # 从数据库加载历史投资组合和价格数据
            # 这里使用空实现
            pass
        except Exception as e:
            logger.warning("historical_data_load_failed", error=str(e))
    
    async def _initialize_benchmarks(self) -> None:
        """初始化基准数据"""
        try:
            # 获取基准收益率数据
            # 这里使用空实现
            pass
        except Exception as e:
            logger.warning("benchmarks_initialization_failed", error=str(e))
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            self.portfolio_history.clear()
            self.price_history.clear()
            logger.info("earnings_auditor_cleanup_completed")
        except Exception as e:
            logger.error("earnings_auditor_cleanup_failed", error=str(e))
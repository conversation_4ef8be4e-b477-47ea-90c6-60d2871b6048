"""
基础 Agent 类
定义所有 Agent 的通用接口和行为
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog
import json
import aiohttp
from dataclasses import asdict

from ..utils.config import Config
from ..utils.database import Database
from ..utils.exceptions import DyFlowException
from ..utils.helpers import get_utc_timestamp, Timer
from ..utils.models_v3 import ChainType, ActionType, RiskLevel

logger = structlog.get_logger(__name__)


class BaseAgent(ABC):
    """所有 Agent 的基础类"""
    
    def __init__(self, name: str, config: Config, database: Database):
        self.name = name
        self.config = config
        self.database = database
        self.is_initialized = False
        self.is_running = False
        self.last_execution_time: Optional[datetime] = None
        self.execution_count = 0
        self.error_count = 0
        
        # Agent 特定配置
        self.agent_config = self._get_agent_config()
        
        # 性能统计
        self.performance_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'avg_execution_time': 0.0,
            'last_error': None
        }
    
    @abstractmethod
    async def initialize(self) -> None:
        """初始化 Agent"""
        pass
    
    @abstractmethod
    async def execute(self) -> Dict[str, Any]:
        """执行 Agent 的主要任务"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass
    
    def _get_agent_config(self) -> Dict[str, Any]:
        """获取 Agent 特定配置"""
        # 从 AGNO 配置文件或默认配置中获取
        return getattr(self.config, f'{self.name.lower()}_agent', {})
    
    async def run(self) -> Dict[str, Any]:
        """运行 Agent（包含错误处理和性能统计）"""
        if not self.is_initialized:
            await self.initialize()
        
        execution_id = f"{self.name}_{get_utc_timestamp().isoformat()}"
        timer = Timer()
        result = {}
        
        try:
            logger.info("agent_execution_started", 
                       agent=self.name, 
                       execution_id=execution_id)
            
            # 记录执行开始
            await self._log_execution_start(execution_id)
            
            timer.start()
            self.is_running = True
            
            # 执行主要任务
            result = await self.execute()
            
            timer.stop()
            self.is_running = False
            self.last_execution_time = get_utc_timestamp()
            self.execution_count += 1
            
            # 更新性能统计
            self.performance_stats['total_executions'] += 1
            self.performance_stats['successful_executions'] += 1
            self._update_avg_execution_time(timer.elapsed())
            
            # 记录执行完成
            await self._log_execution_complete(execution_id, timer.elapsed(), result)
            
            logger.info("agent_execution_completed",
                       agent=self.name,
                       execution_id=execution_id,
                       duration=timer.elapsed(),
                       result_summary=self._get_result_summary(result))
            
            return result
            
        except Exception as e:
            timer.stop()
            self.is_running = False
            self.error_count += 1
            self.performance_stats['failed_executions'] += 1
            self.performance_stats['last_error'] = str(e)
            
            # 记录执行失败
            await self._log_execution_failed(execution_id, timer.elapsed(), str(e))
            
            logger.error("agent_execution_failed",
                        agent=self.name,
                        execution_id=execution_id,
                        error=str(e),
                        duration=timer.elapsed())
            
            # 根据配置决定是否抛出异常
            if self._should_raise_exception(e):
                raise
            
            return {'error': str(e), 'success': False}
    
    def _should_raise_exception(self, exception: Exception) -> bool:
        """判断是否应该抛出异常"""
        # 网络错误不抛出异常，继续执行其他任务
        if isinstance(exception, (aiohttp.ClientError, asyncio.TimeoutError)):
            return False
        
        # 严重错误抛出异常
        if isinstance(exception, DyFlowException):
            return True
            
        return False
    
    def _update_avg_execution_time(self, execution_time: float) -> None:
        """更新平均执行时间"""
        total = self.performance_stats['total_executions']
        current_avg = self.performance_stats['avg_execution_time']
        
        # 计算新的平均值
        new_avg = (current_avg * (total - 1) + execution_time) / total
        self.performance_stats['avg_execution_time'] = new_avg
    
    def _get_result_summary(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """获取结果摘要"""
        summary = {
            'success': result.get('success', True),
            'items_processed': result.get('items_processed', 0),
            'data_points': len(result.get('data', [])) if 'data' in result else 0
        }
        
        if 'error' in result:
            summary['error'] = result['error']
            
        return summary
    
    async def _log_execution_start(self, execution_id: str) -> None:
        """记录执行开始"""
        try:
            log_data = {
                'agent_name': self.name,
                'execution_id': execution_id,
                'status': 'started',
                'start_time': get_utc_timestamp(),
                'input_data': self.agent_config
            }
            
            # 这里可以保存到数据库
            # await self.database.save_agent_log(log_data)
            
        except Exception as e:
            logger.warning("failed_to_log_execution_start", 
                          agent=self.name, error=str(e))
    
    async def _log_execution_complete(self, execution_id: str, 
                                    duration: float, result: Dict[str, Any]) -> None:
        """记录执行完成"""
        try:
            log_data = {
                'execution_id': execution_id,
                'status': 'completed',
                'end_time': get_utc_timestamp(),
                'duration_seconds': int(duration),
                'output_data': self._sanitize_log_data(result)
            }
            
            # 这里可以更新数据库记录
            # await self.database.update_agent_log(execution_id, log_data)
            
        except Exception as e:
            logger.warning("failed_to_log_execution_complete", 
                          agent=self.name, error=str(e))
    
    async def _log_execution_failed(self, execution_id: str, 
                                  duration: float, error: str) -> None:
        """记录执行失败"""
        try:
            log_data = {
                'execution_id': execution_id,
                'status': 'failed',
                'end_time': get_utc_timestamp(),
                'duration_seconds': int(duration),
                'error_message': error
            }
            
            # 这里可以更新数据库记录
            # await self.database.update_agent_log(execution_id, log_data)
            
        except Exception as e:
            logger.warning("failed_to_log_execution_failed", 
                          agent=self.name, error=str(e))
    
    def _sanitize_log_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清理日志数据，避免过大的数据"""
        sanitized = {}
        
        for key, value in data.items():
            if isinstance(value, list) and len(value) > 10:
                # 大数组只保留前几个元素
                sanitized[key] = value[:5] + [f"... and {len(value) - 5} more items"]
            elif isinstance(value, dict):
                # 递归清理字典
                sanitized[key] = self._sanitize_log_data(value)
            elif isinstance(value, str) and len(value) > 1000:
                # 长字符串截断
                sanitized[key] = value[:500] + "... (truncated)"
            else:
                sanitized[key] = value
                
        return sanitized
    
    async def save_to_database(self, table: str, data: Dict[str, Any]) -> str:
        """保存数据到数据库"""
        try:
            # 添加时间戳
            if 'timestamp' not in data:
                data['timestamp'] = get_utc_timestamp()
            
            # 保存到数据库
            record_id = await self.database.insert_record(table, data)
            
            logger.debug("data_saved_to_database",
                        agent=self.name,
                        table=table,
                        record_id=record_id)
            
            return record_id
            
        except Exception as e:
            logger.error("failed_to_save_to_database",
                        agent=self.name,
                        table=table,
                        error=str(e))
            raise
    
    async def call_ollama_llm(self, prompt: str, 
                            model: str = "qwen2.5:7b",
                            temperature: float = 0.2) -> str:
        """调用 Ollama LLM"""
        try:
            llm_config = self.config.llm if hasattr(self.config, 'llm') else {}
            base_url = llm_config.get('base_url', 'http://localhost:11434')
            
            payload = {
                "model": model,
                "prompt": prompt,
                "temperature": temperature,
                "stream": False
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/api/generate",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('response', '')
                    else:
                        error_text = await response.text()
                        raise DyFlowException(f"LLM API 错误: {response.status} - {error_text}")
                        
        except Exception as e:
            logger.error("llm_call_failed", 
                        agent=self.name, 
                        model=model, 
                        error=str(e))
            raise DyFlowException(f"调用 LLM 失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取 Agent 状态"""
        return {
            'name': self.name,
            'is_initialized': self.is_initialized,
            'is_running': self.is_running,
            'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
            'execution_count': self.execution_count,
            'error_count': self.error_count,
            'performance_stats': self.performance_stats,
            'config': self.agent_config
        }
    
    def get_health_score(self) -> float:
        """获取 Agent 健康评分 (0-1)"""
        if self.performance_stats['total_executions'] == 0:
            return 1.0  # 新 Agent 默认健康
        
        success_rate = (self.performance_stats['successful_executions'] / 
                       self.performance_stats['total_executions'])
        
        # 考虑最近的错误
        recent_error_penalty = min(self.error_count * 0.1, 0.5)
        
        # 考虑执行频率（如果很久没执行可能有问题）
        time_penalty = 0
        if self.last_execution_time:
            minutes_since_last = (get_utc_timestamp() - self.last_execution_time).total_seconds() / 60
            if minutes_since_last > 60:  # 超过1小时没执行
                time_penalty = min(minutes_since_last / 1440, 0.3)  # 最多扣30%
        
        health_score = success_rate - recent_error_penalty - time_penalty
        return max(0.0, min(1.0, health_score))
    
    def __str__(self) -> str:
        return f"Agent({self.name})"
    
    def __repr__(self) -> str:
        return f"Agent(name='{self.name}', running={self.is_running}, health={self.get_health_score():.2f})"
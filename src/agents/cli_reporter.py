"""
CLI 报告器 Agent
使用 Rich 库实时显示系统状态和操作结果
每10秒更新显示，提供 Live Dashboard
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog
from rich.console import Console
from rich.live import Live
from rich.table import Table
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich.progress import Progress, BarColumn, TextColumn
from rich.layout import Layout
from rich import box

from .base_agent import BaseAgent
from ..utils.config import Config
from ..utils.database import Database
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class CLIReporterAgent(BaseAgent):
    """CLI 报告器 Agent - Rich Live 仪表板"""
    
    def __init__(self, name: str, config: Config, database: Database): # Add name parameter
        super().__init__(name, config, database) # Pass name to super
        
        # 显示配置
        self.display_components = self.agent_config.get('display_components', [
            'system_status',
            'active_positions',
            'recent_trades',
            'performance_summary',
            'risk_alerts',
            'top_pools'
        ])
        
        self.refresh_rate = self.agent_config.get('refresh_rate', 10)  # 10秒
        self.max_display_rows = self.agent_config.get('max_display_rows', 20)
        
        # 主题配置
        self.theme_colors = {
            'profit': 'green',
            'loss': 'red', 
            'warning': 'yellow',
            'info': 'blue',
            'neutral': 'white',
            'header': 'cyan',
            'border': 'bright_blue'
        }
        
        # Rich 组件
        self.console = Console()
        self.live_display: Optional[Live] = None
        self.layout = Layout()
        
        # 数据缓存
        self.dashboard_data = {
            'system_status': {},
            'portfolio': {},
            'positions': [],
            'trades': [],
            'alerts': [],
            'pools': [],
            'performance': {},
            'last_updated': None
        }
        
        # 运行状态
        self.is_dashboard_running = False
        self.update_count = 0
    
    async def initialize(self) -> None:
        """初始化 CLI 报告器"""
        try:
            logger.info("cli_reporter_initializing")
            
            # 验证配置
            if not self.display_components:
                raise DyFlowException("display_components 不能为空")
            
            # 初始化布局
            self._setup_layout()
            
            # 测试终端兼容性
            self._test_terminal_compatibility()
            
            # 初始化数据
            await self._initialize_dashboard_data()
            
            self.is_initialized = True
            logger.info("cli_reporter_initialized")
            
        except Exception as e:
            logger.error("cli_reporter_initialization_failed", error=str(e))
            raise DyFlowException(f"CLI 报告器初始化失败: {e}")
    
    async def execute(self) -> Dict[str, Any]:
        """执行 CLI 显示任务"""
        try:
            logger.info("cli_reporter_execution_started")
            
            # 1. 收集所有数据
            await self._collect_dashboard_data()
            
            # 2. 更新显示内容
            dashboard_content = self._create_dashboard_content()
            
            # 3. 显示仪表板（如果是第一次运行）
            if not self.is_dashboard_running:
                await self._start_live_dashboard(dashboard_content)
            else:
                # 更新现有显示
                self._update_live_display(dashboard_content)
            
            # 4. 记录更新统计
            self.update_count += 1
            self.dashboard_data['last_updated'] = get_utc_timestamp()
            
            result = {
                'success': True,
                'dashboard_running': self.is_dashboard_running,
                'update_count': self.update_count,
                'components_displayed': len(self.display_components),
                'data_points': sum(len(v) if isinstance(v, list) else 1 
                                 for v in self.dashboard_data.values()),
                'timestamp': get_utc_timestamp()
            }
            
            logger.debug("cli_reporter_execution_completed",
                        update_count=self.update_count,
                        dashboard_running=self.is_dashboard_running)
            
            return result
            
        except Exception as e:
            logger.error("cli_reporter_execution_failed", error=str(e))
            raise
    
    def _setup_layout(self) -> None:
        """设置布局"""
        try:
            # 创建主布局
            self.layout.split(
                Layout(name="header", size=3),
                Layout(name="main", ratio=1),
                Layout(name="footer", size=2)
            )
            
            # 分割主区域
            self.layout["main"].split_row(
                Layout(name="left", ratio=2),
                Layout(name="right", ratio=1)
            )
            
            # 分割左侧区域
            self.layout["left"].split(
                Layout(name="status", size=8),
                Layout(name="positions", ratio=1),
                Layout(name="trades", ratio=1)
            )
            
            # 分割右侧区域
            self.layout["right"].split(
                Layout(name="performance"),
                Layout(name="alerts"),
                Layout(name="pools")
            )
            
        except Exception as e:
            logger.error("layout_setup_failed", error=str(e))
    
    def _test_terminal_compatibility(self) -> None:
        """测试终端兼容性"""
        try:
            # 检查终端是否支持颜色和Unicode
            if not self.console.is_terminal:
                logger.warning("not_running_in_terminal")
            
            # 测试显示能力
            test_table = Table(title="Test")
            test_table.add_column("Test")
            test_table.add_row("✓ Terminal Compatible")
            
            # 不实际打印，只是测试创建
            logger.info("terminal_compatibility_test_passed")
            
        except Exception as e:
            logger.warning("terminal_compatibility_test_failed", error=str(e))
    
    async def _initialize_dashboard_data(self) -> None:
        """初始化仪表板数据"""
        try:
            # 获取初始数据
            await self._collect_dashboard_data()
            logger.info("dashboard_data_initialized")
            
        except Exception as e:
            logger.warning("dashboard_data_initialization_failed", error=str(e))
    
    async def _collect_dashboard_data(self) -> None:
        """收集仪表板数据"""
        try:
            # 并行收集各种数据
            tasks = [
                self._get_system_status(),
                self._get_portfolio_data(), 
                self._get_active_positions(),
                self._get_recent_trades(),
                self._get_risk_alerts(),
                self._get_top_pools(),
                self._get_performance_metrics()
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 更新数据缓存
            data_keys = ['system_status', 'portfolio', 'positions', 'trades', 'alerts', 'pools', 'performance']
            for i, result in enumerate(results):
                if not isinstance(result, Exception):
                    self.dashboard_data[data_keys[i]] = result
                else:
                    logger.warning(f"data_collection_failed_{data_keys[i]}", error=str(result))
            
        except Exception as e:
            logger.error("dashboard_data_collection_failed", error=str(e))
    
    async def _get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            # 模拟系统状态数据
            return {
                'is_running': True,
                'uptime_hours': 24.5,
                'agents_running': 6,
                'agents_healthy': 5,
                'last_error': None,
                'network_status': {
                    'bsc': 'connected',
                    'solana': 'connected'
                },
                'database_status': 'connected',
                'memory_usage_mb': 256,
                'cpu_usage_percent': 15.2
            }
            
        except Exception as e:
            logger.error("system_status_retrieval_failed", error=str(e))
            return {}
    
    async def _get_portfolio_data(self) -> Dict[str, Any]:
        """获取投资组合数据"""
        try:
            return {
                'total_value': 51500.0,
                'cash_balance': 36500.0,
                'invested_value': 15000.0,
                'total_pnl': 1000.0,
                'daily_return': 2.5,
                'total_fees_earned': 230.0
            }
            
        except Exception as e:
            logger.error("portfolio_data_retrieval_failed", error=str(e))
            return {}
    
    async def _get_active_positions(self) -> List[Dict[str, Any]]:
        """获取活跃持仓"""
        try:
            return [
                {
                    'id': 'pos_1',
                    'token_pair': 'WBNB/USDT',
                    'chain': 'BSC',
                    'value_usd': 10800,
                    'cost_usd': 10000,
                    'pnl_usd': 800,
                    'pnl_percent': 8.0,
                    'fees_earned': 150,
                    'days_held': 7,
                    'status': 'active'
                },
                {
                    'id': 'pos_2', 
                    'token_pair': 'SOL/USDC',
                    'chain': 'Solana',
                    'value_usd': 5200,
                    'cost_usd': 5000,
                    'pnl_usd': 200,
                    'pnl_percent': 4.0,
                    'fees_earned': 80,
                    'days_held': 3,
                    'status': 'active'
                }
            ]
            
        except Exception as e:
            logger.error("active_positions_retrieval_failed", error=str(e))
            return []
    
    async def _get_recent_trades(self) -> List[Dict[str, Any]]:
        """获取最近交易"""
        try:
            return [
                {
                    'timestamp': get_utc_timestamp() - timedelta(minutes=30),
                    'action': 'enter',
                    'token_pair': 'SOL/USDC',
                    'amount_usd': 5000,
                    'status': 'confirmed',
                    'gas_cost': 0.5,
                    'tx_hash': '0xabc...123'
                },
                {
                    'timestamp': get_utc_timestamp() - timedelta(hours=2),
                    'action': 'exit',
                    'token_pair': 'ETH/USDT',
                    'amount_usd': 3000,
                    'status': 'confirmed',
                    'gas_cost': 15.2,
                    'tx_hash': '0xdef...456'
                }
            ]
            
        except Exception as e:
            logger.error("recent_trades_retrieval_failed", error=str(e))
            return []
    
    async def _get_risk_alerts(self) -> List[Dict[str, Any]]:
        """获取风险警报"""
        try:
            return [
                {
                    'timestamp': get_utc_timestamp() - timedelta(minutes=15),
                    'severity': 'medium',
                    'type': 'price_drop_warning',
                    'message': 'BTC 15分钟内下跌 6.2%',
                    'resolved': False
                }
            ]
            
        except Exception as e:
            logger.error("risk_alerts_retrieval_failed", error=str(e))
            return []
    
    async def _get_top_pools(self) -> List[Dict[str, Any]]:
        """获取优质池子"""
        try:
            return [
                {
                    'pool_id': 'pool_1',
                    'token_pair': 'WBNB/USDT',
                    'chain': 'BSC',
                    'score': 85,
                    'apr': 18.5,
                    'tvl': 5000000,
                    'risk_level': 'medium'
                },
                {
                    'pool_id': 'pool_2',
                    'token_pair': 'SOL/USDC', 
                    'chain': 'Solana',
                    'score': 82,
                    'apr': 22.3,
                    'tvl': 3200000,
                    'risk_level': 'medium'
                }
            ]
            
        except Exception as e:
            logger.error("top_pools_retrieval_failed", error=str(e))
            return []
    
    async def _get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            return {
                'roi_percent': 6.67,
                'sharpe_ratio': 1.25,
                'max_drawdown': 3.2,
                'win_rate': 75.0,
                'total_trades': 12,
                'avg_holding_days': 5.2
            }
            
        except Exception as e:
            logger.error("performance_metrics_retrieval_failed", error=str(e))
            return {}
    
    def _create_dashboard_content(self) -> Layout:
        """创建仪表板内容"""
        try:
            # 更新各个区域的内容
            self.layout["header"] = self._create_header()
            self.layout["status"] = self._create_system_status_panel()
            self.layout["positions"] = self._create_positions_panel()
            self.layout["trades"] = self._create_trades_panel()
            self.layout["performance"] = self._create_performance_panel()
            self.layout["alerts"] = self._create_alerts_panel()
            self.layout["pools"] = self._create_pools_panel()
            self.layout["footer"] = self._create_footer()
            
            return self.layout
            
        except Exception as e:
            logger.error("dashboard_content_creation_failed", error=str(e))
            return Layout(Panel("错误：无法创建仪表板内容"))
    
    def _create_header(self) -> Panel:
        """创建标题"""
        try:
            portfolio = self.dashboard_data.get('portfolio', {})
            total_value = portfolio.get('total_value', 0)
            daily_return = portfolio.get('daily_return', 0)
            total_pnl = portfolio.get('total_pnl', 0)
            
            # 根据盈亏设置颜色
            pnl_color = self.theme_colors['profit'] if total_pnl >= 0 else self.theme_colors['loss']
            return_color = self.theme_colors['profit'] if daily_return >= 0 else self.theme_colors['loss']
            
            header_text = Text()
            header_text.append("🚀 Dy-Flow AI Agent Dashboard", style=f"bold {self.theme_colors['header']}")
            header_text.append(f"  |  总价值: ${total_value:,.2f}", style="bold white")
            header_text.append(f"  |  总盈亏: ", style="white")
            header_text.append(f"${total_pnl:+,.2f}", style=f"bold {pnl_color}")
            header_text.append(f"  |  日收益: ", style="white")
            header_text.append(f"{daily_return:+.2f}%", style=f"bold {return_color}")
            
            return Panel(header_text, border_style=self.theme_colors['border'])
            
        except Exception as e:
            logger.error("header_creation_failed", error=str(e))
            return Panel("Dy-Flow Dashboard - Error", border_style="red")
    
    def _create_system_status_panel(self) -> Panel:
        """创建系统状态面板"""
        try:
            status = self.dashboard_data.get('system_status', {})
            
            table = Table(show_header=False, box=box.SIMPLE)
            table.add_column("Metric", style="cyan")
            table.add_column("Value")
            table.add_column("Status")
            
            # 系统运行状态
            running_status = "🟢 运行中" if status.get('is_running') else "🔴 已停止"
            table.add_row("系统状态", running_status, "")
            
            # 运行时间
            uptime = status.get('uptime_hours', 0)
            table.add_row("运行时间", f"{uptime:.1f} 小时", "")
            
            # Agent 状态
            agents_running = status.get('agents_running', 0)
            agents_healthy = status.get('agents_healthy', 0)
            agent_status = f"{agents_healthy}/{agents_running}"
            agent_color = self.theme_colors['profit'] if agents_healthy == agents_running else self.theme_colors['warning']
            table.add_row("Agent 状态", agent_status, f"[{agent_color}]●[/{agent_color}]")
            
            # 网络连接
            network = status.get('network_status', {})
            bsc_status = "🟢" if network.get('bsc') == 'connected' else "🔴"
            solana_status = "🟢" if network.get('solana') == 'connected' else "🔴"
            table.add_row("网络连接", f"BSC {bsc_status} Solana {solana_status}", "")
            
            # 资源使用
            memory = status.get('memory_usage_mb', 0)
            cpu = status.get('cpu_usage_percent', 0)
            table.add_row("资源使用", f"内存: {memory}MB", f"CPU: {cpu:.1f}%")
            
            return Panel(table, title="🖥️  系统状态", border_style=self.theme_colors['border'])
            
        except Exception as e:
            logger.error("system_status_panel_creation_failed", error=str(e))
            return Panel("系统状态错误", border_style="red")
    
    def _create_positions_panel(self) -> Panel:
        """创建持仓面板"""
        try:
            positions = self.dashboard_data.get('positions', [])
            
            if not positions:
                return Panel("暂无活跃持仓", title="💼 活跃持仓", border_style=self.theme_colors['border'])
            
            table = Table(show_header=True, header_style="bold cyan")
            table.add_column("交易对", style="white")
            table.add_column("链", style="blue")
            table.add_column("价值", justify="right")
            table.add_column("盈亏", justify="right")
            table.add_column("手续费", justify="right")
            table.add_column("天数", justify="center")
            
            for pos in positions[:self.max_display_rows]:
                # 设置盈亏颜色
                pnl = pos.get('pnl_usd', 0)
                pnl_color = self.theme_colors['profit'] if pnl >= 0 else self.theme_colors['loss']
                
                table.add_row(
                    pos.get('token_pair', 'N/A'),
                    pos.get('chain', 'N/A'),
                    f"${pos.get('value_usd', 0):,.0f}",
                    f"[{pnl_color}]{pnl:+,.0f}[/{pnl_color}]",
                    f"${pos.get('fees_earned', 0):.0f}",
                    f"{pos.get('days_held', 0)}d"
                )
            
            return Panel(table, title="💼 活跃持仓", border_style=self.theme_colors['border'])
            
        except Exception as e:
            logger.error("positions_panel_creation_failed", error=str(e))
            return Panel("持仓面板错误", border_style="red")
    
    def _create_trades_panel(self) -> Panel:
        """创建交易面板"""
        try:
            trades = self.dashboard_data.get('trades', [])
            
            if not trades:
                return Panel("暂无最近交易", title="📈 最近交易", border_style=self.theme_colors['border'])
            
            table = Table(show_header=True, header_style="bold cyan")
            table.add_column("时间", style="white")
            table.add_column("操作", style="blue")
            table.add_column("交易对", style="white")
            table.add_column("金额", justify="right")
            table.add_column("状态", justify="center")
            
            for trade in trades[:self.max_display_rows]:
                # 格式化时间
                timestamp = trade.get('timestamp', get_utc_timestamp())
                time_str = timestamp.strftime("%H:%M")
                
                # 操作类型颜色
                action = trade.get('action', '')
                action_color = self.theme_colors['profit'] if action == 'enter' else self.theme_colors['warning']
                
                # 状态指示
                status = trade.get('status', '')
                status_icon = "✅" if status == 'confirmed' else "⏳" if status == 'pending' else "❌"
                
                table.add_row(
                    time_str,
                    f"[{action_color}]{action.upper()}[/{action_color}]",
                    trade.get('token_pair', 'N/A'),
                    f"${trade.get('amount_usd', 0):,.0f}",
                    status_icon
                )
            
            return Panel(table, title="📈 最近交易", border_style=self.theme_colors['border'])
            
        except Exception as e:
            logger.error("trades_panel_creation_failed", error=str(e))
            return Panel("交易面板错误", border_style="red")
    
    def _create_performance_panel(self) -> Panel:
        """创建性能面板"""
        try:
            perf = self.dashboard_data.get('performance', {})
            
            table = Table(show_header=False, box=box.SIMPLE)
            table.add_column("指标", style="cyan")
            table.add_column("数值", justify="right")
            
            # ROI
            roi = perf.get('roi_percent', 0)
            roi_color = self.theme_colors['profit'] if roi >= 0 else self.theme_colors['loss']
            table.add_row("总收益率", f"[{roi_color}]{roi:+.2f}%[/{roi_color}]")
            
            # 夏普比率
            sharpe = perf.get('sharpe_ratio', 0)
            sharpe_color = self.theme_colors['profit'] if sharpe >= 1 else self.theme_colors['warning']
            table.add_row("夏普比率", f"[{sharpe_color}]{sharpe:.2f}[/{sharpe_color}]")
            
            # 最大回撤
            drawdown = perf.get('max_drawdown', 0)
            table.add_row("最大回撤", f"{drawdown:.2f}%")
            
            # 胜率
            win_rate = perf.get('win_rate', 0)
            win_color = self.theme_colors['profit'] if win_rate >= 70 else self.theme_colors['warning']
            table.add_row("胜率", f"[{win_color}]{win_rate:.1f}%[/{win_color}]")
            
            # 交易次数
            total_trades = perf.get('total_trades', 0)
            table.add_row("总交易数", f"{total_trades}")
            
            return Panel(table, title="📊 性能指标", border_style=self.theme_colors['border'])
            
        except Exception as e:
            logger.error("performance_panel_creation_failed", error=str(e))
            return Panel("性能面板错误", border_style="red")
    
    def _create_alerts_panel(self) -> Panel:
        """创建警报面板"""
        try:
            alerts = self.dashboard_data.get('alerts', [])
            
            if not alerts:
                return Panel("🟢 无风险警报", title="⚠️  风险警报", border_style=self.theme_colors['border'])
            
            alert_text = Text()
            for alert in alerts[:5]:  # 最多显示5个警报
                severity = alert.get('severity', 'info')
                message = alert.get('message', '')
                
                # 根据严重程度设置颜色和图标
                if severity == 'critical':
                    icon = "🚨"
                    color = self.theme_colors['loss']
                elif severity == 'high':
                    icon = "⚠️"
                    color = self.theme_colors['warning']
                else:
                    icon = "ℹ️"
                    color = self.theme_colors['info']
                
                alert_text.append(f"{icon} ", style=color)
                alert_text.append(f"{message}\n", style=color)
            
            return Panel(alert_text, title="⚠️  风险警报", border_style=self.theme_colors['border'])
            
        except Exception as e:
            logger.error("alerts_panel_creation_failed", error=str(e))
            return Panel("警报面板错误", border_style="red")
    
    def _create_pools_panel(self) -> Panel:
        """创建池子面板"""
        try:
            pools = self.dashboard_data.get('pools', [])
            
            if not pools:
                return Panel("暂无优质池子", title="🏊 优质池子", border_style=self.theme_colors['border'])
            
            table = Table(show_header=True, header_style="bold cyan")
            table.add_column("交易对", style="white")
            table.add_column("评分", justify="center")
            table.add_column("APR", justify="right") 
            table.add_column("TVL", justify="right")
            
            for pool in pools[:5]:  # 最多显示5个池子
                score = pool.get('score', 0)
                score_color = (self.theme_colors['profit'] if score >= 80 
                             else self.theme_colors['warning'] if score >= 60 
                             else self.theme_colors['loss'])
                
                apr = pool.get('apr', 0)
                apr_color = self.theme_colors['profit'] if apr >= 15 else self.theme_colors['warning']
                
                tvl = pool.get('tvl', 0)
                tvl_str = f"${tvl/1000000:.1f}M" if tvl >= 1000000 else f"${tvl/1000:.0f}K"
                
                table.add_row(
                    pool.get('token_pair', 'N/A'),
                    f"[{score_color}]{score}[/{score_color}]",
                    f"[{apr_color}]{apr:.1f}%[/{apr_color}]",
                    tvl_str
                )
            
            return Panel(table, title="🏊 优质池子", border_style=self.theme_colors['border'])
            
        except Exception as e:
            logger.error("pools_panel_creation_failed", error=str(e))
            return Panel("池子面板错误", border_style="red")
    
    def _create_footer(self) -> Panel:
        """创建页脚"""
        try:
            last_updated = self.dashboard_data.get('last_updated')
            update_time = last_updated.strftime("%H:%M:%S") if last_updated else "N/A"
            
            footer_text = Text()
            footer_text.append(f"最后更新: {update_time}", style="dim white")
            footer_text.append(f"  |  更新次数: {self.update_count}", style="dim white")
            footer_text.append(f"  |  刷新间隔: {self.refresh_rate}秒", style="dim white")
            footer_text.append("  |  按 Ctrl+C 退出", style="dim yellow")
            
            return Panel(footer_text, border_style="dim blue")
            
        except Exception as e:
            logger.error("footer_creation_failed", error=str(e))
            return Panel("页脚错误", border_style="red")
    
    async def _start_live_dashboard(self, content: Layout) -> None:
        """启动实时仪表板"""
        try:
            self.live_display = Live(
                content,
                console=self.console,
                refresh_per_second=1/self.refresh_rate,
                screen=False
            )
            
            # 在后台启动 Live 显示
            self.live_display.start()
            self.is_dashboard_running = True
            
            logger.info("live_dashboard_started", refresh_rate=self.refresh_rate)
            
        except Exception as e:
            logger.error("live_dashboard_start_failed", error=str(e))
            self.is_dashboard_running = False
    
    def _update_live_display(self, content: Layout) -> None:
        """更新实时显示"""
        try:
            if self.live_display and self.is_dashboard_running:
                self.live_display.update(content)
                
        except Exception as e:
            logger.error("live_display_update_failed", error=str(e))
    
    async def cleanup(self) -> None:
        """清理资源"""
        try:
            # 停止 Live 显示
            if self.live_display and self.is_dashboard_running:
                self.live_display.stop()
                self.is_dashboard_running = False
            
            # 清理数据缓存
            self.dashboard_data.clear()
            
            logger.info("cli_reporter_cleanup_completed")
            
        except Exception as e:
            logger.error("cli_reporter_cleanup_failed", error=str(e))
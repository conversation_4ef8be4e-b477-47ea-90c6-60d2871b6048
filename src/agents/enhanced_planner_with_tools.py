from src.tools.pool_scanner_tool import PoolScannerTool
from src.tools.pool_scoring_tool import PoolScoringTool
from src.tools.supabase_db_tool import SupabaseDbTool
from src.tools.binance_hedge_tool import BinanceHedgeTool

class DataAnalyst:
    def __init__(self):
        self.scanner = PoolScannerTool()
        self.db = SupabaseDbTool()

    def analyze_data(self, chains):
        pools = self.scanner.scan_pools(chains)
        self.db.save({'pools': pools})
        return pools

class StrategyPlanner:
    def __init__(self):
        self.scorer = PoolScoringTool()
        self.hedge = BinanceHedgeTool()

    def plan_strategy(self, pools, capital):
        scores = self.scorer.score_pools(pools)
        hedge_plan = self.hedge.hedge_portfolio(scores, capital)
        return {'scores': scores, 'hedge_plan': hedge_plan}

class RiskManager:
    def __init__(self):
        self.db = SupabaseDbTool()

    def assess_risk(self, plan):
        # 假设有更复杂的风险评估逻辑
        risk_report = {'risk_level': 'medium', 'details': plan}
        self.db.save({'risk_report': risk_report})
        return risk_report

class EnhancedPlannerAgent:
    """
    增强版规划 Agent，集成所有自定义工具，支持对冲工具。
    包含 DataAnalyst、StrategyPlanner、RiskManager 三个专业角色。
    """

    def __init__(self, config=None):
        self.data_analyst = DataAnalyst()
        self.strategy_planner = StrategyPlanner()
        self.risk_manager = RiskManager()
        self.config = config

    def create_comprehensive_plan(self, chains=['bsc', 'solana'], capital=100000):
        pools = self.data_analyst.analyze_data(chains)
        plan = self.strategy_planner.plan_strategy(pools, capital)
        risk = self.risk_manager.assess_risk(plan)
        return {'plan': plan, 'risk': risk}

    async def create_comprehensive_plan_async(self, chains=['bsc', 'solana'], capital=100000):
        # 简化为同步调用，实际可根据需要引入异步
        return self.create_comprehensive_plan(chains, capital)

"""
Executor Agent - Dy-Flow v3
交易執行器，輸入 Plan 執行區塊鏈操作
支援 BSC PancakeSwap 和 Solana Meteora
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog
from web3 import Web3
from solana.rpc.async_api import AsyncClient

from .base_agent import BaseAgent
from ..utils.models_v3 import Plan, AgentResult
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class ExecutorAgent(BaseAgent):
    """Executor Agent - v3 交易執行系統"""
    
    def __init__(self, name: str, config, database):
        super().__init__(name, config, database)
        
        # v3 執行配置
        execution_config = getattr(config, 'execution', {}) if hasattr(config, 'execution') else {}
        self.max_gas_price_gwei = execution_config.get('max_gas_price_gwei', 20)
        self.max_slippage = execution_config.get('max_slippage', 0.03)  # 3%
        self.confirmation_blocks = execution_config.get('confirmation_blocks', 3)
        self.retry_attempts = execution_config.get('retry_attempts', 3)
        self.retry_delay = execution_config.get('retry_delay', 10)
        
        # 對沖配置
        self.hedge_config = execution_config.get('hedge', {
            'hedge_ratio': 0.80,  # 80% 對沖比例
            'max_hedge_delay': 300,  # 5分鐘內完成對沖
            'hedge_tokens': ['WETH', 'WBTC', 'SOL'],  # 可對沖代幣
            'perp_exchanges': ['binance', 'ftx']  # 永續合約交易所
        })
        
        # 網路配置
        self.networks = {
            'bsc': {
                'rpc_url': 'https://bsc-dataseed1.binance.org/',
                'chain_id': 56,
                'contracts': {
                    'pancake_router': '******************************************',
                    'pancake_factory': '******************************************'
                }
            },
            'solana': {
                'rpc_url': 'https://api.mainnet-beta.solana.com',
                'programs': {
                    'meteora_dlmm': 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'
                }
            }
        }
        
        # 客戶端
        self.web3_client: Optional[Web3] = None
        self.solana_client: Optional[AsyncClient] = None
        
        # 執行狀態
        self.pending_transactions: List[Dict[str, Any]] = []
        self.hedge_positions: List[Dict[str, Any]] = []
        self.execution_stats = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'total_gas_spent': 0.0,
            'hedge_success_rate': 0.0
        }
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        await self._initialize_networks()
        logger.info("executor_initialized",
                   networks=list(self.networks.keys()),
                   hedge_config=self.hedge_config)
        self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行計劃"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("executor_execution_started")
            
            # 1. 初始化網路連接
            await self._initialize_networks()
            
            # 2. 獲取待執行的計劃
            plans = await self._get_pending_plans()
            
            # 3. 檢查待處理交易狀態
            await self._update_pending_transactions()
            
            # 4. 執行計劃
            execution_results = []
            for plan in plans:
                try:
                    result = await self._execute_plan(plan)
                    execution_results.append(result)
                except Exception as e:
                    logger.error("plan_execution_failed", 
                                strategy=plan.strategy, 
                                error=str(e))
                    execution_results.append({
                        'success': False,
                        'plan_id': getattr(plan, 'id', 'unknown'),
                        'error': str(e)
                    })
            
            # 5. 更新統計
            self._update_execution_stats(execution_results)
            
            logger.info("executor_execution_completed",
                       plans_executed=len(plans),
                       successful_executions=len([r for r in execution_results if r.get('success')]))
            
            return AgentResult(
                agent_name=self.name,
                data=execution_results,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "plans_executed": len(plans),
                    "successful_executions": len([r for r in execution_results if r.get('success')]),
                    "execution_stats": self.execution_stats
                }
            )
            
        except Exception as e:
            logger.error("executor_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )
    
    async def _initialize_networks(self) -> None:
        """初始化網路連接"""
        try:
            # 初始化 BSC
            if not self.web3_client:
                bsc_config = self.networks['bsc']
                self.web3_client = Web3(Web3.HTTPProvider(bsc_config['rpc_url']))
                
                if self.web3_client.is_connected():
                    logger.info("bsc_client_initialized",
                               latest_block=self.web3_client.eth.block_number)
                else:
                    logger.warning("bsc_connection_failed")
            
            # 初始化 Solana
            if not self.solana_client:
                solana_config = self.networks['solana']
                self.solana_client = AsyncClient(solana_config['rpc_url'])
                
                try:
                    health = await self.solana_client.get_health()
                    logger.info("solana_client_initialized", health=health.value)
                except Exception as e:
                    logger.warning("solana_connection_failed", error=str(e))
                    
        except Exception as e:
            logger.error("network_initialization_failed", error=str(e))
            raise
    
    async def _get_pending_plans(self) -> List[Plan]:
        """獲取待執行的計劃"""
        # 實際實現中應該從數據庫獲取或接收來自 Planner 的數據
        # 這裡使用模擬數據
        return []
    
    async def _execute_plan(self, plan: Plan) -> Dict[str, Any]:
        """執行單個計劃"""
        try:
            logger.info("executing_plan",
                       strategy=plan.strategy,
                       pool_id=plan.pool_id)
            
            execution_results = []
            
            # 執行計劃中的配置
            plan_params = plan.params or {}
            allocations = plan_params.get('allocations', [])
            
            for allocation in allocations:
                try:
                    result = await self._execute_allocation(allocation, plan)
                    execution_results.append(result)
                    
                    # 如果是可對沖的位置，執行對沖
                    if allocation.get('hedge_ratio') and result.get('success'):
                        hedge_result = await self._execute_hedge(allocation, result)
                        if hedge_result:
                            execution_results.append(hedge_result)
                    
                except Exception as e:
                    logger.error("allocation_execution_failed", 
                                pool_id=allocation.get('pool_id'), 
                                error=str(e))
                    execution_results.append({
                        'success': False,
                        'pool_id': allocation.get('pool_id'),
                        'error': str(e)
                    })
            
            # 計算整體執行結果
            successful_allocations = [r for r in execution_results if r.get('success')]
            total_gas_cost = sum(r.get('gas_cost', 0) for r in execution_results)
            
            return {
                'success': len(successful_allocations) > 0,
                'plan_id': getattr(plan, 'id', 'unknown'),
                'strategy': plan.strategy,
                'allocations_executed': len(execution_results),
                'successful_allocations': len(successful_allocations),
                'total_gas_cost': total_gas_cost,
                'execution_details': execution_results,
                'executed_at': get_utc_timestamp()
            }
            
        except Exception as e:
            logger.error("plan_execution_failed", error=str(e))
            raise
    
    async def _execute_allocation(self, allocation: Dict[str, Any], plan: Plan) -> Dict[str, Any]:
        """執行單個配置"""
        try:
            pool_id = allocation['pool_id']
            action = allocation['action']
            amount_usd = allocation['allocation_usd']
            
            # 根據池子 ID 判斷鏈
            chain = self._determine_chain(pool_id)
            
            if chain == 'bsc':
                return await self._execute_bsc_allocation(allocation, plan)
            elif chain == 'solana':
                return await self._execute_solana_allocation(allocation, plan)
            else:
                raise DyFlowException(f"不支援的鏈: {chain}")
                
        except Exception as e:
            logger.error("allocation_execution_failed", error=str(e))
            return {
                'success': False,
                'pool_id': allocation.get('pool_id'),
                'error': str(e)
            }
    
    def _determine_chain(self, pool_id: str) -> str:
        """根據池子 ID 判斷鏈"""
        if pool_id.startswith('bsc_'):
            return 'bsc'
        elif pool_id.startswith('sol_'):
            return 'solana'
        else:
            # 根據其他規則判斷
            return 'bsc'  # 默認
    
    async def _execute_bsc_allocation(self, allocation: Dict[str, Any], plan: Plan) -> Dict[str, Any]:
        """執行 BSC 配置"""
        try:
            pool_id = allocation['pool_id']
            amount_usd = allocation['allocation_usd']
            action = allocation['action']
            
            # 估算 Gas
            gas_estimate = await self._estimate_bsc_gas(action)
            
            # 準備交易參數
            tx_params = self._prepare_bsc_transaction(allocation, gas_estimate)
            
            # 模擬交易執行（實際實現需要私鑰操作）
            tx_hash = f"0x{''.join(['a'] * 64)}"
            
            # 保存到待處理交易
            pending_tx = {
                'hash': tx_hash,
                'chain': 'bsc',
                'status': 'pending',
                'pool_id': pool_id,
                'action': action,
                'amount_usd': amount_usd,
                'submitted_at': get_utc_timestamp()
            }
            self.pending_transactions.append(pending_tx)
            
            return {
                'success': True,
                'pool_id': pool_id,
                'chain': 'bsc',
                'action': action,
                'amount_usd': amount_usd,
                'tx_hash': tx_hash,
                'gas_cost': gas_estimate['estimated_cost_usd'],
                'status': 'pending'
            }
            
        except Exception as e:
            logger.error("bsc_allocation_execution_failed", error=str(e))
            return {
                'success': False,
                'pool_id': allocation.get('pool_id'),
                'error': str(e)
            }
    
    async def _execute_solana_allocation(self, allocation: Dict[str, Any], plan: Plan) -> Dict[str, Any]:
        """執行 Solana 配置"""
        try:
            pool_id = allocation['pool_id']
            amount_usd = allocation['allocation_usd']
            action = allocation['action']
            
            # Solana/Meteora 特定邏輯
            gas_estimate = await self._estimate_solana_gas(action)
            
            # 準備 Meteora DLMM 交易
            tx_params = self._prepare_solana_transaction(allocation, gas_estimate)
            
            # 模擬交易執行
            tx_hash = f"solana_{''.join(['b'] * 64)}"
            
            pending_tx = {
                'hash': tx_hash,
                'chain': 'solana',
                'status': 'pending',
                'pool_id': pool_id,
                'action': action,
                'amount_usd': amount_usd,
                'submitted_at': get_utc_timestamp()
            }
            self.pending_transactions.append(pending_tx)
            
            return {
                'success': True,
                'pool_id': pool_id,
                'chain': 'solana',
                'action': action,
                'amount_usd': amount_usd,
                'tx_hash': tx_hash,
                'gas_cost': gas_estimate['estimated_cost_usd'],
                'status': 'pending'
            }
            
        except Exception as e:
            logger.error("solana_allocation_execution_failed", error=str(e))
            return {
                'success': False,
                'pool_id': allocation.get('pool_id'),
                'error': str(e)
            }
    
    async def _execute_hedge(self, allocation: Dict[str, Any], execution_result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """執行對沖操作"""
        try:
            hedge_ratio = allocation.get('hedge_ratio', self.hedge_config['hedge_ratio'])
            amount_usd = allocation['allocation_usd']
            hedge_amount = amount_usd * hedge_ratio
            
            # 提取代幣信息
            pool_id = allocation['pool_id']
            tokens = self._extract_tokens_from_pool_id(pool_id)
            
            # 選擇可對沖的代幣
            hedgeable_token = None
            for token in tokens:
                if token in self.hedge_config['hedge_tokens']:
                    hedgeable_token = token
                    break
            
            if not hedgeable_token:
                logger.warning("no_hedgeable_token_found", 
                             pool_id=pool_id, 
                             tokens=tokens)
                return None
            
            # 執行永續合約對沖
            hedge_result = await self._execute_perp_hedge(
                hedgeable_token, 
                hedge_amount, 
                'short'  # 做空對沖
            )
            
            if hedge_result['success']:
                hedge_position = {
                    'original_pool_id': pool_id,
                    'hedge_token': hedgeable_token,
                    'hedge_amount': hedge_amount,
                    'hedge_exchange': hedge_result.get('exchange'),
                    'hedge_position_id': hedge_result.get('position_id'),
                    'created_at': get_utc_timestamp()
                }
                self.hedge_positions.append(hedge_position)
                
                logger.info("hedge_executed_successfully", 
                           token=hedgeable_token, 
                           amount=hedge_amount)
            
            return hedge_result
            
        except Exception as e:
            logger.error("hedge_execution_failed", error=str(e))
            return {
                'success': False,
                'type': 'hedge',
                'error': str(e)
            }
    
    def _extract_tokens_from_pool_id(self, pool_id: str) -> List[str]:
        """從池子 ID 提取代幣"""
        # 簡化的實現，實際需要更複雜的解析
        parts = pool_id.split('_')
        if len(parts) >= 3:
            return [parts[1], parts[2]]
        return []
    
    async def _execute_perp_hedge(self, token: str, amount_usd: float, direction: str) -> Dict[str, Any]:
        """執行永續合約對沖"""
        try:
            # 這裡應該集成真實的永續合約交易所 API
            # 模擬實現
            
            exchange = self.hedge_config['perp_exchanges'][0]  # 使用第一個交易所
            position_id = f"hedge_{token}_{int(get_utc_timestamp().timestamp())}"
            
            # 模擬對沖成功
            return {
                'success': True,
                'type': 'hedge',
                'token': token,
                'amount_usd': amount_usd,
                'direction': direction,
                'exchange': exchange,
                'position_id': position_id,
                'executed_at': get_utc_timestamp()
            }
            
        except Exception as e:
            logger.error("perp_hedge_execution_failed", 
                        token=token, 
                        amount=amount_usd, 
                        error=str(e))
            return {
                'success': False,
                'type': 'hedge',
                'error': str(e)
            }
    
    async def _estimate_bsc_gas(self, action: str) -> Dict[str, Any]:
        """估算 BSC Gas 費用"""
        try:
            gas_estimates = {
                'enter': 300000,
                'exit': 250000,
                'rebalance': 400000
            }
            
            gas_limit = gas_estimates.get(action, 200000)
            gas_price_wei = self.web3_client.eth.gas_price if self.web3_client else 5000000000
            gas_price_gwei = gas_price_wei / 10**9
            
            # 限制最大 Gas 價格
            if gas_price_gwei > self.max_gas_price_gwei:
                gas_price_gwei = self.max_gas_price_gwei
                gas_price_wei = int(gas_price_gwei * 10**9)
            
            estimated_cost_eth = (gas_limit * gas_price_wei) / 10**18
            bnb_price_usd = 300  # 模擬 BNB 價格
            estimated_cost_usd = estimated_cost_eth * bnb_price_usd
            
            return {
                'gas_limit': gas_limit,
                'gas_price_wei': gas_price_wei,
                'gas_price_gwei': gas_price_gwei,
                'estimated_cost_usd': estimated_cost_usd
            }
            
        except Exception as e:
            logger.error("bsc_gas_estimation_failed", error=str(e))
            return {
                'gas_limit': 300000,
                'gas_price_wei': 5000000000,
                'estimated_cost_usd': 5.0
            }
    
    async def _estimate_solana_gas(self, action: str) -> Dict[str, Any]:
        """估算 Solana Gas 費用"""
        try:
            base_fee_sol = 0.000005
            sol_price_usd = 100  # 模擬 SOL 價格
            estimated_cost_usd = base_fee_sol * sol_price_usd
            
            return {
                'gas_limit': 1,
                'gas_price_sol': base_fee_sol,
                'estimated_cost_usd': estimated_cost_usd
            }
            
        except Exception as e:
            logger.error("solana_gas_estimation_failed", error=str(e))
            return {
                'gas_limit': 1,
                'gas_price_sol': 0.000005,
                'estimated_cost_usd': 0.0005
            }
    
    def _prepare_bsc_transaction(self, allocation: Dict[str, Any], gas_estimate: Dict[str, Any]) -> Dict[str, Any]:
        """準備 BSC 交易參數"""
        return {
            'chainId': self.networks['bsc']['chain_id'],
            'gas': gas_estimate['gas_limit'],
            'gasPrice': gas_estimate['gas_price_wei'],
            'to': self.networks['bsc']['contracts']['pancake_router'],
            'value': 0,
            'data': '0x'  # 應該包含編碼的函數調用
        }
    
    def _prepare_solana_transaction(self, allocation: Dict[str, Any], gas_estimate: Dict[str, Any]) -> Dict[str, Any]:
        """準備 Solana 交易參數"""
        return {
            'program_id': self.networks['solana']['programs']['meteora_dlmm'],
            'accounts': [],
            'instruction_data': b'',
            'recent_blockhash': None
        }
    
    async def _update_pending_transactions(self) -> None:
        """更新待處理交易狀態"""
        try:
            for tx in self.pending_transactions[:]:
                try:
                    status = await self._check_transaction_status(tx)
                    if status != tx.get('status'):
                        tx['status'] = status
                        tx['updated_at'] = get_utc_timestamp()
                        
                        if status in ['confirmed', 'failed']:
                            self.pending_transactions.remove(tx)
                            
                except Exception as e:
                    logger.error("transaction_status_check_failed", 
                                tx_hash=tx.get('hash'), 
                                error=str(e))
                    
        except Exception as e:
            logger.error("pending_transactions_update_failed", error=str(e))
    
    async def _check_transaction_status(self, tx: Dict[str, Any]) -> str:
        """檢查交易狀態"""
        try:
            chain = tx.get('chain')
            tx_hash = tx.get('hash')
            
            if chain == 'bsc' and self.web3_client:
                try:
                    receipt = self.web3_client.eth.get_transaction_receipt(tx_hash)
                    if receipt.status == 1:
                        current_block = self.web3_client.eth.block_number
                        confirmations = current_block - receipt.blockNumber
                        return 'confirmed' if confirmations >= self.confirmation_blocks else 'pending'
                    else:
                        return 'failed'
                except Exception:
                    return 'pending'
                    
            elif chain == 'solana' and self.solana_client:
                # Solana 交易狀態檢查邏輯
                return 'pending'
            
            return 'unknown'
            
        except Exception as e:
            logger.error("transaction_status_check_failed", error=str(e))
            return 'unknown'
    
    def _update_execution_stats(self, results: List[Dict[str, Any]]) -> None:
        """更新執行統計"""
        try:
            total = len(results)
            successful = len([r for r in results if r.get('success')])
            failed = total - successful
            total_gas = sum(r.get('total_gas_cost', 0) for r in results)
            
            self.execution_stats['total_executions'] += total
            self.execution_stats['successful_executions'] += successful
            self.execution_stats['failed_executions'] += failed
            self.execution_stats['total_gas_spent'] += total_gas
            
            # 計算對沖成功率
            hedge_results = []
            for result in results:
                execution_details = result.get('execution_details', [])
                hedge_results.extend([d for d in execution_details if d.get('type') == 'hedge'])
            
            if hedge_results:
                hedge_success = len([h for h in hedge_results if h.get('success')])
                self.execution_stats['hedge_success_rate'] = hedge_success / len(hedge_results)
                
        except Exception as e:
            logger.error("execution_stats_update_failed", error=str(e))
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            if self.solana_client:
                await self.solana_client.close()
            
            self.pending_transactions.clear()
            self.hedge_positions.clear()
            
            logger.info("executor_cleanup_completed",
                       execution_stats=self.execution_stats)
                       
        except Exception as e:
            logger.error("executor_cleanup_failed", error=str(e))
"""
Planner Agent - Dy-Flow v3
策略規劃師，輸入 PoolScore 輸出 Plan
實現三大策略：Active、Hedge、Passive
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import structlog

from .base_agent import BaseAgent
from ..utils.models_v3 import PoolScore, Plan, AgentResult
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class PlannerAgent(BaseAgent):
    """Planner Agent - v3 策略規劃系統"""
    
    def __init__(self, name: str, config, database):
        super().__init__(name, config, database)
        
        # Agent-specific config from agno_flow.yaml or default.yaml (via BaseAgent)
        agent_specific_config = self.agent_config or {}
        
        # Main strategy and risk config
        main_strategy_config = self.config.strategy
        main_risk_config = self.config.risk_management

        # Define default structures for strategies, decision_rules, and portfolio_limits
        # These can be overridden by agent_specific_config

        default_strategies_config = {
            'active': {
                'min_score': agent_specific_config.get('active_min_score', 70),
                'max_positions': agent_specific_config.get('active_max_positions', main_strategy_config.max_pools_per_chain),
                'max_allocation_per_position': agent_specific_config.get('active_max_alloc_per_pos', main_strategy_config.allocation.get("max_per_pool", 0.20)),
                'rebalance_threshold': agent_specific_config.get('active_rebalance_threshold', main_strategy_config.rebalance_threshold)
            },
            'hedge': {
                'min_score': agent_specific_config.get('hedge_min_score', 60),
                'max_positions': agent_specific_config.get('hedge_max_positions', 3), # Typically fewer for hedge
                'max_allocation_per_position': agent_specific_config.get('hedge_max_alloc_per_pos', main_strategy_config.allocation.get("max_per_pool", 0.30) * 1.5), # Hedge might allow larger single positions
                'hedge_ratio': agent_specific_config.get('hedge_ratio', 0.80)
            },
            'passive': {
                'min_score': agent_specific_config.get('passive_min_score', 50),
                'max_positions': agent_specific_config.get('passive_max_positions', 2), # Fewer, larger positions
                'max_allocation_per_position': agent_specific_config.get('passive_max_alloc_per_pos', main_strategy_config.allocation.get("max_per_pool", 0.40) * 2), # Passive might allow even larger
                'min_tvl': agent_specific_config.get('passive_min_tvl', main_strategy_config.min_tvl * 10) # Passive usually targets very high TVL pools
            }
        }
        self.strategies = agent_specific_config.get('strategies', default_strategies_config)
        # Deep update if agent_specific_config.strategies exists
        if 'strategies' in agent_specific_config and isinstance(agent_specific_config['strategies'], dict):
            for strategy_name, params in agent_specific_config['strategies'].items():
                if strategy_name in self.strategies and isinstance(params, dict):
                    self.strategies[strategy_name].update(params)

        default_decision_rules = {
            'enter_threshold': agent_specific_config.get('enter_threshold', 0.60),
            'exit_threshold': agent_specific_config.get('exit_threshold', main_risk_config.emergency_exit_threshold * 2), # Example: relate to risk config
            'rebalance_price_deviation': agent_specific_config.get('rebalance_price_deviation', main_strategy_config.rebalance_threshold * 4), # Example
            'fee_gas_ratio': agent_specific_config.get('fee_gas_ratio', 1.2)
        }
        self.decision_rules = agent_specific_config.get('decision_rules', default_decision_rules)
        if 'decision_rules' in agent_specific_config and isinstance(agent_specific_config['decision_rules'], dict):
            self.decision_rules.update(agent_specific_config['decision_rules'])

        default_portfolio_limits = {
            'max_total_allocation': agent_specific_config.get('max_total_allocation', 1.0 - main_strategy_config.allocation.get("reserve_ratio", 0.1)),
            'min_cash_reserve': agent_specific_config.get('min_cash_reserve', main_strategy_config.allocation.get("reserve_ratio", 0.1)),
            'max_chain_concentration': agent_specific_config.get('max_chain_concentration', main_strategy_config.allocation.get("cross_chain_ratio", 0.5) * 1.5), # Max per single chain
            'max_protocol_concentration': agent_specific_config.get('max_protocol_concentration', 0.60) # This might need a new config field
        }
        self.portfolio_limits = agent_specific_config.get('portfolio_limits', default_portfolio_limits)
        if 'portfolio_limits' in agent_specific_config and isinstance(agent_specific_config['portfolio_limits'], dict):
            self.portfolio_limits.update(agent_specific_config['portfolio_limits'])
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        logger.info("planner_initialized",
                   strategies=list(self.strategies.keys()),
                   portfolio_limits=self.portfolio_limits)
        self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行策略規劃"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("planner_execution_started")
            
            # 1. 獲取 PoolScore 數據
            pool_scores = await self._get_pool_scores()
            
            # 2. 獲取當前投資組合狀態
            portfolio_state = await self._get_portfolio_state()
            
            # 3. 為每種策略生成計劃
            plans = []
            
            # Active 策略
            active_plan = self._generate_active_plan(pool_scores, portfolio_state)
            if active_plan:
                plans.append(active_plan)
            
            # Hedge 策略
            hedge_plan = self._generate_hedge_plan(pool_scores, portfolio_state)
            if hedge_plan:
                plans.append(hedge_plan)
            
            # Passive 策略
            passive_plan = self._generate_passive_plan(pool_scores, portfolio_state)
            if passive_plan:
                plans.append(passive_plan)
            
            # 4. 選擇最佳策略
            best_plan = self._select_best_plan(plans, portfolio_state)
            
            # 5. 驗證和優化計劃
            if best_plan:
                optimized_plan = self._optimize_plan(best_plan, portfolio_state)
                final_plans = [optimized_plan] if optimized_plan else []
            else:
                final_plans = []
            
            logger.info("planner_execution_completed",
                       pools_analyzed=len(pool_scores),
                       strategies_evaluated=len(plans),
                       final_plans=len(final_plans))
            
            return AgentResult(
                agent_name=self.name,
                data=final_plans,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "pools_analyzed": len(pool_scores),
                    "strategies_evaluated": len(plans),
                    "best_strategy": best_plan.strategy if best_plan else None,
                    "total_allocation": best_plan.params.get('total_allocation', 0) if best_plan else 0
                }
            )
            
        except Exception as e:
            logger.error("planner_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )
    
    async def _get_pool_scores(self) -> List[PoolScore]:
        """獲取 PoolScore 數據"""
        # 實際實現中應該從數據庫獲取或接收來自 Scorer 的數據
        # 這裡使用模擬數據
        mock_scores = [
            PoolScore(id="bsc_wbnb_usdt", score=75.5, hedgeable=True),
            PoolScore(id="eth_eth_usdc", score=68.2, hedgeable=True),
            PoolScore(id="sol_sol_usdc", score=82.1, hedgeable=True),
            PoolScore(id="bsc_cake_wbnb", score=45.3, hedgeable=False),
            PoolScore(id="eth_uni_eth", score=58.7, hedgeable=True)
        ]
        return mock_scores
    
    async def _get_portfolio_state(self) -> Dict[str, Any]:
        """獲取投資組合狀態"""
        # 模擬投資組合狀態
        return {
            "total_value": 100000,  # $100k
            "cash_available": 40000,  # $40k 可用現金
            "current_positions": 2,
            "total_allocation": 0.60,  # 60% 已配置
            "chain_allocations": {
                "BSC": 0.30,
                "ETH": 0.30,
                "SOL": 0.00
            },
            "protocol_allocations": {
                "pancakeswap": 0.30,
                "uniswap": 0.30
            },
            "risk_tolerance": "medium",
            "investment_horizon": "medium"  # 3-12個月
        }
    
    def _generate_active_plan(self, pool_scores: List[PoolScore], 
                            portfolio_state: Dict[str, Any]) -> Optional[Plan]:
        """生成 Active 策略計劃"""
        try:
            active_config = self.strategies['active']
            
            # 篩選高分池子
            eligible_pools = [
                pool for pool in pool_scores 
                if pool.score >= active_config['min_score']
            ]
            
            if not eligible_pools:
                return None
            
            # 按評分排序，選擇前N個
            top_pools = sorted(eligible_pools, key=lambda p: p.score, reverse=True)
            top_pools = top_pools[:active_config['max_positions']]
            
            # 計算配置
            total_value = portfolio_state['total_value']
            available_cash = portfolio_state['cash_available']
            
            allocations = []
            remaining_cash = available_cash
            
            for i, pool in enumerate(top_pools):
                # 平均分配，但不超過單個位置限制
                target_allocation = min(
                    available_cash / len(top_pools),
                    total_value * active_config['max_allocation_per_position']
                )
                
                if target_allocation <= remaining_cash and target_allocation >= 5000:  # 最小 $5k
                    allocations.append({
                        "pool_id": pool.id,
                        "allocation_usd": target_allocation,
                        "allocation_pct": target_allocation / total_value,
                        "score": pool.score,
                        "action": "enter"
                    })
                    remaining_cash -= target_allocation
            
            if not allocations:
                return None
            
            total_allocation = sum(a['allocation_usd'] for a in allocations)
            
            return Plan(
                pool_id=allocations[0]['pool_id'] if allocations else "multiple",
                strategy="delta_neutral_lp",  # 使用 v3 標準策略名
                action="enter",
                params={
                    "target_pools": len(allocations),
                    "total_allocation": total_allocation / total_value,
                    "allocations": allocations,
                    "expected_return": self._calculate_expected_return(allocations, pool_scores),
                    "risk_level": "medium"
                }
            )
            
        except Exception as e:
            logger.error("active_plan_generation_failed", error=str(e))
            return None
    
    def _generate_hedge_plan(self, pool_scores: List[PoolScore], 
                           portfolio_state: Dict[str, Any]) -> Optional[Plan]:
        """生成 Hedge 策略計劃"""
        try:
            hedge_config = self.strategies['hedge']
            
            # 篩選可對沖的高分池子
            eligible_pools = [
                pool for pool in pool_scores 
                if pool.score >= hedge_config['min_score'] and pool.hedgeable
            ]
            
            if not eligible_pools:
                return None
            
            # 選擇最佳對沖池子
            hedge_pools = sorted(eligible_pools, key=lambda p: p.score, reverse=True)
            hedge_pools = hedge_pools[:hedge_config['max_positions']]
            
            # 計算對沖配置
            total_value = portfolio_state['total_value']
            available_cash = portfolio_state['cash_available']
            
            allocations = []
            remaining_cash = available_cash
            
            for pool in hedge_pools:
                # 對沖策略採用較大單個配置
                target_allocation = min(
                    available_cash * 0.6 / len(hedge_pools),  # 60% 現金用於對沖
                    total_value * hedge_config['max_allocation_per_position']
                )
                
                if target_allocation <= remaining_cash and target_allocation >= 10000:  # 最小 $10k
                    allocations.append({
                        "pool_id": pool.id,
                        "allocation_usd": target_allocation,
                        "allocation_pct": target_allocation / total_value,
                        "score": pool.score,
                        "action": "enter",
                        "hedge_ratio": hedge_config['hedge_ratio']
                    })
                    remaining_cash -= target_allocation
            
            if not allocations:
                return None
            
            total_allocation = sum(a['allocation_usd'] for a in allocations)
            
            return Plan(
                pool_id=allocations[0]['pool_id'] if allocations else "multiple",
                strategy="delta_neutral_lp",  # 對沖策略也使用 delta_neutral_lp
                action="enter",
                params={
                    "target_pools": len(allocations),
                    "total_allocation": total_allocation / total_value,
                    "allocations": allocations,
                    "expected_return": self._calculate_expected_return(allocations, pool_scores) * 0.8,
                    "risk_level": "low",
                    "hedge_ratio": hedge_config['hedge_ratio']
                }
            )
            
        except Exception as e:
            logger.error("hedge_plan_generation_failed", error=str(e))
            return None
    
    def _generate_passive_plan(self, pool_scores: List[PoolScore], 
                             portfolio_state: Dict[str, Any]) -> Optional[Plan]:
        """生成 Passive 策略計劃"""
        try:
            passive_config = self.strategies['passive']
            
            # 篩選穩定的高TVL池子
            eligible_pools = [
                pool for pool in pool_scores 
                if pool.score >= passive_config['min_score']
            ]
            
            # 優先選擇穩定幣對或藍籌幣對
            stable_pools = []
            for pool in eligible_pools:
                # 這裡應該根據實際池子信息判斷
                # 模擬判斷：包含 USDT/USDC 或 ETH/BTC 的為穩定池子
                if any(token in pool.id.upper() for token in ['USDT', 'USDC', 'ETH', 'BTC']):
                    stable_pools.append(pool)
            
            if not stable_pools:
                stable_pools = eligible_pools
            
            # 選擇前幾個最穩定的池子
            passive_pools = sorted(stable_pools, key=lambda p: p.score, reverse=True)
            passive_pools = passive_pools[:passive_config['max_positions']]
            
            if not passive_pools:
                return None
            
            # 計算被動配置 - 大額集中投資
            total_value = portfolio_state['total_value']
            available_cash = portfolio_state['cash_available']
            
            allocations = []
            
            for i, pool in enumerate(passive_pools):
                # 被動策略採用大額配置
                if i == 0:  # 主要池子
                    target_allocation = min(
                        available_cash * 0.7,  # 70% 現金投入主池子
                        total_value * passive_config['max_allocation_per_position']
                    )
                else:  # 次要池子
                    target_allocation = min(
                        available_cash * 0.3,  # 30% 現金投入次要池子
                        total_value * 0.20
                    )
                
                if target_allocation >= 15000:  # 最小 $15k
                    allocations.append({
                        "pool_id": pool.id,
                        "allocation_usd": target_allocation,
                        "allocation_pct": target_allocation / total_value,
                        "score": pool.score,
                        "action": "enter"
                    })
            
            if not allocations:
                return None
            
            total_allocation = sum(a['allocation_usd'] for a in allocations)
            
            return Plan(
                pool_id=allocations[0]['pool_id'] if allocations else "multiple",
                strategy="passive_high_tvl",  # 使用 v3 標準策略名
                action="enter",
                params={
                    "target_pools": len(allocations),
                    "total_allocation": total_allocation / total_value,
                    "allocations": allocations,
                    "expected_return": self._calculate_expected_return(allocations, pool_scores) * 0.9,
                    "risk_level": "low"
                }
            )
            
        except Exception as e:
            logger.error("passive_plan_generation_failed", error=str(e))
            return None
    
    def _select_best_plan(self, plans: List[Plan], 
                         portfolio_state: Dict[str, Any]) -> Optional[Plan]:
        """選擇最佳策略計劃"""
        if not plans:
            return None
        
        try:
            # 根據風險偏好選擇策略
            risk_tolerance = portfolio_state.get('risk_tolerance', 'medium')
            investment_horizon = portfolio_state.get('investment_horizon', 'medium')
            
            scored_plans = []
            for plan in plans:
                score = 0
                
                # 預期收益評分
                score += plan.params.get('expected_return', 0) * 40
                
                # 風險匹配評分
                plan_risk_level = plan.params.get('risk_level', 'medium')
                if risk_tolerance == 'low' and plan_risk_level == 'low':
                    score += 30
                elif risk_tolerance == 'medium' and plan_risk_level == 'medium':
                    score += 30
                elif risk_tolerance == 'high' and plan.strategy == 'active':
                    score += 30
                
                # 策略適配評分
                if investment_horizon == 'short' and plan.strategy == 'active':
                    score += 20
                elif investment_horizon == 'medium' and plan.strategy in ['active', 'hedge']:
                    score += 20
                elif investment_horizon == 'long' and plan.strategy == 'passive':
                    score += 20
                
                # 分散化評分
                target_pools = plan.params.get('target_pools', 1)
                if target_pools >= 2:
                    score += 10
                
                scored_plans.append((plan, score))
            
            # 選擇評分最高的計劃
            best_plan = max(scored_plans, key=lambda x: x[1])[0]
            
            logger.info("best_plan_selected",
                       strategy=best_plan.strategy,
                       target_pools=best_plan.params.get('target_pools', 0),
                       expected_return=best_plan.params.get('expected_return', 0))
            
            return best_plan
            
        except Exception as e:
            logger.error("plan_selection_failed", error=str(e))
            return plans[0] if plans else None
    
    def _optimize_plan(self, plan: Plan, portfolio_state: Dict[str, Any]) -> Optional[Plan]:
        """優化計劃"""
        try:
            # 檢查投資組合限制
            total_value = portfolio_state['total_value']
            current_allocation = portfolio_state.get('total_allocation', 0)
            
            # 確保不超過最大總配置
            max_new_allocation = self.portfolio_limits['max_total_allocation'] - current_allocation
            
            plan_total_allocation = plan.params.get('total_allocation', 0)
            plan_allocations = plan.params.get('allocations', [])
            
            if plan_total_allocation > max_new_allocation:
                # 按比例縮減配置
                scale_factor = max_new_allocation / plan_total_allocation
                
                optimized_allocations = []
                for allocation in plan_allocations:
                    new_allocation = allocation['allocation_usd'] * scale_factor
                    if new_allocation >= 5000:  # 保持最小投資額
                        allocation_copy = allocation.copy()
                        allocation_copy['allocation_usd'] = new_allocation
                        allocation_copy['allocation_pct'] = new_allocation / total_value
                        optimized_allocations.append(allocation_copy)
                
                if optimized_allocations:
                    plan.params['allocations'] = optimized_allocations
                    plan.params['total_allocation'] = sum(a['allocation_usd'] for a in optimized_allocations) / total_value
                else:
                    return None
            
            # 檢查鏈集中度
            chain_allocations = {}
            for allocation in plan.params.get('allocations', []):
                chain = allocation['pool_id'].split('_')[0]  # 簡化的鏈提取
                chain_allocations[chain] = chain_allocations.get(chain, 0) + allocation['allocation_pct']
            
            # 如果單鏈配置過高，進行調整
            for chain, allocation_pct in chain_allocations.items():
                if allocation_pct > self.portfolio_limits['max_chain_concentration']:
                    logger.warning("chain_concentration_too_high", 
                                 chain=chain, 
                                 allocation=allocation_pct)
                    # 可以在這裡實現更複雜的重新平衡邏輯
            
            return plan
            
        except Exception as e:
            logger.error("plan_optimization_failed", error=str(e))
            return plan
    
    def _calculate_expected_return(self, allocations: List[Dict[str, Any]], 
                                 pool_scores: List[PoolScore]) -> float:
        """計算預期收益"""
        try:
            total_allocation = sum(a['allocation_usd'] for a in allocations)
            if total_allocation == 0:
                return 0.0
            
            weighted_return = 0.0
            
            for allocation in allocations:
                # 簡化：假設收益與評分成正比
                score = allocation['score']
                weight = allocation['allocation_usd'] / total_allocation
                
                # 評分轉換為年化收益預期 (0-100分對應0-50%年化)
                expected_apy = (score / 100) * 0.50
                
                weighted_return += expected_apy * weight
            
            return weighted_return
            
        except Exception as e:
            logger.error("expected_return_calculation_failed", error=str(e))
            return 0.0
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("planner_cleanup_completed")
        except Exception as e:
            logger.error("planner_cleanup_failed", error=str(e))

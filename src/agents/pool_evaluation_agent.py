"""
池子评估Agent - 完整的评估流程
接收池子信息后，根据多种方法进行系统性评估
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import structlog

# Agno Framework 导入
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.tools.reasoning import ReasoningTools
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

logger = structlog.get_logger(__name__)


@dataclass
class PoolEvaluationResult:
    """池子评估结果"""
    pool_id: str
    pool_name: str
    
    # 基础信息
    token0: str
    token1: str
    tvl_usd: float
    volume_24h: float
    fee_tier: float
    
    # 评分结果
    overall_score: float  # 0-100
    risk_score: float     # 0-100, 越高越危险
    yield_score: float    # 0-100, 收益潜力
    liquidity_score: float # 0-100, 流动性评分
    
    # 详细分析
    factor_scores: Dict[str, float]
    risk_analysis: Dict[str, Any]
    ai_insights: Optional[str]
    
    # 投资建议
    recommendation: str  # "BUY", "HOLD", "AVOID"
    confidence_level: float  # 0-1
    hedgeable: bool
    
    # 元数据
    evaluation_timestamp: datetime
    evaluation_method: str


class PoolEvaluationAgent:
    """池子评估Agent - 完整评估流程"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.agno_available = AGNO_AVAILABLE
        
        # 评估权重配置
        self.weights = {
            'fee_score': 0.30,      # 手续费收益权重
            'volume_score': 0.25,   # 交易量权重
            'tvl_score': 0.20,      # TVL权重
            'liquidity_score': 0.15, # 流动性深度权重
            'risk_score': -0.10     # 风险调整权重（负值）
        }
        
        # 评估阈值
        self.thresholds = {
            'min_tvl': 10000,       # 最小TVL $10K
            'min_volume_24h': 5000, # 最小24h交易量 $5K
            'min_score': 60.0,      # 最小推荐分数
            'max_risk': 70.0        # 最大风险分数
        }
        
        # AI Agent
        self.ai_agent = None
        
    async def initialize(self):
        """初始化Agent"""
        if self.agno_available:
            try:
                self.ai_agent = Agent(
                    name="DeFiPoolEvaluator",
                    role="Professional DeFi pool analyst and investment advisor",
                    model=OpenAIChat(id="gpt-3.5-turbo"),
                    tools=[ReasoningTools(add_instructions=True)],
                    instructions=[
                        "你是专业的DeFi池子分析师和投资顾问。",
                        "分析池子数据并提供投资建议。",
                        "重点关注风险评估、收益潜力和市场条件。",
                        "提供清晰的投资建议：BUY、HOLD或AVOID。",
                        "用中文回答，保持专业和客观。"
                    ],
                    reasoning=False,
                    show_tool_calls=False
                )
                logger.info("pool_evaluation_agent_initialized", ai_available=True)
            except Exception as e:
                logger.warning("ai_agent_init_failed", error=str(e))
                self.ai_agent = None
        else:
            logger.info("pool_evaluation_agent_initialized", ai_available=False)
    
    async def evaluate_pool(self, pool_data: Dict[str, Any]) -> PoolEvaluationResult:
        """
        评估单个池子
        
        Args:
            pool_data: 池子数据字典，包含基础信息
            
        Returns:
            完整的评估结果
        """
        try:
            logger.info("pool_evaluation_started", pool_id=pool_data.get('id', 'unknown'))
            
            # 1. 数据预处理和验证
            processed_data = self._preprocess_pool_data(pool_data)
            if not processed_data:
                raise ValueError("池子数据预处理失败")
            
            # 2. 基础过滤
            if not self._meets_basic_criteria(processed_data):
                return self._create_rejection_result(processed_data, "不满足基础筛选条件")
            
            # 3. 计算各项评分
            factor_scores = self._calculate_factor_scores(processed_data)
            
            # 4. 风险分析
            risk_analysis = self._analyze_risks(processed_data)
            
            # 5. 计算综合评分
            overall_score = self._calculate_overall_score(factor_scores, risk_analysis)
            
            # 6. AI增强分析（如果可用）
            ai_insights = await self._get_ai_insights(processed_data, factor_scores, risk_analysis)
            
            # 7. 生成投资建议
            recommendation, confidence = self._generate_recommendation(
                overall_score, risk_analysis, factor_scores
            )
            
            # 8. 判断是否可对冲
            hedgeable = self._is_hedgeable(processed_data)
            
            # 9. 创建评估结果
            result = PoolEvaluationResult(
                pool_id=processed_data['id'],
                pool_name=f"{processed_data['token0']}/{processed_data['token1']}",
                token0=processed_data['token0'],
                token1=processed_data['token1'],
                tvl_usd=processed_data['tvl_usd'],
                volume_24h=processed_data['volume_24h'],
                fee_tier=processed_data['fee_tier'],
                overall_score=overall_score,
                risk_score=risk_analysis['overall_risk_score'],
                yield_score=factor_scores['fee_score'],
                liquidity_score=factor_scores['liquidity_score'],
                factor_scores=factor_scores,
                risk_analysis=risk_analysis,
                ai_insights=ai_insights,
                recommendation=recommendation,
                confidence_level=confidence,
                hedgeable=hedgeable,
                evaluation_timestamp=datetime.now(),
                evaluation_method="comprehensive_v1"
            )
            
            logger.info("pool_evaluation_completed", 
                       pool_id=processed_data['id'],
                       score=overall_score,
                       recommendation=recommendation)
            
            return result
            
        except Exception as e:
            logger.error("pool_evaluation_failed", 
                        pool_id=pool_data.get('id', 'unknown'),
                        error=str(e))
            raise
    
    async def evaluate_multiple_pools(self, pools_data: List[Dict[str, Any]]) -> List[PoolEvaluationResult]:
        """
        批量评估多个池子
        
        Args:
            pools_data: 池子数据列表
            
        Returns:
            评估结果列表，按评分排序
        """
        logger.info("batch_pool_evaluation_started", pool_count=len(pools_data))
        
        results = []
        for pool_data in pools_data:
            try:
                result = await self.evaluate_pool(pool_data)
                results.append(result)
            except Exception as e:
                logger.warning("pool_evaluation_skipped", 
                             pool_id=pool_data.get('id', 'unknown'),
                             error=str(e))
                continue
        
        # 按评分排序
        results.sort(key=lambda x: x.overall_score, reverse=True)
        
        logger.info("batch_pool_evaluation_completed", 
                   total_pools=len(pools_data),
                   successful_evaluations=len(results))
        
        return results
    
    def _preprocess_pool_data(self, pool_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """预处理池子数据"""
        try:
            # 提取和标准化关键字段
            processed = {
                'id': pool_data.get('id', ''),
                'token0': pool_data.get('token0', {}).get('symbol', '') if isinstance(pool_data.get('token0'), dict) else str(pool_data.get('token0', '')),
                'token1': pool_data.get('token1', {}).get('symbol', '') if isinstance(pool_data.get('token1'), dict) else str(pool_data.get('token1', '')),
                'tvl_usd': float(pool_data.get('totalValueLockedUSD', pool_data.get('tvl_usd', 0))),
                'volume_24h': float(pool_data.get('volumeUSD', pool_data.get('volume_24h', 0))),
                'fee_tier': float(pool_data.get('feeTier', 0)),
                'liquidity': float(pool_data.get('liquidity', 0)),
                'token0_price': float(pool_data.get('token0Price', 0)),
                'token1_price': float(pool_data.get('token1Price', 0)),
                'chain': pool_data.get('chain', 'bsc').lower()
            }
            
            # 计算衍生指标
            if processed['tvl_usd'] > 0 and processed['volume_24h'] > 0:
                processed['volume_tvl_ratio'] = processed['volume_24h'] / processed['tvl_usd']
                processed['daily_fees'] = processed['volume_24h'] * (processed['fee_tier'] / 1000000)
                processed['apr_estimate'] = (processed['daily_fees'] * 365 / processed['tvl_usd']) * 100
            else:
                processed['volume_tvl_ratio'] = 0
                processed['daily_fees'] = 0
                processed['apr_estimate'] = 0
            
            return processed
            
        except Exception as e:
            logger.error("pool_data_preprocessing_failed", error=str(e))
            return None
    
    def _meets_basic_criteria(self, pool_data: Dict[str, Any]) -> bool:
        """检查是否满足基础筛选条件"""
        return (
            pool_data['tvl_usd'] >= self.thresholds['min_tvl'] and
            pool_data['volume_24h'] >= self.thresholds['min_volume_24h'] and
            pool_data['token0'] and pool_data['token1'] and
            pool_data['fee_tier'] > 0
        )
    
    def _calculate_factor_scores(self, pool_data: Dict[str, Any]) -> Dict[str, float]:
        """计算各项因子评分"""
        scores = {}
        
        # 1. 手续费收益评分 (基于APR)
        apr = pool_data['apr_estimate']
        if apr <= 0:
            scores['fee_score'] = 0
        elif apr <= 5:
            scores['fee_score'] = apr * 10  # 0-50分
        elif apr <= 20:
            scores['fee_score'] = 50 + (apr - 5) * 2.5  # 50-87.5分
        else:
            scores['fee_score'] = min(100, 87.5 + (apr - 20) * 0.625)  # 87.5-100分
        
        # 2. 交易量评分
        volume = pool_data['volume_24h']
        if volume <= 10000:
            scores['volume_score'] = volume / 10000 * 30  # 0-30分
        elif volume <= 100000:
            scores['volume_score'] = 30 + (volume - 10000) / 90000 * 40  # 30-70分
        else:
            scores['volume_score'] = 70 + min(30, (volume - 100000) / 1000000 * 30)  # 70-100分
        
        # 3. TVL评分
        tvl = pool_data['tvl_usd']
        if tvl <= 50000:
            scores['tvl_score'] = tvl / 50000 * 40  # 0-40分
        elif tvl <= 1000000:
            scores['tvl_score'] = 40 + (tvl - 50000) / 950000 * 40  # 40-80分
        else:
            scores['tvl_score'] = 80 + min(20, (tvl - 1000000) / 10000000 * 20)  # 80-100分
        
        # 4. 流动性评分 (基于Volume/TVL比率)
        ratio = pool_data['volume_tvl_ratio']
        if ratio <= 0.1:
            scores['liquidity_score'] = ratio * 300  # 0-30分
        elif ratio <= 0.5:
            scores['liquidity_score'] = 30 + (ratio - 0.1) * 125  # 30-80分
        else:
            scores['liquidity_score'] = 80 + min(20, (ratio - 0.5) * 40)  # 80-100分
        
        # 确保所有评分在0-100范围内
        for key in scores:
            scores[key] = max(0, min(100, scores[key]))
        
        return scores

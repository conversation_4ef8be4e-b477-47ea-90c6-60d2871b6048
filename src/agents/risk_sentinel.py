"""
Risk Sentinel Agent - Dy-Flow v3
風險監控系統，1分鐘週期價格監控
使用 ATR 和 kDrop 公式進行風控計算
"""

import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import structlog

from .base_agent import BaseAgent
from ..utils.models_v3 import AgentResult
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class RiskSentinelAgent(BaseAgent):
    """Risk Sentinel Agent - v3 風險監控系統"""
    
    def __init__(self, name: str, config, database):
        super().__init__(name, config, database)
        
        # v3 風控參數
        risk_config = getattr(config, 'risk', {}) if hasattr(config, 'risk') else {}
        self.k_drop = risk_config.get('k_drop', 2.5)
        self.min_drop_pct = risk_config.get('min_drop_pct', 0.06)  # 6%
        self.tvl_drop_threshold = risk_config.get('tvl_drop_threshold', 0.40)  # 40%
        self.tvl_check_window = risk_config.get('tvl_check_window', 30)  # 30分鐘
        
        # 對沖失效參數
        self.hedge_fail_limit = risk_config.get('hedge_fail_limit', 3)
        self.delta_threshold = risk_config.get('delta_threshold', 0.30)  # 30%
        
        # 價格歷史存儲
        self.price_history: Dict[str, List[Tuple[datetime, float]]] = {}
        self.atr_cache: Dict[str, float] = {}
        self.last_tvl_check: Dict[str, float] = {}
        
        # 監控代幣列表
        self.monitored_tokens = [
            'WBNB', 'WETH', 'BTCB', 'USDT', 'USDC', 'BUSD',
            'SOL', 'WSOL', 'BTC', 'ETH', 'BNB'
        ]
        
        # 價格數據源
        self.price_sources = {
            'coingecko': 'https://api.coingecko.com/api/v3/simple/price',
            'binance': 'https://api.binance.com/api/v3/ticker/price'
        }
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        logger.info("risk_sentinel_initialized",
                   k_drop=self.k_drop,
                   min_drop_pct=self.min_drop_pct,
                   monitored_tokens=len(self.monitored_tokens))
        self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行風險監控"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("risk_sentinel_execution_started")
            
            # 1. 獲取當前價格
            current_prices = await self._fetch_current_prices()
            
            # 2. 更新價格歷史
            self._update_price_history(current_prices)
            
            # 3. 計算 ATR 指標
            atr_results = self._calculate_atr_for_all_tokens()
            
            # 4. 檢測價格風險
            price_risks = self._detect_price_risks(current_prices, atr_results)
            
            # 5. 檢測 TVL 風險（模擬）
            tvl_risks = await self._detect_tvl_risks()
            
            # 6. 檢測對沖失效風險（模擬）
            hedge_risks = await self._detect_hedge_risks()
            
            # 7. 生成風險警報
            all_risks = price_risks + tvl_risks + hedge_risks
            risk_level = self._determine_overall_risk_level(all_risks)
            
            logger.info("risk_sentinel_execution_completed",
                       tokens_monitored=len(current_prices),
                       total_risks=len(all_risks),
                       risk_level=risk_level)
            
            return AgentResult(
                agent_name=self.name,
                data={
                    "price_risks": price_risks,
                    "tvl_risks": tvl_risks,
                    "hedge_risks": hedge_risks,
                    "overall_risk_level": risk_level,
                    "atr_results": atr_results
                },
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "tokens_monitored": len(current_prices),
                    "total_risks": len(all_risks),
                    "risk_level": risk_level
                }
            )
            
        except Exception as e:
            logger.error("risk_sentinel_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data={},
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )
    
    async def _fetch_current_prices(self) -> Dict[str, float]:
        """獲取當前價格"""
        try:
            # 優先使用 CoinGecko
            try:
                prices = await self._fetch_prices_from_coingecko()
                if prices:
                    return prices
            except Exception as e:
                logger.warning("coingecko_price_fetch_failed", error=str(e))
            
            # 備用：Binance API
            try:
                prices = await self._fetch_prices_from_binance()
                if prices:
                    return prices
            except Exception as e:
                logger.warning("binance_price_fetch_failed", error=str(e))
            
            # 使用緩存價格
            return self._get_cached_prices()
            
        except Exception as e:
            logger.error("price_fetch_failed", error=str(e))
            return {}
    
    async def _fetch_prices_from_coingecko(self) -> Dict[str, float]:
        """從 CoinGecko 獲取價格"""
        token_ids = {
            'WBNB': 'binancecoin', 'BNB': 'binancecoin',
            'WETH': 'ethereum', 'ETH': 'ethereum',
            'BTCB': 'bitcoin', 'BTC': 'bitcoin',
            'USDT': 'tether', 'USDC': 'usd-coin', 'BUSD': 'binance-usd',
            'SOL': 'solana', 'WSOL': 'solana'
        }
        
        ids = ','.join(set(token_ids.values()))
        url = f"{self.price_sources['coingecko']}?ids={ids}&vs_currencies=usd"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    prices = {}
                    for token, coin_id in token_ids.items():
                        if coin_id in data and 'usd' in data[coin_id]:
                            prices[token] = float(data[coin_id]['usd'])
                    return prices
                else:
                    raise DyFlowException(f"CoinGecko API 錯誤: {response.status}")
    
    async def _fetch_prices_from_binance(self) -> Dict[str, float]:
        """從 Binance 獲取價格"""
        symbol_mapping = {
            'WBNB': 'BNBUSDT', 'BNB': 'BNBUSDT',
            'WETH': 'ETHUSDT', 'ETH': 'ETHUSDT',
            'BTCB': 'BTCUSDT', 'BTC': 'BTCUSDT',
            'SOL': 'SOLUSDT'
        }
        
        url = "https://api.binance.com/api/v3/ticker/price"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    symbol_prices = {item['symbol']: float(item['price']) for item in data}
                    
                    prices = {}
                    for token, symbol in symbol_mapping.items():
                        if symbol in symbol_prices:
                            prices[token] = symbol_prices[symbol]
                    
                    # 穩定幣價格
                    prices.update({'USDT': 1.0, 'USDC': 1.0, 'BUSD': 1.0})
                    return prices
                else:
                    raise DyFlowException(f"Binance API 錯誤: {response.status}")
    
    def _get_cached_prices(self) -> Dict[str, float]:
        """獲取緩存價格"""
        prices = {}
        for token in self.monitored_tokens:
            if token in self.price_history and self.price_history[token]:
                prices[token] = self.price_history[token][-1][1]
        return prices
    
    def _update_price_history(self, current_prices: Dict[str, float]) -> None:
        """更新價格歷史"""
        current_time = get_utc_timestamp()
        
        for token, price in current_prices.items():
            if token not in self.price_history:
                self.price_history[token] = []
            
            self.price_history[token].append((current_time, price))
            
            # 保持最近24小時數據 (1440個點)
            if len(self.price_history[token]) > 1440:
                self.price_history[token] = self.price_history[token][-1440:]
    
    def _calculate_atr_for_all_tokens(self) -> Dict[str, float]:
        """計算所有代幣的 ATR"""
        atr_results = {}
        
        for token in self.monitored_tokens:
            if token in self.price_history and len(self.price_history[token]) >= 14:
                atr = self._calculate_atr(token, period=14)
                if atr > 0:
                    atr_results[token] = atr
                    self.atr_cache[token] = atr
        
        return atr_results
    
    def _calculate_atr(self, token: str, period: int = 14) -> float:
        """計算 ATR (Average True Range)"""
        try:
            if token not in self.price_history or len(self.price_history[token]) < period + 1:
                return 0.0
            
            prices = self.price_history[token][-period-1:]
            
            true_ranges = []
            for i in range(1, len(prices)):
                high = prices[i][1]
                low = prices[i][1]  # 對於價格序列，high/low 相同
                prev_close = prices[i-1][1]
                
                # True Range = max(high-low, |high-prev_close|, |low-prev_close|)
                tr = max(
                    abs(high - low),
                    abs(high - prev_close),
                    abs(low - prev_close)
                )
                true_ranges.append(tr)
            
            # ATR = 平均 True Range
            if true_ranges:
                return sum(true_ranges) / len(true_ranges)
            
            return 0.0
            
        except Exception as e:
            logger.error("atr_calculation_failed", token=token, error=str(e))
            return 0.0
    
    def _detect_price_risks(self, current_prices: Dict[str, float], 
                          atr_results: Dict[str, float]) -> List[Dict[str, Any]]:
        """檢測價格風險 - 使用 v3 kDrop 公式"""
        risks = []
        
        for token, current_price in current_prices.items():
            if token not in self.price_history or len(self.price_history[token]) < 2:
                continue
            
            # 獲取前一價格
            prev_price = self.price_history[token][-2][1]
            
            if prev_price <= 0:
                continue
            
            # 計算價格變化百分比
            price_change = (current_price - prev_price) / prev_price
            
            # 使用 ATR 計算動態閾值
            if token in atr_results:
                atr = atr_results[token]
                # kDrop 公式：threshold = max(k_drop * ATR / price, min_drop_pct)
                threshold = max(self.k_drop * atr / current_price, self.min_drop_pct)
            else:
                # 沒有 ATR 時使用最小閾值
                threshold = self.min_drop_pct
            
            # 檢查是否觸發風險
            if price_change <= -threshold:
                risks.append({
                    "type": "price_drop_risk",
                    "token": token,
                    "current_price": current_price,
                    "prev_price": prev_price,
                    "change_pct": price_change,
                    "threshold": threshold,
                    "atr": atr_results.get(token, 0),
                    "severity": "critical" if price_change <= -threshold * 1.5 else "high",
                    "timestamp": get_utc_timestamp()
                })
        
        return risks
    
    async def _detect_tvl_risks(self) -> List[Dict[str, Any]]:
        """檢測 TVL 風險"""
        risks = []
        
        # 這裡應該從數據庫獲取實際的 TVL 數據
        # 模擬 TVL 檢查
        mock_pools = ['pool_1', 'pool_2', 'pool_3']
        
        for pool_id in mock_pools:
            # 模擬 TVL 數據
            current_tvl = 1000000  # $1M
            prev_tvl = self.last_tvl_check.get(pool_id, current_tvl)
            
            if prev_tvl > 0:
                tvl_change = (current_tvl - prev_tvl) / prev_tvl
                
                # 檢查 30分鐘內 TVL 跌幅是否超過 40%
                if tvl_change <= -self.tvl_drop_threshold:
                    risks.append({
                        "type": "tvl_drop_risk",
                        "pool_id": pool_id,
                        "current_tvl": current_tvl,
                        "prev_tvl": prev_tvl,
                        "change_pct": tvl_change,
                        "threshold": self.tvl_drop_threshold,
                        "severity": "critical",
                        "timestamp": get_utc_timestamp()
                    })
            
            self.last_tvl_check[pool_id] = current_tvl
        
        return risks
    
    async def _detect_hedge_risks(self) -> List[Dict[str, Any]]:
        """檢測對沖失效風險"""
        risks = []
        
        # 這裡應該檢查實際的對沖狀態
        # 模擬對沖檢查
        mock_hedges = ['hedge_1', 'hedge_2']
        
        for hedge_id in mock_hedges:
            # 模擬對沖失敗次數和 delta
            fail_count = 2  # 模擬值
            current_delta = 0.25  # 25%
            
            # 檢查失敗次數是否超過限制
            if fail_count >= self.hedge_fail_limit:
                risks.append({
                    "type": "hedge_failure_risk",
                    "hedge_id": hedge_id,
                    "fail_count": fail_count,
                    "fail_limit": self.hedge_fail_limit,
                    "severity": "high",
                    "timestamp": get_utc_timestamp()
                })
            
            # 檢查 delta 是否超過閾值
            if current_delta >= self.delta_threshold:
                risks.append({
                    "type": "hedge_delta_risk",
                    "hedge_id": hedge_id,
                    "current_delta": current_delta,
                    "delta_threshold": self.delta_threshold,
                    "severity": "medium",
                    "timestamp": get_utc_timestamp()
                })
        
        return risks
    
    def _determine_overall_risk_level(self, all_risks: List[Dict[str, Any]]) -> str:
        """確定整體風險等級"""
        if not all_risks:
            return "low"
        
        # 統計不同嚴重程度的風險
        critical_count = sum(1 for r in all_risks if r.get('severity') == 'critical')
        high_count = sum(1 for r in all_risks if r.get('severity') == 'high')
        
        if critical_count > 0:
            return "critical"
        elif high_count >= 2:
            return "high"
        elif high_count > 0:
            return "medium"
        else:
            return "low"
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            # 清理緩存，但保留少量歷史數據
            for token in list(self.price_history.keys()):
                if len(self.price_history[token]) > 100:
                    self.price_history[token] = self.price_history[token][-100:]
            
            logger.info("risk_sentinel_cleanup_completed")
        except Exception as e:
            logger.error("risk_sentinel_cleanup_failed", error=str(e))
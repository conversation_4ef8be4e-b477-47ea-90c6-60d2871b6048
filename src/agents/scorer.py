"""
Scorer Agent - Dy-Flow v3
池子因子打分系統，輸入 PoolRaw 輸出 PoolScore
規則決策優先，LLM 僅在標記 needs_llm 時使用
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog

from .base_agent import BaseAgent
from ..utils.models_v3 import PoolRaw, PoolScore, AgentResult
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class ScorerAgent(BaseAgent):
    """Scorer Agent - v3 因子打分系統"""
    
    def __init__(self, name: str, config, database):
        super().__init__(name, config, database)
        
        # Agent-specific config from agno_flow.yaml or default.yaml (via BaseAgent)
        agent_specific_config = self.agent_config or {}

        # Scoring weights from main Config object (decision_weights) or agent_specific_config
        default_weights = {
            'fee_tvl': 0.40,
            'volume_score': 0.30,
            'tvl_score': 0.20,
            'token_quality': 0.10
        }
        # self.config.decision_weights is loaded from strategies.yaml
        self.weights = agent_specific_config.get('weights', getattr(self.config, 'decision_weights', default_weights))

        # Scoring thresholds from main Config object (strategy) or agent_specific_config
        strategy_config = self.config.strategy
        default_thresholds = {
            'min_fee_tvl': 5.0,
            'min_volume_24h': strategy_config.min_volume_24h,
            'min_tvl': strategy_config.min_tvl,
            'stable_tokens': ['USDT', 'USDC', 'BUSD', 'DAI'], # These could also come from strategy_config.preferred_tokens
            'blue_chip_tokens': ['WBNB', 'WETH', 'BTCB', 'SOL', 'WSOL'] # Or strategy_config.preferred_tokens
        }
        
        # Allow agent_specific_config to override individual threshold values
        self.thresholds = default_thresholds
        if 'thresholds' in agent_specific_config and isinstance(agent_specific_config['thresholds'], dict):
            self.thresholds.update(agent_specific_config['thresholds'])
        
        # Ensure specific thresholds from strategy_config are used if not overridden
        self.thresholds['min_volume_24h'] = agent_specific_config.get('min_volume_24h_threshold', self.thresholds['min_volume_24h'])
        self.thresholds['min_tvl'] = agent_specific_config.get('min_tvl_threshold', self.thresholds['min_tvl'])
        self.thresholds['min_fee_tvl'] = agent_specific_config.get('min_fee_tvl_threshold', self.thresholds['min_fee_tvl'])
        
        # Update stable and blue chip tokens from strategy_config if not specifically set in agent_config
        preferred_tokens = set(strategy_config.preferred_tokens)
        if not agent_specific_config.get('thresholds', {}).get('stable_tokens'):
             self.thresholds['stable_tokens'] = [t for t in ['USDT', 'USDC', 'BUSD', 'DAI'] if t in preferred_tokens] or ['USDT', 'USDC'] # Fallback
        if not agent_specific_config.get('thresholds', {}).get('blue_chip_tokens'):
            self.thresholds['blue_chip_tokens'] = [t for t in ['WBNB', 'WETH', 'BTCB', 'SOL', 'WSOL', 'BTC', 'ETH', 'BNB'] if t in preferred_tokens] or ['WBNB', 'WETH', 'BTCB', 'SOL'] # Fallback
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        logger.info("scorer_initialized",
                   weights=self.weights,
                   thresholds=self.thresholds)
        self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行池子評分"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("scorer_execution_started")
            
            # 獲取 PoolRaw 數據（通常從數據庫或其他 Agent 獲取）
            pool_raws = await self._get_pool_raws()
            
            if not pool_raws:
                logger.info("no_pools_to_score")
                return AgentResult(
                    agent_name=self.name,
                    data=[],
                    timestamp=get_utc_timestamp(),
                    status="success",
                    metadata={"message": "沒有需要評分的池子"}
                )
            
            # 批量評分
            pool_scores = []
            for pool_raw in pool_raws:
                try:
                    pool_score = self._score_pool(pool_raw)
                    if pool_score:
                        pool_scores.append(pool_score)
                except Exception as e:
                    logger.error("pool_scoring_failed", 
                               pool_id=pool_raw.id, 
                               error=str(e))
            
            logger.info("scorer_execution_completed", 
                       pools_processed=len(pool_raws),
                       pools_scored=len(pool_scores))
            
            return AgentResult(
                agent_name=self.name,
                data=pool_scores,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "pools_processed": len(pool_raws),
                    "pools_scored": len(pool_scores),
                    "avg_score": sum(p.score for p in pool_scores) / len(pool_scores) if pool_scores else 0
                }
            )
            
        except Exception as e:
            logger.error("scorer_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )
    
    async def _get_pool_raws(self) -> List[PoolRaw]:
        """獲取需要評分的 PoolRaw 數據"""
        # 實際實現中應該從數據庫查詢或接收來自 Scout 的數據
        # 這裡使用模擬數據
        return []
    
    def _score_pool(self, pool_raw: PoolRaw) -> Optional[PoolScore]:
        """對單個池子進行評分"""
        try:
            # 1. 基本過濾
            if not self._meets_basic_criteria(pool_raw):
                return None
            
            # 2. 計算各因子評分
            fee_score = self._calculate_fee_score(pool_raw)
            volume_score = self._calculate_volume_score(pool_raw)
            tvl_score = self._calculate_tvl_score(pool_raw)
            token_score = self._calculate_token_quality_score(pool_raw)
            
            # 3. 計算綜合評分
            final_score = (
                fee_score * self.weights['fee_tvl'] +
                volume_score * self.weights['volume_score'] +
                tvl_score * self.weights['tvl_score'] +
                token_score * self.weights['token_quality']
            )
            
            # 4. 判斷是否可對沖
            hedgeable = self._is_hedgeable(pool_raw)
            
            return PoolScore(
                id=pool_raw.id,
                score=round(final_score, 2),
                hedgeable=hedgeable
            )
            
        except Exception as e:
            logger.error("pool_scoring_error", 
                        pool_id=pool_raw.id, 
                        error=str(e))
            return None
    
    def _meets_basic_criteria(self, pool: PoolRaw) -> bool:
        """檢查基本篩選條件"""
        # 由於 PoolRaw 標準格式沒有 volume24h 和 fee_rate，我們使用現有字段
        estimated_volume = pool.fee24h / 0.0025 if pool.fee24h > 0 else 0  # 假設 0.25% 費率
        return (
            pool.tvl_usd >= self.thresholds['min_tvl'] and
            estimated_volume >= self.thresholds['min_volume_24h'] and
            pool.fee_tvl >= self.thresholds['min_fee_tvl']
        )
    
    def _calculate_fee_score(self, pool: PoolRaw) -> float:
        """計算費率評分 (0-100)"""
        # 基於年化費率的分數
        fee_tvl = pool.fee_tvl
        
        if fee_tvl >= 50:  # 50%+ 極高
            return 100.0
        elif fee_tvl >= 30:  # 30-50% 很高
            return 80.0 + (fee_tvl - 30) * 1.0  # 80-100
        elif fee_tvl >= 15:  # 15-30% 高
            return 60.0 + (fee_tvl - 15) * (20.0 / 15)  # 60-80
        elif fee_tvl >= 5:   # 5-15% 中等
            return 20.0 + (fee_tvl - 5) * (40.0 / 10)   # 20-60
        else:  # <5% 低
            return fee_tvl * 4.0  # 0-20
    
    def _calculate_volume_score(self, pool: PoolRaw) -> float:
        """計算交易量評分 (0-100)"""
        # 通過 fee24h 估算交易量
        volume = pool.fee24h / 0.0025 if pool.fee24h > 0 else 0  # 假設 0.25% 費率
        
        if volume >= 1000000:  # $1M+
            return 100.0
        elif volume >= 500000:  # $500k-1M
            return 80.0 + (volume - 500000) * (20.0 / 500000)
        elif volume >= 100000:  # $100k-500k
            return 60.0 + (volume - 100000) * (20.0 / 400000)
        elif volume >= 50000:   # $50k-100k
            return 40.0 + (volume - 50000) * (20.0 / 50000)
        elif volume >= 10000:   # $10k-50k
            return 20.0 + (volume - 10000) * (20.0 / 40000)
        else:  # <$10k
            return volume * (20.0 / 10000)
    
    def _calculate_tvl_score(self, pool: PoolRaw) -> float:
        """計算 TVL 評分 (0-100)"""
        tvl = pool.tvl_usd
        
        if tvl >= 10000000:  # $10M+
            return 100.0
        elif tvl >= 5000000:  # $5M-10M
            return 80.0 + (tvl - 5000000) * (20.0 / 5000000)
        elif tvl >= 1000000:  # $1M-5M
            return 60.0 + (tvl - 1000000) * (20.0 / 4000000)
        elif tvl >= 500000:   # $500k-1M
            return 40.0 + (tvl - 500000) * (20.0 / 500000)
        elif tvl >= 50000:    # $50k-500k
            return 20.0 + (tvl - 50000) * (20.0 / 450000)
        else:  # <$50k
            return tvl * (20.0 / 50000)
    
    def _calculate_token_quality_score(self, pool: PoolRaw) -> float:
        """計算代幣品質評分 (0-100)"""
        # 由於 PoolRaw 標準格式沒有 token0/token1 字段，
        # 暫時給一個中等分數，實際應該從池子地址獲取代幣信息
        # TODO: 實現從池子地址獲取代幣信息的邏輯
        return 50.0  # 默認中等分數
    
    def _is_hedgeable(self, pool: PoolRaw) -> bool:
        """判斷池子是否可對沖"""
        # 由於 PoolRaw 標準格式沒有 token0/token1 字段，
        # 暫時假設大 TVL 的池子是可對沖的
        # TODO: 實現從池子地址獲取代幣信息並判斷是否可對沖
        return pool.tvl_usd >= 1000000  # TVL > $1M 假設可對沖
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("scorer_cleanup_completed")
        except Exception as e:
            logger.error("scorer_cleanup_failed", error=str(e))
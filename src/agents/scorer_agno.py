"""
Scorer Agent - 基於 Agno Framework
對池進行因子打分和排名
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Literal
from dataclasses import dataclass, asdict
import structlog

from .base_agent import BaseAgent
from ..utils.config import Config
from ..utils.database import Database
from .scout_bsc_agno import PoolRaw

# Agno Framework 導入
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

logger = structlog.get_logger(__name__)


@dataclass
class PoolScore:
    """池評分數據 (Dy-Flow v3 標準)"""
    id: str
    score: float
    hedgeable: bool
    factors: Dict[str, float]
    rank: int
    timestamp: datetime


class ScorerAgent(BaseAgent):
    """Scorer Agent - 池評分和排名"""
    
    def __init__(self, config: Config, database: Database):
        super().__init__("scorer", config, database)
        
        # 評分因子權重
        self.factor_weights = self.agent_config.get('factor_weights', {
            'tvl_score': 0.3,
            'fee_score': 0.25,
            'volume_score': 0.2,
            'liquidity_score': 0.15,
            'risk_score': 0.1
        })
        
        # 閾值設定
        self.min_score = self.agent_config.get('min_score', 60.0)
        self.max_pools_ranked = self.agent_config.get('max_pools_ranked', 20)
        
        # Agno Agent
        self.agno_agent = None
        
    async def initialize(self) -> None:
        """初始化 Scorer Agent"""
        try:
            logger.info("scorer_initializing")
            
            # 初始化 Agno Agent（如果可用）
            if AGNO_AVAILABLE and self.agent_config.get('openai_api_key'):
                await self._initialize_agno_agent()
            
            self.is_initialized = True
            logger.info("scorer_initialized", agno_enabled=self.agno_agent is not None)
            
        except Exception as e:
            logger.error("scorer_initialization_failed", error=str(e))
            raise
    
    async def _initialize_agno_agent(self) -> None:
        """初始化 Agno Agent"""
        try:
            self.agno_agent = Agent(
                name="DyFlow Pool Scorer",
                description="池評分和排名專家，基於多因子模型",
                model=OpenAIChat(
                    id="gpt-4o",
                    api_key=self.agent_config.get('openai_api_key')
                ),
                tools=[
                    self._agno_calculate_scores,
                    self._agno_analyze_factors,
                    self._agno_rank_pools
                ],
                instructions=[
                    "你是池評分專家",
                    "基於 TVL、手續費、流動性等因子評分",
                    "識別最佳投資機會",
                    "提供評分解釋和建議"
                ],
                show_tool_calls=True,
                markdown=True
            )
            
        except Exception as e:
            logger.warning("agno_scorer_init_failed", error=str(e))
            self.agno_agent = None
    
    async def execute(self) -> Dict[str, Any]:
        """執行池評分"""
        try:
            logger.info("scorer_execution_started")
            
            # 獲取原始池數據
            pools_raw = await self._load_pools_raw()
            
            # 計算評分
            pools_scored = await self._score_pools(pools_raw)
            
            # 排名
            pools_ranked = self._rank_pools(pools_scored)
            
            # 保存結果
            saved_count = await self._save_scores(pools_ranked)
            
            # 返回結果
            result = {
                'success': True,
                'pools_processed': len(pools_raw),
                'pools_scored': len(pools_scored),
                'pools_ranked': len(pools_ranked),
                'pools_saved': saved_count,
                'timestamp': datetime.utcnow().isoformat(),
                'data': [asdict(pool) for pool in pools_ranked],
                'items_processed': len(pools_ranked)
            }
            
            logger.info("scorer_execution_completed", 
                       pools_processed=len(pools_raw),
                       pools_ranked=len(pools_ranked))
            
            return result
            
        except Exception as e:
            logger.error("scorer_execution_failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat(),
                'items_processed': 0
            }
    
    async def _load_pools_raw(self) -> List[PoolRaw]:
        """加載原始池數據"""
        pools_raw = []
        
        try:
            # 從數據庫加載最新的池數據
            # 這裡簡化為模擬數據
            bsc_pools = await self._load_from_database('pools_raw_bsc')
            sol_pools = await self._load_from_database('pools_raw_sol')
            
            # 轉換為 PoolRaw 對象
            for pool_data in bsc_pools + sol_pools:
                try:
                    pool = PoolRaw(
                        id=pool_data['id'],
                        chain=pool_data['chain'],
                        protocol=pool_data['protocol'],
                        name=pool_data['name'],
                        tvl_usd=float(pool_data['tvl_usd']),
                        fee24h=float(pool_data['fee24h']),
                        fee_tvl=float(pool_data['fee_tvl']),
                        created_at=datetime.fromisoformat(pool_data['created_at'].replace('Z', '+00:00'))
                    )
                    pools_raw.append(pool)
                except Exception as e:
                    logger.warning("pool_conversion_failed", pool_id=pool_data.get('id'), error=str(e))
            
        except Exception as e:
            logger.error("load_pools_raw_failed", error=str(e))
            
        return pools_raw
    
    async def _load_from_database(self, table: str) -> List[Dict[str, Any]]:
        """從數據庫加載數據（模擬）"""
        # 這裡應該實際查詢數據庫
        # 暫時返回空列表
        return []
    
    async def _score_pools(self, pools_raw: List[PoolRaw]) -> List[PoolScore]:
        """計算池評分"""
        pools_scored = []
        
        for pool in pools_raw:
            try:
                # 計算各項因子分數
                factors = self._calculate_factors(pool)
                
                # 計算綜合評分
                score = self._calculate_weighted_score(factors)
                
                # 判斷是否可對沖
                hedgeable = self._is_hedgeable(pool)
                
                pool_score = PoolScore(
                    id=pool.id,
                    score=score,
                    hedgeable=hedgeable,
                    factors=factors,
                    rank=0,  # 稍後排名
                    timestamp=datetime.utcnow()
                )
                
                # 只保留達到最低分數的池
                if score >= self.min_score:
                    pools_scored.append(pool_score)
                    
            except Exception as e:
                logger.warning("pool_scoring_failed", pool_id=pool.id, error=str(e))
        
        return pools_scored
    
    def _calculate_factors(self, pool: PoolRaw) -> Dict[str, float]:
        """計算評分因子"""
        factors = {}
        
        # TVL 分數 (0-100)
        factors['tvl_score'] = min(100, (pool.tvl_usd / 1000000) * 50)  # 200萬 TVL = 100分
        
        # 手續費分數 (0-100)
        if pool.tvl_usd > 0:
            fee_ratio = pool.fee24h / pool.tvl_usd
            factors['fee_score'] = min(100, fee_ratio * 1000 * 100)  # 0.1% 日手續費率 = 100分
        else:
            factors['fee_score'] = 0
        
        # 交易量分數 (0-100)
        factors['volume_score'] = min(100, (pool.fee24h / 100000) * 50)  # 20萬日交易量 = 100分
        
        # 流動性分數 (0-100)
        factors['liquidity_score'] = min(100, pool.fee_tvl * 1000)  # 0.1% 手續費率 = 100分
        
        # 風險分數 (0-100, 100 = 低風險)
        risk_score = 100
        if pool.chain == 'SOL':
            risk_score -= 10  # Solana 網絡風險稍高
        if pool.tvl_usd < 500000:
            risk_score -= 20  # 低 TVL 風險
        factors['risk_score'] = max(0, risk_score)
        
        return factors
    
    def _calculate_weighted_score(self, factors: Dict[str, float]) -> float:
        """計算加權綜合評分"""
        weighted_score = 0
        
        for factor, score in factors.items():
            weight = self.factor_weights.get(factor, 0)
            weighted_score += score * weight
        
        return round(weighted_score, 2)
    
    def _is_hedgeable(self, pool: PoolRaw) -> bool:
        """判斷池是否可對沖"""
        # 簡單的對沖能力判斷
        major_tokens = ['BTC', 'ETH', 'BNB', 'SOL', 'USDT', 'USDC', 'BUSD']
        
        for token in major_tokens:
            if token in pool.name.upper():
                return True
        
        return False
    
    def _rank_pools(self, pools_scored: List[PoolScore]) -> List[PoolScore]:
        """對池進行排名"""
        # 按評分降序排序
        pools_sorted = sorted(pools_scored, key=lambda x: x.score, reverse=True)
        
        # 設定排名
        for i, pool in enumerate(pools_sorted[:self.max_pools_ranked], 1):
            pool.rank = i
        
        return pools_sorted[:self.max_pools_ranked]
    
    async def _save_scores(self, pools_scored: List[PoolScore]) -> int:
        """保存評分結果"""
        saved_count = 0
        
        for pool in pools_scored:
            try:
                # 轉換為字典格式
                pool_dict = asdict(pool)
                
                # 保存到數據庫
                await self.save_to_database('pools_scored', pool_dict)
                saved_count += 1
                
            except Exception as e:
                logger.warning("save_score_failed", pool_id=pool.id, error=str(e))
        
        return saved_count
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("scorer_cleanup")
        except Exception as e:
            logger.warning("scorer_cleanup_failed", error=str(e))
    
    # Agno Agent 工具函數
    def _agno_calculate_scores(self, pools_count: int) -> str:
        """Agno 工具：計算評分"""
        try:
            return f"已對 {pools_count} 個池進行多因子評分分析"
        except Exception as e:
            return f"評分計算失敗: {str(e)}"
    
    def _agno_analyze_factors(self, factor_data: str) -> str:
        """Agno 工具：分析因子"""
        return "因子分析：TVL、手續費、流動性、風險因子權重均衡"
    
    def _agno_rank_pools(self, top_n: int = 10) -> str:
        """Agno 工具：池排名"""
        return f"已完成 Top {top_n} 池排名，基於綜合評分"


# 工廠函數
def create_scorer_agent(config: Config, database: Database) -> ScorerAgent:
    """創建 Scorer Agent"""
    return ScorerAgent(config, database)
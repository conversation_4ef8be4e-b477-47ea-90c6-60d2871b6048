"""
Scorer Agent v2 - Dy-Flow v3 Enhanced
增强版池子因子打分系統，包含動態權重和新評分因子
基於 v1 版本改進，增加流動性深度、代幣品質分析等新功能
"""

import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import json
import math

from .base_agent import BaseAgent
from ..utils.models_v3 import PoolRaw, PoolScore, AgentResult
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


@dataclass
class PoolDetailedInfo:
    """擴展的池子詳細信息"""
    token0_symbol: str
    token1_symbol: str
    token0_address: str
    token1_address: str
    fee_rate: float
    liquidity_depth: float
    price_volatility: float
    volume_24h: float
    historical_apy: Optional[float] = None


@dataclass
class DynamicWeights:
    """動態權重配置"""
    fee_tvl: float
    volume_score: float
    tvl_score: float
    token_quality: float
    liquidity_depth: float
    volatility_score: float
    
    def normalize(self) -> 'DynamicWeights':
        """正規化權重，確保總和為1"""
        total = sum([self.fee_tvl, self.volume_score, self.tvl_score, 
                    self.token_quality, self.liquidity_depth, self.volatility_score])
        if total == 0:
            return self
        factor = 1.0 / total
        return DynamicWeights(
            fee_tvl=self.fee_tvl * factor,
            volume_score=self.volume_score * factor,
            tvl_score=self.tvl_score * factor,
            token_quality=self.token_quality * factor,
            liquidity_depth=self.liquidity_depth * factor,
            volatility_score=self.volatility_score * factor
        )


class ScorerV2Agent(BaseAgent):
    """增強版 Scorer Agent - v2 動態評分系統"""
    
    def __init__(self, name: str, config, database):
        super().__init__(name, config, database)
        
        # Agent-specific config
        agent_specific_config = self.agent_config or {}
        
        # 基礎權重配置（將根據市場情況動態調整）
        default_base_weights = {
            'fee_tvl': 0.30,
            'volume_score': 0.25,
            'tvl_score': 0.15,
            'token_quality': 0.15,
            'liquidity_depth': 0.10,
            'volatility_score': 0.05
        }
        self.base_weights = agent_specific_config.get('weights', default_base_weights)
        
        # 增強的閾值配置
        strategy_config = self.config.strategy
        default_thresholds = {
            'min_fee_tvl': 3.0,  # 降低至3%以擴大候選池
            'min_volume_24h': strategy_config.min_volume_24h,
            'min_tvl': strategy_config.min_tvl,
            'min_liquidity_depth': 50000,  # 最小流動性深度 $50k
            'max_volatility': 0.05,  # 最大日波動率 5%
            'stable_tokens': ['USDT', 'USDC', 'BUSD', 'DAI', 'FRAX'],
            'blue_chip_tokens': ['WBNB', 'WETH', 'BTCB', 'SOL', 'WSOL', 'BTC', 'ETH', 'BNB'],
            'high_risk_tokens': []  # 高風險代幣黑名單，需要實時更新
        }
        
        self.thresholds = default_thresholds
        if 'thresholds' in agent_specific_config:
            self.thresholds.update(agent_specific_config['thresholds'])
        
        # 動態權重調整參數
        self.volatility_adjustment = agent_specific_config.get('volatility_adjustment', True)
        self.market_condition_adjustment = agent_specific_config.get('market_condition_adjustment', True)
        
        # 歷史數據緩存（實際實現中應該使用 Redis 等外部緩存）
        self.historical_data_cache = {}
        
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        logger.info("scorer_v2_initialized",
                   base_weights=self.base_weights,
                   thresholds=self.thresholds,
                   features=["dynamic_weights", "liquidity_depth", "token_analysis"])
        self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行增強版池子評分"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("scorer_v2_execution_started")
            
            # 1. 獲取市場數據和池子信息
            pool_raws = self._get_pool_raws()
            market_condition = await self._analyze_market_condition()
            
            if not pool_raws:
                logger.info("no_pools_to_score")
                return AgentResult(
                    agent_name=self.name,
                    data=[],
                    timestamp=get_utc_timestamp(),
                    status="success",
                    metadata={"message": "沒有需要評分的池子"}
                )
            
            # 2. 根據市場情況調整權重
            dynamic_weights = self._calculate_dynamic_weights(market_condition)
            
            # 3. 批量評分
            pool_scores = []
            detailed_analysis = []
            
            for pool_raw in pool_raws:
                try:
                    # 獲取池子詳細信息
                    pool_detail = await self._get_pool_detailed_info(pool_raw)
                    if not pool_detail:
                        continue
                    
                    # 執行增強評分
                    pool_score, analysis = self._score_pool_enhanced(pool_raw, pool_detail, dynamic_weights)
                    if pool_score:
                        pool_scores.append(pool_score)
                        detailed_analysis.append(analysis)
                        
                except Exception as e:
                    logger.error("pool_scoring_failed", 
                               pool_id=pool_raw.id, 
                               error=str(e))
            
            # 4. 排序和最終處理
            pool_scores = sorted(pool_scores, key=lambda x: x.score, reverse=True)
            
            logger.info("scorer_v2_execution_completed", 
                       pools_processed=len(pool_raws),
                       pools_scored=len(pool_scores),
                       market_condition=market_condition,
                       dynamic_weights=dynamic_weights.__dict__)
            
            return AgentResult(
                agent_name=self.name,
                data=pool_scores,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "pools_processed": len(pool_raws),
                    "pools_scored": len(pool_scores),
                    "avg_score": sum(p.score for p in pool_scores) / len(pool_scores) if pool_scores else 0,
                    "market_condition": market_condition,
                    "dynamic_weights": dynamic_weights.__dict__,
                    "top_pools": [{"id": p.id, "score": p.score} for p in pool_scores[:5]],
                    "detailed_analysis": detailed_analysis[:10]  # 前10個的詳細分析
                }
            )
            
        except Exception as e:
            logger.error("scorer_v2_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )
    
    def _get_pool_raws(self) -> List[PoolRaw]:
        """獲取需要評分的 PoolRaw 數據"""
        # TODO: 實際實現從數據庫或 Scout Agent 獲取數據
        return []
    
    async def _analyze_market_condition(self) -> Dict[str, Any]:
        """分析當前市場情況"""
        # TODO: 實現市場分析邏輯
        # 應該包含：波動率、交易量趨勢、恐慌指數等
        return {
            "volatility_level": "medium",  # low, medium, high
            "volume_trend": "stable",      # increasing, stable, decreasing
            "market_sentiment": "neutral", # bullish, neutral, bearish
            "risk_appetite": "moderate"    # conservative, moderate, aggressive
        }
    
    def _calculate_dynamic_weights(self, market_condition: Dict[str, Any]) -> DynamicWeights:
        """根據市場情況計算動態權重"""
        weights = DynamicWeights(**self.base_weights)
        
        if not self.market_condition_adjustment:
            return weights.normalize()
        
        # 根據市場波動率調整權重
        volatility_level = market_condition.get("volatility_level", "medium")
        if volatility_level == "high":
            # 高波動時更重視 TVL 和流動性深度
            weights.tvl_score *= 1.2
            weights.liquidity_depth *= 1.3
            weights.fee_tvl *= 0.8
            weights.volatility_score *= 1.5
        elif volatility_level == "low":
            # 低波動時更重視費率收益
            weights.fee_tvl *= 1.2
            weights.volume_score *= 1.1
            weights.volatility_score *= 0.5
        
        # 根據市場情緒調整
        sentiment = market_condition.get("market_sentiment", "neutral")
        if sentiment == "bearish":
            # 熊市時更保守，重視穩定性
            weights.token_quality *= 1.3
            weights.tvl_score *= 1.2
            weights.fee_tvl *= 0.9
        elif sentiment == "bullish":
            # 牛市時可以承受更高風險
            weights.fee_tvl *= 1.1
            weights.volume_score *= 1.2
            weights.token_quality *= 0.9
        
        return weights.normalize()
    
    async def _get_pool_detailed_info(self, pool_raw: PoolRaw) -> Optional[PoolDetailedInfo]:
        """獲取池子的詳細信息"""
        # TODO: 實現從區塊鏈或API獲取詳細信息的邏輯
        # 這裡應該包含：
        # 1. 代幣地址和符號
        # 2. 精確的費率信息
        # 3. 流動性深度計算
        # 4. 價格波動率計算
        # 5. 準確的24小時交易量
        
        # 模擬數據，實際實現需要替換
        return PoolDetailedInfo(
            token0_symbol="TOKEN0",
            token1_symbol="TOKEN1",
            token0_address="0x...",
            token1_address="0x...",
            fee_rate=0.0025,  # 0.25%
            liquidity_depth=pool_raw.tvl_usd * 0.1,  # 估算可用流動性為TVL的10%
            price_volatility=0.02,  # 2% 日波動率
            volume_24h=pool_raw.fee24h / 0.0025 if pool_raw.fee24h > 0 else 0
        )
    
    def _score_pool_enhanced(self, pool_raw: PoolRaw, pool_detail: PoolDetailedInfo, 
                           weights: DynamicWeights) -> Tuple[Optional[PoolScore], Dict[str, Any]]:
        """增強版池子評分算法"""
        try:
            # 1. 基本過濾
            if not self._meets_enhanced_criteria(pool_raw, pool_detail):
                return None, {"reason": "failed_basic_criteria"}
            
            # 2. 計算各項評分
            factor_scores = {
                'fee_score': self._calculate_fee_score_v2(pool_raw, pool_detail),
                'volume_score': self._calculate_volume_score_v2(pool_raw, pool_detail),
                'tvl_score': self._calculate_tvl_score_v2(pool_raw, pool_detail),
                'token_quality_score': self._calculate_token_quality_score_v2(pool_detail),
                'liquidity_depth_score': self._calculate_liquidity_depth_score(pool_detail),
                'volatility_score': self._calculate_volatility_score(pool_detail)
            }
            
            # 3. 計算加權綜合評分
            final_score = (
                factor_scores['fee_score'] * weights.fee_tvl +
                factor_scores['volume_score'] * weights.volume_score +
                factor_scores['tvl_score'] * weights.tvl_score +
                factor_scores['token_quality_score'] * weights.token_quality +
                factor_scores['liquidity_depth_score'] * weights.liquidity_depth +
                factor_scores['volatility_score'] * weights.volatility_score
            )
            
            # 4. 風險調整
            risk_adjustment = self._calculate_risk_adjustment(pool_detail)
            adjusted_score = final_score * risk_adjustment
            
            # 5. 判斷是否可對沖
            hedgeable = self._is_hedgeable_v2(pool_detail)
            
            analysis = {
                "factor_scores": factor_scores,
                "weights_used": weights.__dict__,
                "risk_adjustment": risk_adjustment,
                "final_score_before_risk": final_score,
                "hedgeable_analysis": hedgeable
            }
            
            return PoolScore(
                id=pool_raw.id,
                score=round(adjusted_score, 2),
                hedgeable=hedgeable
            ), analysis
            
        except Exception as e:
            logger.error("enhanced_pool_scoring_error", 
                        pool_id=pool_raw.id, 
                        error=str(e))
            return None, {"error": str(e)}
    
    def _meets_enhanced_criteria(self, pool: PoolRaw, detail: PoolDetailedInfo) -> bool:
        """增強的基本篩選條件"""
        return (
            pool.tvl_usd >= self.thresholds['min_tvl'] and
            detail.volume_24h >= self.thresholds['min_volume_24h'] and
            pool.fee_tvl >= self.thresholds['min_fee_tvl'] and
            detail.liquidity_depth >= self.thresholds['min_liquidity_depth'] and
            detail.price_volatility <= self.thresholds['max_volatility'] and
            not self._is_high_risk_token(detail)
        )
    
    def _is_high_risk_token(self, detail: PoolDetailedInfo) -> bool:
        """檢查是否包含高風險代幣"""
        high_risk = self.thresholds['high_risk_tokens']
        return (detail.token0_symbol in high_risk or 
                detail.token1_symbol in high_risk)
    
    def _calculate_fee_score_v2(self, pool: PoolRaw, detail: PoolDetailedInfo) -> float:
        """增強版費率評分"""
        # 基於實際費率而非估算
        annual_fee_rate = pool.fee_tvl
        
        # 考慮費率的可持續性（基於歷史數據）
        sustainability_factor = 1.0
        if detail.historical_apy:
            current_vs_historical = annual_fee_rate / detail.historical_apy
            if current_vs_historical > 2.0:  # 當前費率遠高於歷史平均，可能不可持續
                sustainability_factor = 0.8
            elif current_vs_historical < 0.5:  # 當前費率遠低於歷史平均，可能反彈
                sustainability_factor = 1.1
        
        base_score = self._calculate_fee_score(pool)  # 使用原有邏輯
        return min(100.0, base_score * sustainability_factor)
    
    def _calculate_volume_score_v2(self, pool: PoolRaw, detail: PoolDetailedInfo) -> float:
        """增強版交易量評分"""
        volume = detail.volume_24h
        
        # 基礎分數
        if volume >= 2000000:  # $2M+
            base_score = 100.0
        elif volume >= 1000000:  # $1M-2M
            base_score = 90.0 + (volume - 1000000) * (10.0 / 1000000)
        elif volume >= 500000:  # $500k-1M
            base_score = 80.0 + (volume - 500000) * (10.0 / 500000)
        elif volume >= 100000:  # $100k-500k
            base_score = 60.0 + (volume - 100000) * (20.0 / 400000)
        elif volume >= 50000:   # $50k-100k
            base_score = 40.0 + (volume - 50000) * (20.0 / 50000)
        else:  # <$50k
            base_score = volume * (40.0 / 50000)
        
        # 考慮交易量的穩定性
        stability_factor = 1.0
        # TODO: 實現基於歷史交易量穩定性的調整
        
        return min(100.0, base_score * stability_factor)
    
    def _calculate_tvl_score_v2(self, pool: PoolRaw, detail: PoolDetailedInfo) -> float:
        """增強版 TVL 評分"""
        # 使用原有邏輯，但增加 TVL 穩定性考量
        base_score = self._calculate_tvl_score(pool)
        
        # TVL 與流動性深度的比率（健康的池子應該有較好的流動性深度）
        liquidity_efficiency = detail.liquidity_depth / pool.tvl_usd if pool.tvl_usd > 0 else 0
        if liquidity_efficiency >= 0.15:  # 15%+ 很好的流動性效率
            efficiency_bonus = 1.1
        elif liquidity_efficiency >= 0.10:  # 10-15% 不錯的效率
            efficiency_bonus = 1.05
        elif liquidity_efficiency < 0.05:  # <5% 流動性效率較差
            efficiency_bonus = 0.9
        else:
            efficiency_bonus = 1.0
        
        return min(100.0, base_score * efficiency_bonus)
    
    def _calculate_token_quality_score_v2(self, detail: PoolDetailedInfo) -> float:
        """增強版代幣品質評分"""
        token0 = detail.token0_symbol.upper()
        token1 = detail.token1_symbol.upper()
        
        stable_tokens = [t.upper() for t in self.thresholds['stable_tokens']]
        blue_chip_tokens = [t.upper() for t in self.thresholds['blue_chip_tokens']]
        
        # 計算代幣品質分數
        scores = []
        for token in [token0, token1]:
            if token in stable_tokens:
                scores.append(95.0)  # 穩定幣最高分
            elif token in blue_chip_tokens:
                scores.append(85.0)  # 藍籌代幣高分
            else:
                # 其他代幣基於一些啟發式規則評分
                if len(token) <= 5 and token.isalpha():  # 短名稱，可能是主流代幣
                    scores.append(60.0)
                else:  # 長名稱或包含數字，可能是新代幣
                    scores.append(30.0)
        
        # 代幣對組合獎勵
        avg_score = sum(scores) / len(scores)
        
        # 穩定幣對獎勵
        if all(token in stable_tokens for token in [token0, token1]):
            avg_score = min(100.0, avg_score * 1.05)
        # 穩定幣+藍籌組合獎勵
        elif (token0 in stable_tokens and token1 in blue_chip_tokens) or \
             (token1 in stable_tokens and token0 in blue_chip_tokens):
            avg_score = min(100.0, avg_score * 1.1)
        
        return avg_score
    
    def _calculate_liquidity_depth_score(self, detail: PoolDetailedInfo) -> float:
        """計算流動性深度評分"""
        depth = detail.liquidity_depth
        
        if depth >= 500000:  # $500k+
            return 100.0
        elif depth >= 200000:  # $200k-500k
            return 80.0 + (depth - 200000) * (20.0 / 300000)
        elif depth >= 100000:  # $100k-200k
            return 60.0 + (depth - 100000) * (20.0 / 100000)
        elif depth >= 50000:   # $50k-100k
            return 40.0 + (depth - 50000) * (20.0 / 50000)
        else:  # <$50k
            return depth * (40.0 / 50000)
    
    def _calculate_volatility_score(self, detail: PoolDetailedInfo) -> float:
        """計算波動率評分（波動率越低分數越高）"""
        volatility = detail.price_volatility
        
        if volatility <= 0.01:  # ≤1% 極低波動
            return 100.0
        elif volatility <= 0.02:  # 1-2% 低波動
            return 90.0 - (volatility - 0.01) * (10.0 / 0.01)
        elif volatility <= 0.05:  # 2-5% 中等波動
            return 70.0 - (volatility - 0.02) * (20.0 / 0.03)
        elif volatility <= 0.10:  # 5-10% 高波動
            return 30.0 - (volatility - 0.05) * (40.0 / 0.05)
        else:  # >10% 極高波動
            return 0.0
    
    def _calculate_risk_adjustment(self, detail: PoolDetailedInfo) -> float:
        """計算風險調整係數"""
        adjustment = 1.0
        
        # 波動率風險調整
        if detail.price_volatility > 0.08:  # >8% 日波動率
            adjustment *= 0.8
        elif detail.price_volatility > 0.05:  # >5% 日波動率
            adjustment *= 0.9
        
        # 流動性風險調整
        if detail.liquidity_depth < 100000:  # <$100k 流動性深度
            adjustment *= 0.85
        
        # 代幣風險調整
        token0 = detail.token0_symbol.upper()
        token1 = detail.token1_symbol.upper()
        stable_tokens = [t.upper() for t in self.thresholds['stable_tokens']]
        blue_chip_tokens = [t.upper() for t in self.thresholds['blue_chip_tokens']]
        
        risk_tokens = sum(1 for token in [token0, token1] 
                         if token not in stable_tokens and token not in blue_chip_tokens)
        if risk_tokens == 2:  # 兩個都是風險代幣
            adjustment *= 0.8
        elif risk_tokens == 1:  # 一個風險代幣
            adjustment *= 0.9
        
        return max(0.5, adjustment)  # 最低調整係數為0.5
    
    def _is_hedgeable_v2(self, detail: PoolDetailedInfo) -> bool:
        """增強版可對沖判斷"""
        token0 = detail.token0_symbol.upper()
        token1 = detail.token1_symbol.upper()
        
        stable_tokens = [t.upper() for t in self.thresholds['stable_tokens']]
        blue_chip_tokens = [t.upper() for t in self.thresholds['blue_chip_tokens']]
        
        hedgeable_tokens = stable_tokens + blue_chip_tokens
        
        # 至少有一個代幣可對沖，且流動性充足
        has_hedgeable_token = any(token in hedgeable_tokens for token in [token0, token1])
        sufficient_liquidity = detail.liquidity_depth >= 200000  # $200k 最小對沖流動性
        
        return has_hedgeable_token and sufficient_liquidity
    
    # 繼承原有的簡單評分方法作為備用
    def _calculate_fee_score(self, pool: PoolRaw) -> float:
        """原有的費率評分方法（備用）"""
        fee_tvl = pool.fee_tvl
        
        if fee_tvl >= 50:
            return 100.0
        elif fee_tvl >= 30:
            return 80.0 + (fee_tvl - 30) * 1.0
        elif fee_tvl >= 15:
            return 60.0 + (fee_tvl - 15) * (20.0 / 15)
        elif fee_tvl >= 5:
            return 20.0 + (fee_tvl - 5) * (40.0 / 10)
        else:
            return fee_tvl * 4.0
    
    def _calculate_tvl_score(self, pool: PoolRaw) -> float:
        """原有的 TVL 評分方法（備用）"""
        tvl = pool.tvl_usd
        
        if tvl >= 10000000:
            return 100.0
        elif tvl >= 5000000:
            return 80.0 + (tvl - 5000000) * (20.0 / 5000000)
        elif tvl >= 1000000:
            return 60.0 + (tvl - 1000000) * (20.0 / 4000000)
        elif tvl >= 500000:
            return 40.0 + (tvl - 500000) * (20.0 / 500000)
        elif tvl >= 50000:
            return 20.0 + (tvl - 50000) * (20.0 / 450000)
        else:
            return tvl * (20.0 / 50000)
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("scorer_v2_cleanup_completed")
        except Exception as e:
            logger.error("scorer_v2_cleanup_failed", error=str(e))

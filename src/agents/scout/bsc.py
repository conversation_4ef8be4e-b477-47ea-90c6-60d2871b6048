"""
Scout BSC Agent - Dy-Flow v3
負責 BSC PancakeSwap 數據收集，每5分鐘執行
按照 v3 架構規範實現 PoolRaw 數據格式
"""

import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog

from ..base_agent import BaseAgent
from ...utils.models_v3 import PoolRaw, AgentResult
from ...utils.helpers import get_utc_timestamp
from ...utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class ScoutBSC(BaseAgent):
    """Scout BSC Agent - BSC PancakeSwap 數據收集"""
    
    def __init__(self, name: str, config, database):
        super().__init__(name, config, database)
        
        # Agent-specific config from agno_flow.yaml or default.yaml (via BaseAgent)
        agent_specific_config = self.agent_config or {}

        # BSC network configuration from main Config object
        bsc_network_config = self.config.get_network_config('bsc')
        self.rpc_url = agent_specific_config.get('rpc_url', bsc_network_config.rpc_url)
        
        # API endpoints can be hardcoded or come from agent_specific_config if needed
        self.api_endpoints = agent_specific_config.get('api_endpoints', {
            'pools': 'https://api.pancakeswap.info/api/v2/pools',
            'tokens': 'https://api.pancakeswap.info/api/v2/tokens'
        })
        
        # Strategy configuration from main Config object
        strategy_config = self.config.strategy

        # Filter conditions, use agent_specific_config to override strategy_config if present
        self.min_tvl = agent_specific_config.get('min_tvl', strategy_config.min_tvl)
        self.min_volume_24h = agent_specific_config.get('min_volume_24h', strategy_config.min_volume_24h)
        self.max_pools_per_scan = agent_specific_config.get('max_pools_per_scan', strategy_config.max_pools_per_chain)
        
        # Target tokens, prioritize agent_specific_config
        default_bsc_tokens = ['WBNB', 'WETH', 'BTCB', 'USDT', 'USDC', 'BUSD']
        # Combine preferred_tokens with specific bsc tokens, ensuring no duplicates and prioritizing agent_specific
        preferred_tokens_set = set(strategy_config.preferred_tokens)
        default_bsc_tokens_set = set(default_bsc_tokens)
        combined_tokens = list(preferred_tokens_set.union(default_bsc_tokens_set))
        
        self.target_tokens = agent_specific_config.get('target_tokens', combined_tokens)
    
    async def initialize(self) -> None:
        """初始化 Agent"""
        await super().initialize()
        logger.info("scout_bsc_initialized",
                   min_tvl=self.min_tvl,
                   target_tokens=self.target_tokens)
        self.is_initialized = True

    async def execute(self) -> AgentResult:
        """執行 BSC 數據收集"""
        if not self.is_initialized:
            await self.initialize()
        
        try:
            logger.info("scout_bsc_execution_started")
            
            # 獲取池子數據
            pools_data = await self._fetch_pools_data()
            
            # 解析為 PoolRaw 格式
            pool_raws = []
            for pool_data in pools_data:
                pool_raw = self._parse_to_pool_raw(pool_data)
                if pool_raw and self._meets_criteria(pool_raw):
                    pool_raws.append(pool_raw)
            
            # 限制數量並排序
            sorted_pools = sorted(pool_raws, key=lambda p: p.tvl_usd, reverse=True)
            final_pools = sorted_pools[:self.max_pools_per_scan]
            
            logger.info("scout_bsc_execution_completed", 
                       pools_found=len(final_pools),
                       total_scanned=len(pools_data))
            
            return AgentResult(
                agent_name=self.name,
                data=final_pools,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "pools_scanned": len(pools_data),
                    "pools_filtered": len(final_pools),
                    "chain": "BSC"
                }
            )
            
        except Exception as e:
            logger.error("scout_bsc_execution_failed", error=str(e))
            return AgentResult(
                agent_name=self.name,
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )
    
    async def _fetch_pools_data(self) -> List[Dict[str, Any]]:
        """獲取池子數據"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.api_endpoints['pools'], 
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status != 200:
                        raise DyFlowException(f"BSC API 錯誤: {response.status}")
                    
                    data = await response.json()
                    return data.get('data', [])
                    
        except Exception as e:
            logger.error("bsc_pool_fetch_failed", error=str(e))
            raise DyFlowException(f"BSC 池子數據獲取失敗: {e}")
    
    def _parse_to_pool_raw(self, pool_data: Dict[str, Any]) -> Optional[PoolRaw]:
        """解析為 PoolRaw 格式"""
        try:
            pool_address = pool_data.get('address')
            if not pool_address:
                return None
            
            # 獲取代幣信息
            token0 = pool_data.get('token0', {})
            token1 = pool_data.get('token1', {})
            
            token0_symbol = token0.get('symbol', '').upper()
            token1_symbol = token1.get('symbol', '').upper()
            
            if not token0_symbol or not token1_symbol:
                return None
            
            # 計算指標
            tvl_usd = float(pool_data.get('tvlUSD', 0))
            volume_24h = float(pool_data.get('volumeUSD24h', 0))
            fee_rate = float(pool_data.get('feeTier', 300)) / 1000000  # 轉換為小數
            
            # 計算 fee24h 和 fee_tvl
            fee24h = volume_24h * fee_rate
            fee_tvl = (fee24h / tvl_usd * 100) if tvl_usd > 0 else 0.0
            
            return PoolRaw(
                id=pool_address,
                chain="BSC",
                tvl_usd=tvl_usd,
                fee24h=fee24h,
                fee_tvl=fee_tvl,
                created_at=get_utc_timestamp()
            )
            
        except (ValueError, KeyError, TypeError) as e:
            logger.warning("failed_to_parse_bsc_pool", 
                         pool_address=pool_data.get('address'), 
                         error=str(e))
            return None
    
    def _meets_criteria(self, pool: PoolRaw) -> bool:
        """檢查池子是否滿足篩選條件"""
        try:
            # TVL 檢查
            if pool.tvl_usd < self.min_tvl:
                return False
            
            # 交易量檢查 (通過 fee24h 反推)
            estimated_volume = pool.fee24h / 0.0025 if pool.fee24h > 0 else 0  # 假設 0.25% 費率
            if estimated_volume < self.min_volume_24h:
                return False
            
            # 代幣白名單檢查 (需要從池子ID或其他方式獲取代幣信息)
            # 暫時跳過，因為 PoolRaw 標準格式沒有 token0/token1 字段
            # TODO: 實現從池子地址獲取代幣信息的邏輯
            if False:  # self.target_tokens:
                pass  # 暫時禁用代幣檢查
            
            # 費率合理性檢查 (通過 fee_tvl 檢查)
            if pool.fee_tvl <= 0 or pool.fee_tvl > 50:  # 年化費率不應超過5000%
                return False
            
            return True
            
        except Exception as e:
            logger.warning("criteria_check_failed", 
                         pool_id=pool.id, 
                         error=str(e))
            return False
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("scout_bsc_cleanup_completed")
        except Exception as e:
            logger.error("scout_bsc_cleanup_failed", error=str(e))
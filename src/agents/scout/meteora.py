"""
Scout Meteora Agent - Dy-Flow v3
負責 Solana Meteora DLMM 數據收集，每1分鐘執行
按照 v3 架構規範實現 PoolRaw 數據格式
"""

import asyncio
import aiohttp
from typing import List, Dict, Any, Optional
from datetime import datetime
import structlog

from ..base_agent import BaseAgent
from ...utils.models_v3 import PoolRaw, AgentResult
from ...utils.helpers import get_utc_timestamp
from ...utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class ScoutMeteora(BaseAgent):
    """Scout Meteora Agent - Solana Meteora DLMM 數據收集"""
    
    def __init__(self, name: str, config, database): # Match BaseAgent
        super().__init__(name, config, database) # Pass all args
        # self.agent_name is already set by BaseAgent
        
        # Agent-specific config from agno_flow.yaml or default.yaml (via BaseAgent)
        agent_specific_config = self.agent_config or {}

        # Solana network configuration from main Config object
        solana_network_config = self.config.get_network_config('solana')
        self.rpc_url = agent_specific_config.get('rpc_url', solana_network_config.rpc_url)
        
        # API endpoints can be hardcoded or come from agent_specific_config if needed
        self.api_endpoints = agent_specific_config.get('api_endpoints', {
            'pools': 'https://dlmm-api.meteora.ag/pair/all',
            'markets': 'https://dlmm-api.meteora.ag/market/all'
        })
        
        # Strategy configuration from main Config object
        strategy_config = self.config.strategy

        # Filter conditions, use agent_specific_config to override strategy_config if present
        self.min_tvl = agent_specific_config.get('min_tvl', strategy_config.min_tvl)
        # For min_volume_24h, Meteora might have a specific lower threshold
        self.min_volume_24h = agent_specific_config.get('min_volume_24h', strategy_config.min_volume_24h / 10) # Example: 1/10th of general
        self.max_pools_per_scan = agent_specific_config.get('max_pools_per_scan', strategy_config.max_pools_per_chain)
        
        # Target tokens, prioritize agent_specific_config
        default_sol_tokens = ['SOL', 'WSOL', 'USDC', 'USDT', 'BONK', 'WIF', 'JTO']
        # Combine preferred_tokens with specific solana tokens, ensuring no duplicates and prioritizing agent_specific
        preferred_tokens_set = set(strategy_config.preferred_tokens)
        default_sol_tokens_set = set(default_sol_tokens)
        combined_tokens = list(preferred_tokens_set.union(default_sol_tokens_set))
        
        self.target_tokens = agent_specific_config.get('target_tokens', combined_tokens)

    async def initialize(self) -> None:
        """Initialize Agent"""
        await super().initialize() # Call parent's initialize
        logger.info(f"{self.name}_initialized",
                   min_tvl=self.min_tvl,
                   target_tokens=self.target_tokens)
        self.is_initialized = True
    
    async def execute(self) -> AgentResult:
        """執行 Meteora 數據收集"""
        try:
            logger.info("scout_meteora_execution_started")
            
            # 獲取池子數據
            pools_data = await self._fetch_pools_data()
            
            # 解析為 PoolRaw 格式
            pool_raws = []
            for pool_data in pools_data:
                pool_raw = self._parse_to_pool_raw(pool_data)
                if pool_raw and self._meets_criteria(pool_raw):
                    pool_raws.append(pool_raw)
            
            # 限制數量並排序
            sorted_pools = sorted(pool_raws, key=lambda p: p.fee_tvl, reverse=True)
            final_pools = sorted_pools[:self.max_pools_per_scan]
            
            logger.info("scout_meteora_execution_completed", 
                       pools_found=len(final_pools),
                       total_scanned=len(pools_data))
            
            return AgentResult(
                data=final_pools,
                timestamp=get_utc_timestamp(),
                status="success",
                metadata={
                    "pools_scanned": len(pools_data),
                    "pools_filtered": len(final_pools),
                    "chain": "SOL"
                }
            )
            
        except Exception as e:
            logger.error("scout_meteora_execution_failed", error=str(e))
            return AgentResult(
                data=[],
                timestamp=get_utc_timestamp(),
                status="error",
                metadata={"error": str(e)}
            )
    
    async def _fetch_pools_data(self) -> List[Dict[str, Any]]:
        """獲取池子數據"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.api_endpoints['pools'], 
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status != 200:
                        raise DyFlowException(f"Meteora API 錯誤: {response.status}")
                    
                    data = await response.json()
                    # Meteora API 直接返回 list
                    return data if isinstance(data, list) else data.get('data', [])
                    
        except Exception as e:
            logger.error("meteora_pool_fetch_failed", error=str(e))
            raise DyFlowException(f"Meteora 池子數據獲取失敗: {e}")
    
    def _parse_to_pool_raw(self, pool_data: Dict[str, Any]) -> Optional[PoolRaw]:
        """解析為 PoolRaw 格式"""
        try:
            pool_address = pool_data.get('address')
            if not pool_address:
                return None
            
            # 獲取代幣信息
            token_x = pool_data.get('mint_x', {})
            token_y = pool_data.get('mint_y', {})
            
            token0_symbol = token_x.get('symbol', '').upper()
            token1_symbol = token_y.get('symbol', '').upper()
            
            if not token0_symbol or not token1_symbol:
                return None
            
            # 計算指標
            tvl_usd = float(pool_data.get('liquidity_usd', 0))
            volume_24h = float(pool_data.get('volume_24h', 0))
            fee_rate = float(pool_data.get('fee_rate', 0)) / 100  # 轉換為小數
            
            # 計算 fee24h 和 fee_tvl
            fee24h = volume_24h * fee_rate
            fee_tvl = (fee24h / tvl_usd * 100) if tvl_usd > 0 else 0.0
            
            # 獲取價格信息
            token_x_price = float(pool_data.get('mint_x', {}).get('price', 0))
            token_y_price = float(pool_data.get('mint_y', {}).get('price', 0))
            
            return PoolRaw(
                id=pool_address,
                chain="SOL",
                token0=token0_symbol,
                token1=token1_symbol,
                tvl_usd=tvl_usd,
                volume24h=volume_24h,
                fee24h=fee24h,
                fee_tvl=fee_tvl,
                fee_rate=fee_rate,
                token0_price=token_x_price,
                token1_price=token_y_price,
                created_at=get_utc_timestamp()
            )
            
        except (ValueError, KeyError, TypeError) as e:
            logger.warning("failed_to_parse_meteora_pool", 
                         pool_address=pool_data.get('address'), 
                         error=str(e))
            return None
    
    def _meets_criteria(self, pool: PoolRaw) -> bool:
        """檢查池子是否滿足篩選條件"""
        try:
            # TVL 檢查
            if pool.tvl_usd < self.min_tvl:
                return False
            
            # 交易量檢查
            if pool.volume24h < self.min_volume_24h:
                return False
            
            # 代幣白名單檢查
            if self.target_tokens:
                if not (pool.token0 in self.target_tokens or pool.token1 in self.target_tokens):
                    return False
            
            # 費率合理性檢查
            if pool.fee_rate <= 0 or pool.fee_rate > 0.1:
                return False
            
            # fee_tvl 必須大於 5%（年化）
            if pool.fee_tvl < 5.0:
                return False
            
            return True
            
        except Exception as e:
            logger.warning("criteria_check_failed", 
                         pool_id=pool.id, 
                         error=str(e))
            return False
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("scout_meteora_cleanup_completed")
        except Exception as e:
            logger.error("scout_meteora_cleanup_failed", error=str(e))
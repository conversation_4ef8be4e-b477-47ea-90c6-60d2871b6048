"""
BSC Scout Agent - 基於 Agno Framework
5分鐘週期收集 BSC PancakeSwap V3 池數據
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
import structlog

from .base_agent import BaseAgent
from ..utils.config import Config
from ..utils.database import Database
from ..integrations.pancakeswap import PancakeSwapAPI

# Agno Framework 導入
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

logger = structlog.get_logger(__name__)


@dataclass
class PoolRaw:
    """池原始數據 (Dy-Flow v3 標準)"""
    id: str
    chain: str
    protocol: str
    name: str
    tvl_usd: float
    fee24h: float
    fee_tvl: float
    created_at: datetime


class ScoutBSCAgent(BaseAgent):
    """BSC Scout Agent - 收集 PancakeSwap 池數據"""
    
    def __init__(self, config: Config, database: Database):
        super().__init__("scout_bsc", config, database)
        
        # Agent 配置
        self.schedule_interval = self.agent_config.get('schedule_interval', 300)  # 5分鐘
        self.min_tvl_usd = self.agent_config.get('min_tvl_usd', 100000)  # 最小 TVL
        self.max_pools = self.agent_config.get('max_pools', 50)  # 最大池數量
        
        # Agno Agent
        self.agno_agent = None
        
    async def initialize(self) -> None:
        """初始化 Scout BSC Agent"""
        try:
            logger.info("scout_bsc_initializing")
            
            # 初始化 Agno Agent（如果可用）
            if AGNO_AVAILABLE and self.agent_config.get('openai_api_key'):
                await self._initialize_agno_agent()
            
            self.is_initialized = True
            logger.info("scout_bsc_initialized", agno_enabled=self.agno_agent is not None)
            
        except Exception as e:
            logger.error("scout_bsc_initialization_failed", error=str(e))
            raise
    
    async def _initialize_agno_agent(self) -> None:
        """初始化 Agno Agent"""
        try:
            self.agno_agent = Agent(
                name="DyFlow BSC Scout",
                description="BSC PancakeSwap V3 池數據收集專家",
                model=OpenAIChat(
                    id="gpt-4o",
                    api_key=self.agent_config.get('openai_api_key')
                ),
                tools=[
                    self._agno_fetch_top_pools,
                    self._agno_analyze_pool_metrics,
                    self._agno_filter_pools
                ],
                instructions=[
                    "你是 BSC PancakeSwap V3 數據收集專家",
                    "收集高 TVL 和高手續費的優質池",
                    "過濾掉低質量和風險池",
                    "提供數據質量分析"
                ],
                show_tool_calls=True,
                markdown=True
            )
            
        except Exception as e:
            logger.warning("agno_scout_bsc_init_failed", error=str(e))
            self.agno_agent = None
    
    async def execute(self) -> Dict[str, Any]:
        """執行 BSC 池數據收集"""
        try:
            logger.info("scout_bsc_execution_started")
            
            # 收集池數據
            pools_raw = await self._fetch_pools()
            
            # 保存到數據庫
            saved_count = await self._save_pools(pools_raw)
            
            # 返回結果
            result = {
                'success': True,
                'pools_collected': len(pools_raw),
                'pools_saved': saved_count,
                'timestamp': datetime.utcnow().isoformat(),
                'data': [asdict(pool) for pool in pools_raw],
                'items_processed': len(pools_raw)
            }
            
            logger.info("scout_bsc_execution_completed", 
                       pools_collected=len(pools_raw),
                       pools_saved=saved_count)
            
            return result
            
        except Exception as e:
            logger.error("scout_bsc_execution_failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat(),
                'items_processed': 0
            }
    
    async def _fetch_pools(self) -> List[PoolRaw]:
        """獲取 BSC 池數據"""
        pools_raw = []
        
        try:
            # 使用 PancakeSwap API
            async with PancakeSwapAPI() as pancake_api:
                pools_data = await pancake_api.get_top_pools(
                    limit=self.max_pools,
                    min_tvl=self.min_tvl_usd
                )
                
                for pool_data in pools_data:
                    try:
                        pool = PoolRaw(
                            id=pool_data['id'],
                            chain='BSC',
                            protocol='PancakeSwap_V3',
                            name=f"{pool_data.get('token0', {}).get('symbol', '')}-{pool_data.get('token1', {}).get('symbol', '')}",
                            tvl_usd=float(pool_data.get('tvlUSD', 0)),
                            fee24h=float(pool_data.get('volumeUSD', 0)),
                            fee_tvl=float(pool_data.get('feeTier', 0)) / 10000,  # 轉換為百分比
                            created_at=datetime.utcnow()
                        )
                        
                        # 質量過濾
                        if self._is_quality_pool(pool):
                            pools_raw.append(pool)
                            
                    except Exception as e:
                        logger.warning("pool_parsing_failed", pool_id=pool_data.get('id'), error=str(e))
                        
        except Exception as e:
            logger.error("fetch_pools_failed", error=str(e))
            
        return pools_raw
    
    def _is_quality_pool(self, pool: PoolRaw) -> bool:
        """檢查池質量"""
        # 基本質量檢查
        if pool.tvl_usd < self.min_tvl_usd:
            return False
        
        if pool.fee_tvl <= 0:
            return False
        
        # 檢查代幣對名稱
        if not pool.name or '-' not in pool.name:
            return False
        
        return True
    
    async def _save_pools(self, pools: List[PoolRaw]) -> int:
        """保存池數據到數據庫"""
        saved_count = 0
        
        for pool in pools:
            try:
                # 轉換為字典格式
                pool_dict = asdict(pool)
                
                # 保存到數據庫
                await self.save_to_database('pools_raw_bsc', pool_dict)
                saved_count += 1
                
            except Exception as e:
                logger.warning("save_pool_failed", pool_id=pool.id, error=str(e))
        
        return saved_count
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("scout_bsc_cleanup")
        except Exception as e:
            logger.warning("scout_bsc_cleanup_failed", error=str(e))
    
    # Agno Agent 工具函數
    def _agno_fetch_top_pools(self, limit: int = 50) -> str:
        """Agno 工具：獲取頂級池"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                pools = loop.run_until_complete(self._fetch_pools())
                return f"成功獲取 {len(pools)} 個高質量池"
            finally:
                loop.close()
        except Exception as e:
            return f"獲取池數據失敗: {str(e)}"
    
    def _agno_analyze_pool_metrics(self, pools_data: str) -> str:
        """Agno 工具：分析池指標"""
        # 這裡可以添加更複雜的分析邏輯
        return "池指標分析：TVL、手續費、流動性分佈正常"
    
    def _agno_filter_pools(self, criteria: str) -> str:
        """Agno 工具：過濾池"""
        return f"根據條件 '{criteria}' 過濾池數據"


# 工廠函數
def create_scout_bsc_agent(config: Config, database: Database) -> ScoutBSCAgent:
    """創建 BSC Scout Agent"""
    return ScoutBSCAgent(config, database)
"""
Meteora Scout Agent - 基於 Agno Framework
1分鐘週期收集 Solana Meteora 池數據
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any
from dataclasses import dataclass, asdict
import structlog

from .base_agent import BaseAgent
from ..utils.config import Config
from ..utils.database import Database
from ..integrations.meteora import MeteoraAPI
from .scout_bsc_agno import PoolRaw  # 重用數據結構

# Agno Framework 導入
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

logger = structlog.get_logger(__name__)


class ScoutMeteoraAgent(BaseAgent):
    """Meteora Scout Agent - 收集 Solana Meteora 池數據"""
    
    def __init__(self, config: Config, database: Database):
        super().__init__("scout_meteora", config, database)
        
        # Agent 配置
        self.schedule_interval = self.agent_config.get('schedule_interval', 60)  # 1分鐘
        self.min_tvl_usd = self.agent_config.get('min_tvl_usd', 50000)  # 最小 TVL
        self.max_pools = self.agent_config.get('max_pools', 30)  # 最大池數量
        
        # Agno Agent
        self.agno_agent = None
        
    async def initialize(self) -> None:
        """初始化 Scout Meteora Agent"""
        try:
            logger.info("scout_meteora_initializing")
            
            # 初始化 Agno Agent（如果可用）
            if AGNO_AVAILABLE and self.agent_config.get('openai_api_key'):
                await self._initialize_agno_agent()
            
            self.is_initialized = True
            logger.info("scout_meteora_initialized", agno_enabled=self.agno_agent is not None)
            
        except Exception as e:
            logger.error("scout_meteora_initialization_failed", error=str(e))
            raise
    
    async def _initialize_agno_agent(self) -> None:
        """初始化 Agno Agent"""
        try:
            self.agno_agent = Agent(
                name="DyFlow Meteora Scout",
                description="Solana Meteora 池數據收集專家",
                model=OpenAIChat(
                    id="gpt-4o",
                    api_key=self.agent_config.get('openai_api_key')
                ),
                tools=[
                    self._agno_fetch_meteora_pools,
                    self._agno_analyze_sol_metrics,
                    self._agno_compare_chains
                ],
                instructions=[
                    "你是 Solana Meteora 數據收集專家",
                    "收集高流動性 SOL 生態池",
                    "監控 Solana 網絡狀態",
                    "比較多鏈機會"
                ],
                show_tool_calls=True,
                markdown=True
            )
            
        except Exception as e:
            logger.warning("agno_scout_meteora_init_failed", error=str(e))
            self.agno_agent = None
    
    async def execute(self) -> Dict[str, Any]:
        """執行 Meteora 池數據收集"""
        try:
            logger.info("scout_meteora_execution_started")
            
            # 收集池數據
            pools_raw = await self._fetch_pools()
            
            # 保存到數據庫
            saved_count = await self._save_pools(pools_raw)
            
            # 返回結果
            result = {
                'success': True,
                'pools_collected': len(pools_raw),
                'pools_saved': saved_count,
                'timestamp': datetime.utcnow().isoformat(),
                'data': [asdict(pool) for pool in pools_raw],
                'items_processed': len(pools_raw)
            }
            
            logger.info("scout_meteora_execution_completed", 
                       pools_collected=len(pools_raw),
                       pools_saved=saved_count)
            
            return result
            
        except Exception as e:
            logger.error("scout_meteora_execution_failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat(),
                'items_processed': 0
            }
    
    async def _fetch_pools(self) -> List[PoolRaw]:
        """獲取 Meteora 池數據"""
        pools_raw = []
        
        try:
            # 使用 Meteora API
            async with MeteoraAPI() as meteora_api:
                pools_data = await meteora_api.get_pools(
                    min_tvl=self.min_tvl_usd,
                    limit=self.max_pools
                )
                
                for pool_data in pools_data:
                    try:
                        pool = PoolRaw(
                            id=pool_data['pubkey'],
                            chain='SOL',
                            protocol='Meteora',
                            name=f"{pool_data.get('token_a_mint', '')[:8]}-{pool_data.get('token_b_mint', '')[:8]}",
                            tvl_usd=float(pool_data.get('tvl', 0)),
                            fee24h=float(pool_data.get('volume_24h', 0)),
                            fee_tvl=float(pool_data.get('fee_rate', 0)) / 10000,  # 轉換為百分比
                            created_at=datetime.utcnow()
                        )
                        
                        # 質量過濾
                        if self._is_quality_pool(pool):
                            pools_raw.append(pool)
                            
                    except Exception as e:
                        logger.warning("pool_parsing_failed", pool_id=pool_data.get('pubkey'), error=str(e))
                        
        except Exception as e:
            logger.error("fetch_meteora_pools_failed", error=str(e))
            
        return pools_raw
    
    def _is_quality_pool(self, pool: PoolRaw) -> bool:
        """檢查池質量"""
        # 基本質量檢查
        if pool.tvl_usd < self.min_tvl_usd:
            return False
        
        if pool.fee_tvl <= 0:
            return False
        
        # Solana 特定檢查
        if len(pool.id) < 32:  # Solana pubkey 長度檢查
            return False
        
        return True
    
    async def _save_pools(self, pools: List[PoolRaw]) -> int:
        """保存池數據到數據庫"""
        saved_count = 0
        
        for pool in pools:
            try:
                # 轉換為字典格式
                pool_dict = asdict(pool)
                
                # 保存到數據庫
                await self.save_to_database('pools_raw_sol', pool_dict)
                saved_count += 1
                
            except Exception as e:
                logger.warning("save_pool_failed", pool_id=pool.id, error=str(e))
        
        return saved_count
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            logger.info("scout_meteora_cleanup")
        except Exception as e:
            logger.warning("scout_meteora_cleanup_failed", error=str(e))
    
    # Agno Agent 工具函數
    def _agno_fetch_meteora_pools(self, limit: int = 30) -> str:
        """Agno 工具：獲取 Meteora 池"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                pools = loop.run_until_complete(self._fetch_pools())
                return f"成功獲取 {len(pools)} 個 Meteora 池"
            finally:
                loop.close()
        except Exception as e:
            return f"獲取 Meteora 池失敗: {str(e)}"
    
    def _agno_analyze_sol_metrics(self, pools_data: str) -> str:
        """Agno 工具：分析 SOL 指標"""
        return "Solana 網絡指標：TPS、手續費、流動性正常"
    
    def _agno_compare_chains(self, criteria: str) -> str:
        """Agno 工具：比較多鏈機會"""
        return f"多鏈比較分析：{criteria}"


# 工廠函數
def create_scout_meteora_agent(config: Config, database: Database) -> ScoutMeteoraAgent:
    """創建 Meteora Scout Agent"""
    return ScoutMeteoraAgent(config, database)
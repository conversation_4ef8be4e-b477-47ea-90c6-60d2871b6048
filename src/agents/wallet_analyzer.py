"""
錢包分析器 - 整合多重價格源的詐騙幣過濾和價值評估
提供完整的錢包資產分析、詐騙幣檢測和投資組合價值計算
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
import structlog

try:
    from .base_agent import BaseAgent
    from ..utils.config import Config
    from ..utils.database import Database
    USE_BASE_AGENT = True
except ImportError:
    # 如果無法導入基類，創建一個簡化版本
    class BaseAgent:
        def __init__(self, name: str, config: Any, database: Any = None):
            self.name = name
            self.config = config
            self.database = database
            self.is_initialized = False
        
        async def execute(self) -> Dict[str, Any]:
            return {"status": "ready"}
    
    class Database:
        pass
    
    USE_BASE_AGENT = False

from ..integrations.okx_dex_api import OKXDexAPI
from ..integrations.bscscan_api import BSCScanAPI
from ..integrations.price_aggregator import PriceAggregator

logger = structlog.get_logger(__name__)


class WalletAnalyzer(BaseAgent):
    """錢包分析器 - 集成詐騙幣過濾和價值評估"""
    
    def __init__(self, config: Dict[str, Any]):
        # 根據是否能導入真正的 BaseAgent 來決定初始化方式
        if USE_BASE_AGENT:
            try:
                # 嘗試創建真正的 Config 對象（無參數）
                config_obj = Config()
                database = Database()
                super().__init__("wallet_analyzer", config_obj, database)
            except Exception as e:
                # 如果失敗，使用簡化方式
                logger.warning(f"無法創建 Config 對象，使用簡化模式: {e}")
                super().__init__("wallet_analyzer", config, None)
        else:
            super().__init__("wallet_analyzer", config, None)
        
        # 從傳入的配置字典中提取 API 密鑰
        self.bscscan_api_key = config.get('bscscan_api_key')
        self.okx_api_key = config.get('okx_api_key')
        self.okx_secret_key = config.get('okx_secret_key')
        self.okx_passphrase = config.get('okx_passphrase')
        
        # 重要代幣白名單
        self.important_tokens = {
            "******************************************": {
                "symbol": "USDT",
                "decimals": 18,
                "name": "Tether USD",
                "category": "stablecoin"
            },
            "******************************************": {
                "symbol": "BUSD", 
                "decimals": 18,
                "name": "Binance USD",
                "category": "stablecoin"
            },
            "******************************************": {
                "symbol": "USDC",
                "decimals": 18,
                "name": "USD Coin",
                "category": "stablecoin"
            },
            "******************************************": {
                "symbol": "Unknown",
                "decimals": 18,
                "name": "Unknown Token",
                "category": "other"
            }
        }
        
        # 已知詐騙幣黑名單
        self.known_scam_tokens = {
            "******************************************": "Alexa",
            "******************************************": "MistrAI"
        }
    
    async def initialize(self) -> None:
        """初始化錢包分析器"""
        try:
            logger.info("wallet_analyzer_initializing")
            # 驗證配置
            if not self.bscscan_api_key:
                raise ValueError("BSCScan API 密鑰未配置")
            
            self.is_initialized = True
            logger.info("wallet_analyzer_initialized")
            
        except Exception as e:
            logger.error("wallet_analyzer_initialization_failed", error=str(e))
            raise
    
    async def cleanup(self) -> None:
        """清理資源"""
        try:
            # 清理任何需要清理的資源
            logger.info("wallet_analyzer_cleanup_completed")
        except Exception as e:
            logger.error("wallet_analyzer_cleanup_failed", error=str(e))
    
    async def analyze_wallet(self, wallet_address: str, chain_id: str = "56") -> Dict[str, Any]:
        """完整的錢包分析"""
        start_time = time.time()
        logger.info("wallet_analysis_started", wallet=wallet_address, chain=chain_id)
        
        results = {
            'wallet_address': wallet_address,
            'chain_id': chain_id,
            'analysis_timestamp': start_time,
            'native_balance': None,
            'legitimate_tokens': [],
            'filtered_scam_tokens': [],
            'total_value_usd': 0.0,
            'api_status': {},
            'errors': [],
            'filtering_stats': {
                'total_tokens_found': 0,
                'legitimate_count': 0,
                'scam_filtered_count': 0,
                'whitelist_matches': 0
            },
            'value_breakdown': {
                'native_token': 0.0,
                'stablecoins': 0.0,
                'major_tokens': 0.0,
                'other_tokens': 0.0
            },
            'risk_assessment': {
                'risk_level': '低風險',
                'risk_score': 25,
                'risk_factors': [],
                'recommendations': ['投資組合結構健康']
            },
            'recent_transactions': [],
            'lp_interactions': []
        }
        
        try:
            # 使用多重 API 源
            async with PriceAggregator() as price_aggregator:
                async with BSCScanAPI(api_key=self.bscscan_api_key) as bscscan_api:
                    
                    # 獲取 BNB 餘額和價格
                    native_balance = await self._get_native_balance_with_price(
                        wallet_address, chain_id, price_aggregator, bscscan_api
                    )
                    results['native_balance'] = native_balance
                    results['api_status']['price_aggregator'] = 'working' if native_balance else 'failed'
                    
                    # 分析所有代幣
                    legitimate_tokens, scam_tokens = await self._analyze_all_tokens(
                        wallet_address, chain_id, price_aggregator, bscscan_api
                    )
                    results['legitimate_tokens'] = legitimate_tokens
                    results['filtered_scam_tokens'] = scam_tokens
                    results['api_status']['bscscan'] = 'working'
                    
                    # 獲取交易記錄
                    try:
                        transactions = await bscscan_api.get_transactions(wallet_address, limit=10)
                        results['recent_transactions'] = transactions[:5]  # 限制返回數量
                    except Exception as e:
                        logger.warning("get_transactions_failed", error=str(e))
                    
                    # 計算價值分解
                    native_value = 0.0
                    stablecoin_value = 0.0
                    major_token_value = 0.0
                    other_token_value = 0.0
                    
                    if results['native_balance']:
                        native_value = results['native_balance']['balance_usd']
                    
                    for token in results['legitimate_tokens']:
                        value = token.get('value_usd', 0)
                        category = token.get('category', 'other')
                        
                        if category == 'stablecoin':
                            stablecoin_value += value
                        elif category == 'major':
                            major_token_value += value
                        else:
                            other_token_value += value
                    
                    total_value = native_value + stablecoin_value + major_token_value + other_token_value
                    results['total_value_usd'] = total_value
                    
                    results['value_breakdown'] = {
                        'native_token': native_value,
                        'stablecoins': stablecoin_value,
                        'major_tokens': major_token_value,
                        'other_tokens': other_token_value
                    }
                    
                    # 更新統計信息
                    total_found = len(legitimate_tokens) + len(scam_tokens)
                    whitelist_matches = sum(1 for t in legitimate_tokens 
                                          if t.get('trust_level') == 'trusted_whitelist')
                    
                    results['filtering_stats'] = {
                        'total_tokens_found': total_found,
                        'legitimate_count': len(legitimate_tokens),
                        'scam_filtered_count': len(scam_tokens),
                        'whitelist_matches': whitelist_matches
                    }
                    
                    # 簡單的風險評估
                    risk_assessment = self._assess_risk(results)
                    results['risk_assessment'] = risk_assessment
                    
        except Exception as e:
            error_msg = f"錢包分析失敗: {str(e)}"
            logger.error("wallet_analysis_failed", wallet=wallet_address, error=str(e))
            results['errors'].append(error_msg)
        
        # 更新分析完成時間
        results['analysis_duration'] = time.time() - start_time
        
        logger.info("wallet_analysis_completed", 
                   wallet=wallet_address, 
                   duration=results['analysis_duration'],
                   total_value=results['total_value_usd'])
        
        return results
    
    async def _analyze_all_tokens(
        self, wallet_address: str, chain_id: str,
        price_aggregator: PriceAggregator, bscscan_api: BSCScanAPI
    ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """分析錢包中的所有代幣（包括白名單和其他代幣）"""
        legitimate_tokens = []
        scam_tokens = []
        
        try:
            # 首先獲取錢包的所有代幣交易來發現代幣
            token_txs = await bscscan_api.get_token_transactions(wallet_address, limit=200)
            discovered_tokens = set()
            
            # 從交易中發現代幣地址
            for tx in token_txs:
                contract_address = tx.get('contractAddress')
                if contract_address and contract_address.lower() not in [addr.lower() for addr in self.important_tokens.keys()]:
                    discovered_tokens.add(contract_address.lower())
            
            logger.info(f"discovered_tokens_from_transactions", count=len(discovered_tokens))
            
            # 檢查白名單代幣
            for contract_address, token_info in self.important_tokens.items():
                try:
                    balance_raw = await bscscan_api.get_token_balance(
                        wallet_address, contract_address
                    )
                    if balance_raw > 0:
                        balance_formatted = balance_raw / (10 ** token_info['decimals'])
                        legitimate_tokens.append({
                            'contract_address': contract_address,
                            'symbol': token_info['symbol'],
                            'name': token_info['name'],
                            'balance_formatted': balance_formatted,
                            'trust_level': 'trusted_whitelist',
                            'category': token_info.get('category', 'other'),
                            'price_usd': 0.0,
                            'value_usd': 0.0,
                            'price_source': 'pending'
                        })
                        logger.info(f"whitelist_token_found", symbol=token_info['symbol'], balance=balance_formatted)
                    await asyncio.sleep(0.1)
                except Exception as e:
                    logger.warning("token_check_failed", 
                                 token=token_info['symbol'], error=str(e))
            
            # 檢查從交易中發現的其他代幣
            for contract_address in list(discovered_tokens)[:10]:  # 限制檢查數量避免超時
                try:
                    # 檢查是否是已知詐騙幣
                    if contract_address.lower() in [addr.lower() for addr in self.known_scam_tokens.keys()]:
                        continue
                    
                    balance_raw = await bscscan_api.get_token_balance(
                        wallet_address, contract_address
                    )
                    if balance_raw > 0:
                        # 嘗試獲取代幣信息
                        try:
                            token_info = await price_aggregator.get_token_info_from_contract(
                                contract_address, self.bscscan_api_key
                            )
                            balance_formatted = balance_raw / (10 ** token_info.get('decimals', 18))
                            
                            # 簡單的詐騙幣過濾
                            if self._is_likely_scam(token_info, balance_formatted):
                                scam_tokens.append({
                                    'contract_address': contract_address,
                                    'symbol': token_info.get('symbol', 'UNKNOWN'),
                                    'name': token_info.get('name', 'Unknown Token'),
                                    'balance_formatted': balance_formatted,
                                    'filter_reason': '可疑代幣特徵',
                                    'scam_type': 'suspicious_pattern'
                                })
                                logger.info(f"scam_token_filtered", symbol=token_info.get('symbol'), address=contract_address)
                            else:
                                legitimate_tokens.append({
                                    'contract_address': contract_address,
                                    'symbol': token_info.get('symbol', 'UNKNOWN'),
                                    'name': token_info.get('name', 'Unknown Token'),
                                    'balance_formatted': balance_formatted,
                                    'trust_level': 'discovered',
                                    'category': 'other',
                                    'price_usd': 0.0,
                                    'value_usd': 0.0,
                                    'price_source': 'pending'
                                })
                                logger.info(f"legitimate_token_found", symbol=token_info.get('symbol'), address=contract_address, balance=balance_formatted)
                        except Exception as e:
                            logger.warning("get_token_info_failed", address=contract_address, error=str(e))
                    
                    await asyncio.sleep(0.2)  # 避免 API 限制
                except Exception as e:
                    logger.warning("discovered_token_check_failed", address=contract_address, error=str(e))
        
        except Exception as e:
            logger.error("analyze_all_tokens_failed", error=str(e))
        
        # 使用多重價格源為代幣添加價格信息
        if legitimate_tokens:
            await self._enrich_with_multiple_price_sources(legitimate_tokens, chain_id, price_aggregator)
        
        return legitimate_tokens, scam_tokens
    
    async def _enrich_with_multiple_price_sources(self, tokens: List[Dict[str, Any]], chain_id: str, price_aggregator: PriceAggregator):
        """使用多重價格源為代幣添加價格信息"""
        if not tokens:
            return
        
        logger.info("enriching_tokens_with_multiple_price_sources", count=len(tokens))
        
        for token in tokens:
            try:
                contract_address = token['contract_address']
                logger.info(f"fetching_price_for_token", symbol=token['symbol'], address=contract_address[:10])
                
                # 使用價格聚合器獲取價格
                price_result = await price_aggregator.get_token_price(contract_address, chain_id)
                
                price_usd = price_result.get('price_usd', 0.0)
                price_source = price_result.get('source', 'unavailable')
                balance = token['balance_formatted']
                
                token['price_usd'] = price_usd
                token['value_usd'] = balance * price_usd
                token['price_source'] = price_source
                
                if price_usd > 0:
                    logger.info(f"price_found_for_token", 
                              symbol=token['symbol'], 
                              price=price_usd, 
                              source=price_source,
                              value=token['value_usd'])
                else:
                    logger.warning(f"no_price_found_for_token", 
                                 symbol=token['symbol'], 
                                 source=price_source)
                
                # 避免請求過於頻繁
                await asyncio.sleep(0.3)
                
            except Exception as e:
                logger.error("token_price_enrichment_failed", 
                           symbol=token.get('symbol'), 
                           error=str(e))
                # 設置默認值
                token['price_usd'] = 0.0
                token['value_usd'] = 0.0
                token['price_source'] = 'enrichment_failed'
    
    def _is_likely_scam(self, token_info: Dict[str, Any], balance: float) -> bool:
        """簡單的詐騙幣檢測邏輯"""
        symbol = token_info.get('symbol', '').upper()
        name = token_info.get('name', '').upper()
        
        # 檢查可疑模式
        suspicious_patterns = [
            'ELON', 'DOGE', 'SHIB', 'SAFEMOON', 'BABY', 'MINI',
            'MOON', 'ROCKET', 'SAFE', 'REWARD', 'DIVIDEND'
        ]
        
        for pattern in suspicious_patterns:
            if pattern in symbol or pattern in name:
                return True
        
        # 檢查餘額是否異常大（可能是空投詐騙）
        if balance > 1000000:
            return True
        
        return False
    
    def _assess_risk(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """簡單的風險評估"""
        risk_score = 0
        risk_factors = []
        recommendations = []
        
        total_value = results['total_value_usd']
        scam_count = len(results['filtered_scam_tokens'])
        legitimate_count = len(results['legitimate_tokens'])
        
        # 基於投資組合規模的風險評估
        if total_value < 10:
            risk_score += 10
            risk_factors.append("投資組合價值較小")
        elif total_value > 10000:
            risk_score += 5
            recommendations.append("考慮分散投資到多個錢包")
        
        # 基於代幣多樣性的風險評估
        if legitimate_count == 0:
            risk_score += 5
            recommendations.append("考慮持有一些穩定幣")
        elif legitimate_count > 10:
            risk_score += 10
            risk_factors.append("持有代幣種類過多")
        
        # 基於詐騙幣過濾結果
        if scam_count > 0:
            risk_score += scam_count * 15
            risk_factors.append(f"檢測到 {scam_count} 種可疑代幣")
            recommendations.append("建議立即處理可疑代幣")
        
        # 確定風險等級
        if risk_score >= 50:
            risk_level = "高風險"
        elif risk_score >= 25:
            risk_level = "中風險"
        else:
            risk_level = "低風險"
        
        if not recommendations:
            recommendations.append("投資組合結構健康")
        
        return {
            'risk_level': risk_level,
            'risk_score': min(risk_score, 100),
            'risk_factors': risk_factors,
            'recommendations': recommendations
        }
    
    async def _get_native_balance_with_price(
        self, wallet_address: str, chain_id: str,
        price_aggregator: PriceAggregator, bscscan_api: BSCScanAPI
    ) -> Optional[Dict[str, Any]]:
        """獲取原生代幣餘額和價格"""
        try:
            balance_wei = await bscscan_api.get_balance(wallet_address)
            balance_native = balance_wei / (10 ** 18)
            
            # 使用價格聚合器獲取 BNB 價格
            wbnb_address = "******************************************"
            price_result = await price_aggregator.get_token_price(wbnb_address, chain_id)
            
            native_price = price_result.get('price_usd', 600.0)  # 默認價格
            price_source = price_result.get('source', 'price_aggregator')
            
            balance_usd = balance_native * native_price
            
            return {
                'symbol': 'BNB',
                'balance_native': balance_native,
                'balance_usd': balance_usd,
                'price_usd': native_price,
                'price_source': price_source
            }
        except Exception as e:
            logger.error("native_balance_failed", wallet=wallet_address, error=str(e))
            return None

    async def execute(self) -> Dict[str, Any]:
        """BaseAgent 接口實現"""
        return {"status": "WalletAnalyzer ready for use"}
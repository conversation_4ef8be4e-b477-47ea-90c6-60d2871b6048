"""
錢包分析器 Agent - 基於 Agno Framework 和 Dy-Flow v3 架構
遵循標準 BaseAgent 模式，集成 Agno Framework 的 AI 能力
提供完整的錢包資產分析、詐騙幣檢測和投資組合價值計算
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import structlog

from .base_agent import BaseAgent
from ..utils.config import Config
from ..utils.database import Database
from ..integrations.price_aggregator import PriceAggregator
from ..integrations.bscscan_api import BSCScanAPI

# Agno Framework 導入
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    Agent = None
    OpenAIChat = None

logger = structlog.get_logger(__name__)


@dataclass
class WalletAnalysisResult:
    """錢包分析結果數據類別（Dy-Flow v3 標準）"""
    wallet_address: str
    native_balance: float
    native_value_usd: float
    total_portfolio_value: float
    legitimate_tokens: List[Dict[str, Any]]
    scam_tokens: List[Dict[str, Any]]
    transaction_summary: Dict[str, Any]
    risk_assessment: Dict[str, Any]
    timestamp: datetime
    analysis_metadata: Dict[str, Any]


class WalletAnalyzerAgent(BaseAgent):
    """錢包分析器 Agent - 遵循 Dy-Flow v3 標準架構"""
    
    def __init__(self, config: Config, database: Database):
        super().__init__("wallet_analyzer", config, database)
        
        # Agent 配置
        self.bscscan_api_key = self.agent_config.get(
            'bscscan_api_key', 
            "**********************************"
        )
        self.openai_api_key = self.agent_config.get('openai_api_key')
        
        # 重要代幣白名單
        self.important_tokens = {
            "******************************************": {
                "symbol": "USDT", "decimals": 18, "name": "Tether USD", "category": "stablecoin"
            },
            "******************************************": {
                "symbol": "BUSD", "decimals": 18, "name": "Binance USD", "category": "stablecoin"
            },
            "******************************************": {
                "symbol": "USDC", "decimals": 18, "name": "USD Coin", "category": "stablecoin"
            }
        }
        
        # 已知詐騙幣黑名單
        self.known_scam_tokens = {
            "******************************************": "Alexa",
            "******************************************": "MistrAI"
        }
        
        # Agno Agent 實例
        self.agno_agent: Optional[Agent] = None
        
    async def initialize(self) -> None:
        """初始化 Agent（Dy-Flow v3 標準）"""
        try:
            logger.info("wallet_analyzer_initializing")
            
            # 初始化 Agno Agent（如果可用且有 API key）
            if AGNO_AVAILABLE and self.openai_api_key:
                await self._initialize_agno_agent()
            
            # 驗證必要的服務
            await self._validate_services()
            
            self.is_initialized = True
            logger.info("wallet_analyzer_initialized", agno_enabled=self.agno_agent is not None)
            
        except Exception as e:
            logger.error("wallet_analyzer_initialization_failed", error=str(e))
            raise
    
    async def _initialize_agno_agent(self) -> None:
        """初始化 Agno Agent"""
        try:
            self.agno_agent = Agent(
                name="DyFlow Wallet Analyzer",
                description="專業的 DeFi 錢包分析專家，提供詐騙幣檢測、投資組合評估和風險分析",
                model=OpenAIChat(
                    id="gpt-4o",
                    api_key=self.openai_api_key
                ),
                tools=[
                    self._agno_get_wallet_balance,
                    self._agno_analyze_wallet_tokens,
                    self._agno_get_wallet_transactions
                ],
                instructions=[
                    "你是一個專業的 DeFi 錢包分析專家",
                    "分析錢包時，請詳細檢查所有代幣和餘額",
                    "識別並過濾可疑的詐騙幣",
                    "提供投資組合價值評估和風險分析",
                    "使用表格格式顯示數據",
                    "提供專業的投資建議",
                    "始終包含價格來源信息"
                ],
                show_tool_calls=True,
                markdown=True
            )
            logger.info("agno_agent_initialized")
            
        except Exception as e:
            logger.warning("agno_agent_initialization_failed", error=str(e))
            self.agno_agent = None
    
    async def _validate_services(self) -> None:
        """驗證必要的服務"""
        # 這裡可以添加服務健康檢查
        pass
    
    async def execute(self) -> Dict[str, Any]:
        """執行錢包分析（Dy-Flow v3 標準）"""
        try:
            # 從配置或輸入獲取錢包地址
            wallet_address = self.agent_config.get('target_wallet', 
                "******************************************")
            
            logger.info("wallet_analysis_started", wallet=wallet_address)
            
            # 執行分析
            analysis_result = await self._perform_wallet_analysis(wallet_address)
            
            # 保存結果到數據庫
            await self._save_analysis_result(analysis_result)
            
            # 返回標準格式結果
            return {
                'success': True,
                'wallet_address': wallet_address,
                'analysis_result': analysis_result,
                'timestamp': datetime.utcnow().isoformat(),
                'items_processed': len(analysis_result.legitimate_tokens) + len(analysis_result.scam_tokens)
            }
            
        except Exception as e:
            logger.error("wallet_analysis_failed", error=str(e))
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
    
    async def _perform_wallet_analysis(self, wallet_address: str) -> WalletAnalysisResult:
        """執行詳細的錢包分析"""
        async with BSCScanAPI(api_key=self.bscscan_api_key) as bscscan_api:
            async with PriceAggregator() as price_aggregator:
                
                # 1. 獲取 BNB 餘額
                balance_wei = await bscscan_api.get_balance(wallet_address)
                native_balance = balance_wei / (10 ** 18)
                
                # 2. 獲取 BNB 價格
                wbnb_address = "******************************************"
                price_result = await price_aggregator.get_token_price(wbnb_address, "56")
                native_price = price_result.get('price_usd', 600.0)
                native_value_usd = native_balance * native_price
                
                # 3. 分析代幣
                legitimate_tokens, scam_tokens = await self._analyze_wallet_tokens(
                    wallet_address, bscscan_api, price_aggregator
                )
                
                # 4. 獲取交易摘要
                transaction_summary = await self._get_transaction_summary(
                    wallet_address, bscscan_api
                )
                
                # 5. 計算總投資組合價值
                total_token_value = sum(token.get('value_usd', 0) for token in legitimate_tokens)
                total_portfolio_value = native_value_usd + total_token_value
                
                # 6. 風險評估
                risk_assessment = self._assess_portfolio_risk(
                    legitimate_tokens, scam_tokens, total_portfolio_value
                )
                
                return WalletAnalysisResult(
                    wallet_address=wallet_address,
                    native_balance=native_balance,
                    native_value_usd=native_value_usd,
                    total_portfolio_value=total_portfolio_value,
                    legitimate_tokens=legitimate_tokens,
                    scam_tokens=scam_tokens,
                    transaction_summary=transaction_summary,
                    risk_assessment=risk_assessment,
                    timestamp=datetime.utcnow(),
                    analysis_metadata={
                        'price_sources': price_result.get('source', 'unknown'),
                        'tokens_analyzed': len(legitimate_tokens) + len(scam_tokens),
                        'agno_enabled': self.agno_agent is not None
                    }
                )
    
    async def _analyze_wallet_tokens(self, wallet_address: str, 
                                   bscscan_api: BSCScanAPI, 
                                   price_aggregator: PriceAggregator) -> tuple[List[Dict], List[Dict]]:
        """分析錢包中的所有代幣"""
        legitimate_tokens = []
        scam_tokens = []
        
        # 獲取代幣交易記錄來發現代幣
        token_txs = await bscscan_api.get_token_transactions(wallet_address, limit=200)
        discovered_tokens = set()
        
        # 從交易中發現代幣地址
        for tx in token_txs:
            contract_address = tx.get('contractAddress')
            if contract_address and contract_address.lower() not in [addr.lower() for addr in self.important_tokens.keys()]:
                discovered_tokens.add(contract_address.lower())
        
        logger.info("discovered_tokens_from_transactions", count=len(discovered_tokens))
        
        # 檢查白名單代幣
        for contract_address, token_info in self.important_tokens.items():
            try:
                balance_raw = await bscscan_api.get_token_balance(wallet_address, contract_address)
                if balance_raw > 0:
                    balance_formatted = balance_raw / (10 ** token_info['decimals'])
                    
                    # 獲取價格
                    price_result = await price_aggregator.get_token_price(contract_address, "56")
                    price_usd = price_result.get('price_usd', 0.0)
                    
                    legitimate_tokens.append({
                        'contract_address': contract_address,
                        'symbol': token_info['symbol'],
                        'name': token_info['name'],
                        'balance_formatted': balance_formatted,
                        'trust_level': 'trusted_whitelist',
                        'category': token_info.get('category', 'other'),
                        'price_usd': price_usd,
                        'value_usd': balance_formatted * price_usd,
                        'price_source': price_result.get('source', 'unavailable')
                    })
                    
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.warning("token_check_failed", token=token_info['symbol'], error=str(e))
        
        # 檢查發現的其他代幣（限制數量避免超時）
        for contract_address in list(discovered_tokens)[:10]:
            try:
                if contract_address.lower() in [addr.lower() for addr in self.known_scam_tokens.keys()]:
                    continue
                
                balance_raw = await bscscan_api.get_token_balance(wallet_address, contract_address)
                if balance_raw > 0:
                    # 獲取代幣信息
                    token_info = await price_aggregator.get_token_info_from_contract(
                        contract_address, self.bscscan_api_key
                    )
                    balance_formatted = balance_raw / (10 ** token_info.get('decimals', 18))
                    
                    # 詐騙幣檢測
                    if self._is_likely_scam(token_info, balance_formatted):
                        scam_tokens.append({
                            'contract_address': contract_address,
                            'symbol': token_info.get('symbol', 'UNKNOWN'),
                            'name': token_info.get('name', 'Unknown Token'),
                            'balance_formatted': balance_formatted,
                            'filter_reason': '可疑代幣特徵',
                            'scam_type': 'suspicious_pattern'
                        })
                    else:
                        # 獲取價格
                        price_result = await price_aggregator.get_token_price(contract_address, "56")
                        price_usd = price_result.get('price_usd', 0.0)
                        
                        legitimate_tokens.append({
                            'contract_address': contract_address,
                            'symbol': token_info.get('symbol', 'UNKNOWN'),
                            'name': token_info.get('name', 'Unknown Token'),
                            'balance_formatted': balance_formatted,
                            'trust_level': 'discovered',
                            'category': 'other',
                            'price_usd': price_usd,
                            'value_usd': balance_formatted * price_usd,
                            'price_source': price_result.get('source', 'unavailable')
                        })
                
                await asyncio.sleep(0.2)
            except Exception as e:
                logger.warning("discovered_token_check_failed", address=contract_address, error=str(e))
        
        return legitimate_tokens, scam_tokens
    
    async def _get_transaction_summary(self, wallet_address: str, 
                                     bscscan_api: BSCScanAPI) -> Dict[str, Any]:
        """獲取交易摘要"""
        try:
            transactions = await bscscan_api.get_transactions(wallet_address, limit=50)
            token_txs = await bscscan_api.get_token_transactions(wallet_address, limit=50)
            
            return {
                'total_normal_txs': len(transactions),
                'total_token_txs': len(token_txs),
                'recent_activity': len([tx for tx in transactions if 
                    (datetime.utcnow() - datetime.fromtimestamp(int(tx.get('timeStamp', 0)))).days <= 7])
            }
        except Exception as e:
            logger.warning("transaction_summary_failed", error=str(e))
            return {'error': str(e)}
    
    def _assess_portfolio_risk(self, legitimate_tokens: List[Dict], 
                             scam_tokens: List[Dict], 
                             total_value: float) -> Dict[str, Any]:
        """評估投資組合風險"""
        risk_factors = []
        risk_score = 0
        
        # 詐騙幣風險
        if scam_tokens:
            risk_factors.append(f"檢測到 {len(scam_tokens)} 個可疑代幣")
            risk_score += len(scam_tokens) * 10
        
        # 集中度風險
        if legitimate_tokens:
            max_token_value = max(token.get('value_usd', 0) for token in legitimate_tokens)
            concentration_ratio = max_token_value / total_value if total_value > 0 else 0
            if concentration_ratio > 0.8:
                risk_factors.append("投資組合過度集中")
                risk_score += 20
        
        # 未知代幣風險
        unknown_tokens = [t for t in legitimate_tokens if t.get('trust_level') == 'discovered']
        if unknown_tokens:
            risk_factors.append(f"{len(unknown_tokens)} 個未驗證代幣")
            risk_score += len(unknown_tokens) * 5
        
        # 確定風險等級
        if risk_score >= 50:
            risk_level = "HIGH"
        elif risk_score >= 20:
            risk_level = "MEDIUM"
        else:
            risk_level = "LOW"
        
        return {
            'risk_level': risk_level,
            'risk_score': min(risk_score, 100),
            'risk_factors': risk_factors,
            'recommendations': self._get_risk_recommendations(risk_level, risk_factors)
        }
    
    def _get_risk_recommendations(self, risk_level: str, risk_factors: List[str]) -> List[str]:
        """獲取風險建議"""
        recommendations = []
        
        if risk_level == "HIGH":
            recommendations.append("建議立即檢查並移除可疑代幣")
            recommendations.append("考慮分散投資降低集中度風險")
        elif risk_level == "MEDIUM":
            recommendations.append("定期監控投資組合變化")
            recommendations.append("驗證未知代幣的合法性")
        else:
            recommendations.append("保持當前風險管理策略")
        
        return recommendations
    
    def _is_likely_scam(self, token_info: Dict[str, Any], balance: float) -> bool:
        """簡單的詐騙幣檢測邏輯"""
        symbol = token_info.get('symbol', '').upper()
        name = token_info.get('name', '').upper()
        
        # 檢查可疑模式
        suspicious_patterns = [
            'ELON', 'DOGE', 'SHIB', 'SAFEMOON', 'BABY', 'MINI',
            'MOON', 'ROCKET', 'SAFE', 'REWARD', 'DIVIDEND'
        ]
        
        for pattern in suspicious_patterns:
            if pattern in symbol or pattern in name:
                return True
        
        # 檢查餘額是否異常大（可能是空投詐騙）
        if balance > 1000000:
            return True
        
        return False
    
    async def _save_analysis_result(self, result: WalletAnalysisResult) -> None:
        """保存分析結果到數據庫（Dy-Flow v3 標準）"""
        try:
            # 轉換為字典格式
            result_dict = {
                'wallet_address': result.wallet_address,
                'native_balance': result.native_balance,
                'native_value_usd': result.native_value_usd,
                'total_portfolio_value': result.total_portfolio_value,
                'legitimate_tokens_count': len(result.legitimate_tokens),
                'scam_tokens_count': len(result.scam_tokens),
                'risk_level': result.risk_assessment.get('risk_level'),
                'risk_score': result.risk_assessment.get('risk_score'),
                'timestamp': result.timestamp,
                'metadata': result.analysis_metadata
            }
            
            # 保存到數據庫
            await self.save_to_database('wallet_analysis_results', result_dict)
            
        except Exception as e:
            logger.warning("failed_to_save_analysis_result", error=str(e))
    
    async def cleanup(self) -> None:
        """清理資源（Dy-Flow v3 標準）"""
        try:
            logger.info("wallet_analyzer_cleanup")
            # 這裡可以添加清理邏輯
        except Exception as e:
            logger.warning("wallet_analyzer_cleanup_failed", error=str(e))
    
    # Agno Agent 工具函數
    def _agno_get_wallet_balance(self, wallet_address: str) -> str:
        """Agno Agent 工具：獲取錢包餘額"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async def _get_balance():
                    async with BSCScanAPI(api_key=self.bscscan_api_key) as bscscan_api:
                        balance_wei = await bscscan_api.get_balance(wallet_address)
                        return balance_wei / (10 ** 18)
                
                balance = loop.run_until_complete(_get_balance())
                return f"錢包 {wallet_address} 的 BNB 餘額: {balance:.4f} BNB"
            finally:
                loop.close()
        except Exception as e:
            return f"獲取餘額失敗: {str(e)}"
    
    def _agno_analyze_wallet_tokens(self, wallet_address: str) -> str:
        """Agno Agent 工具：分析錢包代幣"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async def _analyze():
                    async with BSCScanAPI(api_key=self.bscscan_api_key) as bscscan_api:
                        async with PriceAggregator() as price_aggregator:
                            legitimate, scam = await self._analyze_wallet_tokens(
                                wallet_address, bscscan_api, price_aggregator
                            )
                            return legitimate, scam
                
                legitimate_tokens, scam_tokens = loop.run_until_complete(_analyze())
                return f"發現 {len(legitimate_tokens)} 個合法代幣，{len(scam_tokens)} 個可疑代幣"
            finally:
                loop.close()
        except Exception as e:
            return f"代幣分析失敗: {str(e)}"
    
    def _agno_get_wallet_transactions(self, wallet_address: str) -> str:
        """Agno Agent 工具：獲取交易記錄"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                async def _get_txs():
                    async with BSCScanAPI(api_key=self.bscscan_api_key) as bscscan_api:
                        return await self._get_transaction_summary(wallet_address, bscscan_api)
                
                summary = loop.run_until_complete(_get_txs())
                return f"交易摘要: {summary}"
            finally:
                loop.close()
        except Exception as e:
            return f"交易記錄獲取失敗: {str(e)}"
    
    async def analyze_wallet_with_agno(self, wallet_address: str) -> str:
        """使用 Agno Agent 進行智能分析"""
        if not self.agno_agent:
            return "Agno Agent 未初始化"
        
        try:
            prompt = f"""
            請對錢包地址 {wallet_address} 進行完整的 DeFi 分析：

            1. 獲取錢包的 BNB 餘額
            2. 分析錢包中的代幣
            3. 獲取交易記錄摘要
            4. 提供投資組合評估和風險分析
            5. 給出專業建議

            請使用表格格式顯示結果，並提供詳細的分析報告。
            """
            
            response = self.agno_agent.run(prompt)
            return response.content
            
        except Exception as e:
            logger.error("agno_analysis_failed", error=str(e))
            return f"Agno 分析失敗: {str(e)}"


# 便利函數
def create_wallet_analyzer_agent(config: Config, database: Database) -> WalletAnalyzerAgent:
    """創建錢包分析器 Agent 實例"""
    return WalletAnalyzerAgent(config, database)

# Backwards compatibility for tests
DyFlowWalletAnalyzer = WalletAnalyzerAgent



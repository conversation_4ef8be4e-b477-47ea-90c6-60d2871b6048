"""
AGNO DAG 调度器
基于 AGNO 配置文件协调七个核心 Agent 的执行
实现依赖管理、并发控制和错误处理
"""

import asyncio
import yaml
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import structlog
from croniter import croniter

from ..utils.config import Config
from ..utils.database import Database
from ..utils.helpers import get_utc_timestamp
from ..utils.exceptions import DyFlowException
from ..agents import (
    ScoutBSC, ScoutMeteora, ScorerAgent, PlannerAgent, RiskSentinelAgent,
    ExecutorAgent, EarningsAuditorAgent, CLIReporterAgent
)

logger = structlog.get_logger(__name__)


class AgentStatus(Enum):
    """Agent 状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    WAITING = "waiting"


@dataclass
class AgentExecution:
    """Agent 执行状态"""
    agent_name: str
    status: AgentStatus
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    retry_count: int = 0


class AGNOScheduler:
    """AGNO DAG 调度器"""
    
    def __init__(self, config: Config, database: Database, agno_config_path: str = "docs/agno_flow.yaml"):
        self.config = config
        self.database = database
        self.agno_config_path = agno_config_path
        
        # AGNO 配置
        self.agno_config: Dict[str, Any] = {}
        self.agent_configs: Dict[str, Any] = {}
        self.workflow_config: Dict[str, Any] = {}
        
        # Agent 实例
        self.agents: Dict[str, Any] = {}
        self.agent_schedules: Dict[str, croniter] = {}
        
        # 执行状态
        self.current_executions: Dict[str, AgentExecution] = {}
        self.execution_history: List[AgentExecution] = []
        self.is_running = False
        
        # 调度配置
        self.max_concurrent_agents = 3
        self.retry_limit = 3
        self.retry_delay = 5
        
        # 统计信息
        self.stats = {
            'total_cycles': 0,
            'successful_cycles': 0,
            'failed_cycles': 0,
            'agents_executed': 0,
            'avg_cycle_time': 0.0
        }
    
    async def initialize(self) -> None:
        """初始化调度器"""
        try:
            logger.info("agno_scheduler_initializing")
            
            # 加载 AGNO 配置
            await self._load_agno_config()
            
            # 验证配置
            self._validate_agno_config()
            
            # 创建 Agent 实例
            await self._create_agent_instances()
            
            # 初始化 Agent 调度表
            self._setup_agent_schedules()
            
            # 初始化所有 Agent
            await self._initialize_agents()
            
            logger.info("agno_scheduler_initialized", 
                       agents_count=len(self.agents))
            
        except Exception as e:
            logger.error("agno_scheduler_initialization_failed", error=str(e))
            raise DyFlowException(f"AGNO 调度器初始化失败: {e}")
    
    async def _load_agno_config(self) -> None:
        """加载 AGNO 配置文件"""
        try:
            with open(self.agno_config_path, 'r', encoding='utf-8') as file:
                self.agno_config = yaml.safe_load(file)
            
            # 提取各部分配置
            self.agent_configs = self.agno_config.get('agents', {})
            self.workflow_config = self.agno_config.get('workflow', {})
            
            # 更新全局配置
            global_config = self.agno_config.get('global_config', {})
            scheduler_config = global_config.get('scheduler', {})
            
            self.max_concurrent_agents = scheduler_config.get('max_concurrent_agents', 3)
            self.retry_limit = scheduler_config.get('retry_limit', 3)
            self.retry_delay = scheduler_config.get('retry_delay', 5)
            
            logger.info("agno_config_loaded", 
                       agents_count=len(self.agent_configs),
                       workflow_layers=len(self.workflow_config))
            
        except Exception as e:
            logger.error("agno_config_load_failed", error=str(e))
            raise DyFlowException(f"加载 AGNO 配置失败: {e}")
    
    def _validate_agno_config(self) -> None:
        """验证 AGNO 配置"""
        try:
            required_agents = [
                'scout_bsc', 'scout_meteora', 'scorer', 'planner', 'risk_sentinel',
                'executor', 'auditor', 'cli_reporter'
            ]
            
            # 检查必需的 Agent
            for agent_name in required_agents:
                if agent_name not in self.agent_configs:
                    raise DyFlowException(f"缺少必需的 Agent 配置: {agent_name}")
            
            # 验证 cron 表达式
            for agent_name, agent_config in self.agent_configs.items():
                schedule_config = agent_config.get('schedule', {})
                cron_expr = schedule_config.get('cron')
                
                if cron_expr:
                    try:
                        croniter(cron_expr)
                    except Exception as e:
                        raise DyFlowException(f"Agent {agent_name} 的 cron 表达式无效: {cron_expr}")
            
            # 验证工作流依赖
            self._validate_workflow_dependencies()
            
            logger.info("agno_config_validation_passed")
            
        except Exception as e:
            logger.error("agno_config_validation_failed", error=str(e))
            raise
    
    def _validate_workflow_dependencies(self) -> None:
        """验证工作流依赖关系"""
        try:
            # 检查依赖循环
            agent_deps = {}
            for agent_name, agent_config in self.agent_configs.items():
                deps = agent_config.get('dependencies', [])
                agent_deps[agent_name] = deps
            
            # 简单的循环检测
            def has_cycle(node: str, visited: Set[str], rec_stack: Set[str]) -> bool:
                visited.add(node)
                rec_stack.add(node)
                
                for dep in agent_deps.get(node, []):
                    if dep not in visited:
                        if has_cycle(dep, visited, rec_stack):
                            return True
                    elif dep in rec_stack:
                        return True
                
                rec_stack.remove(node)
                return False
            
            visited = set()
            for agent in agent_deps:
                if agent not in visited:
                    if has_cycle(agent, visited, set()):
                        raise DyFlowException(f"检测到依赖循环: {agent}")
            
        except Exception as e:
            logger.error("workflow_dependency_validation_failed", error=str(e))
            raise
    
    async def _create_agent_instances(self) -> None:
        """创建 Agent 实例"""
        try:
            agent_classes = {
                'scout_bsc': ScoutBSC,
                'scout_meteora': ScoutMeteora,
                'scorer': ScorerAgent,
                'planner': PlannerAgent,
                'risk_sentinel': RiskSentinelAgent,
                'executor': ExecutorAgent,
                'auditor': EarningsAuditorAgent,
                'cli_reporter': CLIReporterAgent
            }
            
            for agent_name, agent_class in agent_classes.items():
                if agent_name in self.agent_configs:
                    # 合并配置
                    agent_config = self.agent_configs[agent_name].get('config', {})
                    
                    # 创建 Agent 实例 - 使用正确的构造函数签名
                    agent_instance = agent_class(agent_name, self.config, self.database)
                    
                    # 更新 Agent 配置
                    if hasattr(agent_instance, 'agent_config'):
                        agent_instance.agent_config.update(agent_config)
                    
                    self.agents[agent_name] = agent_instance
                    
                    logger.debug("agent_instance_created", agent_name=agent_name)
            
            logger.info("agent_instances_created", count=len(self.agents))
            
        except Exception as e:
            logger.error("agent_instance_creation_failed", error=str(e))
            raise
    
    def _setup_agent_schedules(self) -> None:
        """设置 Agent 调度表"""
        try:
            for agent_name, agent_config in self.agent_configs.items():
                schedule_config = agent_config.get('schedule', {})
                cron_expr = schedule_config.get('cron')
                
                if cron_expr:
                    # 创建 croniter 实例
                    cron_iter = croniter(cron_expr, get_utc_timestamp())
                    self.agent_schedules[agent_name] = cron_iter
                    
                    logger.debug("agent_schedule_setup", 
                               agent_name=agent_name, 
                               cron=cron_expr)
            
            logger.info("agent_schedules_setup", count=len(self.agent_schedules))
            
        except Exception as e:
            logger.error("agent_schedule_setup_failed", error=str(e))
            raise
    
    async def _initialize_agents(self) -> None:
        """初始化所有 Agent"""
        try:
            initialization_tasks = []
            
            for agent_name, agent in self.agents.items():
                task = self._initialize_single_agent(agent_name, agent)
                initialization_tasks.append(task)
            
            # 并发初始化
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            # 检查初始化结果
            failed_agents = []
            for i, result in enumerate(results):
                agent_name = list(self.agents.keys())[i]
                if isinstance(result, Exception):
                    failed_agents.append(agent_name)
                    logger.error("agent_initialization_failed", 
                               agent_name=agent_name, error=str(result))
            
            if failed_agents:
                raise DyFlowException(f"以下 Agent 初始化失败: {failed_agents}")
            
            logger.info("all_agents_initialized", count=len(self.agents))
            
        except Exception as e:
            logger.error("agents_initialization_failed", error=str(e))
            raise
    
    async def _initialize_single_agent(self, agent_name: str, agent: Any) -> None:
        """初始化单个 Agent"""
        try:
            await agent.initialize()
            logger.info("agent_initialized", agent_name=agent_name)
        except Exception as e:
            logger.error("single_agent_initialization_failed", 
                        agent_name=agent_name, error=str(e))
            raise
    
    async def start(self) -> None:
        """启动调度器"""
        try:
            logger.info("agno_scheduler_starting")
            self.is_running = True
            
            # 启动主调度循环
            await self._run_scheduling_loop()
            
        except Exception as e:
            logger.error("agno_scheduler_start_failed", error=str(e))
            raise
        finally:
            self.is_running = False
    
    async def stop(self) -> None:
        """停止调度器"""
        try:
            logger.info("agno_scheduler_stopping")
            self.is_running = False
            
            # 等待当前执行完成
            await self._wait_for_current_executions()
            
            # 清理 Agent
            await self._cleanup_agents()
            
            logger.info("agno_scheduler_stopped")
            
        except Exception as e:
            logger.error("agno_scheduler_stop_failed", error=str(e))
    
    async def _run_scheduling_loop(self) -> None:
        """运行主调度循环"""
        try:
            while self.is_running:
                cycle_start_time = get_utc_timestamp()
                
                try:
                    # 检查需要执行的 Agent
                    agents_to_run = self._get_agents_to_run()
                    
                    if agents_to_run:
                        # 根据依赖关系排序
                        execution_order = self._resolve_execution_order(agents_to_run)
                        
                        # 执行 Agent
                        await self._execute_agents(execution_order)
                    
                    # 清理完成的执行
                    self._cleanup_completed_executions()
                    
                    # 更新统计信息
                    cycle_time = (get_utc_timestamp() - cycle_start_time).total_seconds()
                    self._update_cycle_stats(cycle_time, success=True)
                    
                except Exception as e:
                    logger.error("scheduling_cycle_failed", error=str(e))
                    self._update_cycle_stats(0, success=False)
                
                # 等待下一个调度周期
                await asyncio.sleep(1)  # 每秒检查一次
                
        except Exception as e:
            logger.error("scheduling_loop_failed", error=str(e))
            raise
    
    def _get_agents_to_run(self) -> List[str]:
        """获取需要执行的 Agent"""
        try:
            agents_to_run = []
            current_time = get_utc_timestamp()
            
            for agent_name, cron_iter in self.agent_schedules.items():
                # 检查是否到了执行时间
                next_run_time = cron_iter.get_next(datetime)
                
                # 如果下次执行时间已经过去，说明应该执行
                if next_run_time <= current_time:
                    # 检查 Agent 是否正在运行
                    if agent_name not in self.current_executions:
                        agents_to_run.append(agent_name)
                        
                        # 更新到下一个执行时间
                        cron_iter.get_next(datetime)
            
            return agents_to_run
            
        except Exception as e:
            logger.error("get_agents_to_run_failed", error=str(e))
            return []
    
    def _resolve_execution_order(self, agent_names: List[str]) -> List[List[str]]:
        """解析执行顺序（按依赖关系分层）"""
        try:
            # 构建依赖图
            dependencies = {}
            for agent_name in agent_names:
                agent_config = self.agent_configs.get(agent_name, {})
                deps = agent_config.get('dependencies', [])
                dependencies[agent_name] = [dep for dep in deps if dep in agent_names]
            
            # 拓扑排序分层
            execution_layers = []
            remaining_agents = set(agent_names)
            
            while remaining_agents:
                # 找到没有依赖的 Agent（或依赖已满足）
                ready_agents = []
                for agent in remaining_agents:
                    agent_deps = dependencies[agent]
                    if not agent_deps or all(dep not in remaining_agents for dep in agent_deps):
                        ready_agents.append(agent)
                
                if not ready_agents:
                    # 如果没有可执行的 Agent，可能存在循环依赖
                    logger.warning("no_ready_agents_found", remaining=list(remaining_agents))
                    ready_agents = list(remaining_agents)  # 强制执行
                
                execution_layers.append(ready_agents)
                remaining_agents -= set(ready_agents)
            
            return execution_layers
            
        except Exception as e:
            logger.error("execution_order_resolution_failed", error=str(e))
            # 返回简单的单层执行
            return [agent_names]
    
    async def _execute_agents(self, execution_layers: List[List[str]]) -> None:
        """按层执行 Agent"""
        try:
            for layer_index, agent_layer in enumerate(execution_layers):
                logger.info("executing_agent_layer", 
                           layer=layer_index, 
                           agents=agent_layer)
                
                # 检查并发限制
                if len(agent_layer) > self.max_concurrent_agents:
                    # 分批执行
                    for i in range(0, len(agent_layer), self.max_concurrent_agents):
                        batch = agent_layer[i:i + self.max_concurrent_agents]
                        await self._execute_agent_batch(batch)
                else:
                    # 并发执行整层
                    await self._execute_agent_batch(agent_layer)
                
                # 等待当前层完成
                await self._wait_for_layer_completion(agent_layer)
                
        except Exception as e:
            logger.error("agent_execution_failed", error=str(e))
            raise
    
    async def _execute_agent_batch(self, agent_names: List[str]) -> None:
        """执行 Agent 批次"""
        try:
            tasks = []
            
            for agent_name in agent_names:
                if agent_name in self.agents:
                    task = self._execute_single_agent(agent_name)
                    tasks.append(task)
            
            # 并发执行
            if tasks:
                await asyncio.gather(*tasks, return_exceptions=True)
                
        except Exception as e:
            logger.error("agent_batch_execution_failed", error=str(e))
    
    async def _execute_single_agent(self, agent_name: str) -> None:
        """执行单个 Agent"""
        try:
            agent = self.agents[agent_name]
            
            # 创建执行记录
            execution = AgentExecution(
                agent_name=agent_name,
                status=AgentStatus.RUNNING,
                start_time=get_utc_timestamp()
            )
            
            self.current_executions[agent_name] = execution
            
            logger.info("agent_execution_started", agent_name=agent_name)
            
            try:
                # 执行 Agent
                result = await agent.run()
                
                # 更新执行结果
                execution.status = AgentStatus.COMPLETED
                execution.end_time = get_utc_timestamp()
                execution.result = result
                
                self.stats['agents_executed'] += 1
                
                logger.info("agent_execution_completed", 
                           agent_name=agent_name,
                           duration=(execution.end_time - execution.start_time).total_seconds())
                
            except Exception as e:
                # 处理执行失败
                execution.status = AgentStatus.FAILED
                execution.end_time = get_utc_timestamp()
                execution.error = str(e)
                
                logger.error("agent_execution_failed", 
                           agent_name=agent_name, 
                           error=str(e))
                
                # 考虑重试
                if execution.retry_count < self.retry_limit:
                    await self._schedule_agent_retry(agent_name)
            
        except Exception as e:
            logger.error("single_agent_execution_failed", 
                        agent_name=agent_name, error=str(e))
    
    async def _schedule_agent_retry(self, agent_name: str) -> None:
        """调度 Agent 重试"""
        try:
            execution = self.current_executions.get(agent_name)
            if not execution:
                return
            
            execution.retry_count += 1
            execution.status = AgentStatus.WAITING
            
            logger.info("agent_retry_scheduled", 
                       agent_name=agent_name, 
                       retry_count=execution.retry_count)
            
            # 延迟后重试
            await asyncio.sleep(self.retry_delay)
            
            # 重新执行
            await self._execute_single_agent(agent_name)
            
        except Exception as e:
            logger.error("agent_retry_scheduling_failed", 
                        agent_name=agent_name, error=str(e))
    
    async def _wait_for_layer_completion(self, agent_names: List[str]) -> None:
        """等待层完成"""
        try:
            max_wait_time = 300  # 最大等待5分钟
            wait_start = get_utc_timestamp()
            
            while True:
                # 检查所有 Agent 是否完成
                all_completed = True
                for agent_name in agent_names:
                    execution = self.current_executions.get(agent_name)
                    if execution and execution.status == AgentStatus.RUNNING:
                        all_completed = False
                        break
                
                if all_completed:
                    break
                
                # 检查超时
                if (get_utc_timestamp() - wait_start).total_seconds() > max_wait_time:
                    logger.warning("layer_completion_timeout", 
                                 agents=agent_names,
                                 timeout=max_wait_time)
                    break
                
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error("layer_completion_wait_failed", error=str(e))
    
    async def _wait_for_current_executions(self) -> None:
        """等待当前执行完成"""
        try:
            max_wait_time = 60  # 最大等待1分钟
            wait_start = get_utc_timestamp()
            
            while self.current_executions:
                if (get_utc_timestamp() - wait_start).total_seconds() > max_wait_time:
                    logger.warning("execution_completion_timeout")
                    break
                
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error("execution_wait_failed", error=str(e))
    
    def _cleanup_completed_executions(self) -> None:
        """清理已完成的执行"""
        try:
            completed_agents = []
            
            for agent_name, execution in self.current_executions.items():
                if execution.status in [AgentStatus.COMPLETED, AgentStatus.FAILED]:
                    completed_agents.append(agent_name)
                    
                    # 添加到历史记录
                    self.execution_history.append(execution)
            
            # 移除已完成的执行
            for agent_name in completed_agents:
                del self.current_executions[agent_name]
            
            # 限制历史记录大小
            if len(self.execution_history) > 1000:
                self.execution_history = self.execution_history[-500:]
                
        except Exception as e:
            logger.error("execution_cleanup_failed", error=str(e))
    
    def _update_cycle_stats(self, cycle_time: float, success: bool) -> None:
        """更新周期统计"""
        try:
            self.stats['total_cycles'] += 1
            
            if success:
                self.stats['successful_cycles'] += 1
            else:
                self.stats['failed_cycles'] += 1
            
            # 更新平均周期时间
            if self.stats['total_cycles'] > 0:
                current_avg = self.stats['avg_cycle_time']
                new_avg = (current_avg * (self.stats['total_cycles'] - 1) + cycle_time) / self.stats['total_cycles']
                self.stats['avg_cycle_time'] = new_avg
                
        except Exception as e:
            logger.error("cycle_stats_update_failed", error=str(e))
    
    async def _cleanup_agents(self) -> None:
        """清理所有 Agent"""
        try:
            cleanup_tasks = []
            
            for agent_name, agent in self.agents.items():
                if hasattr(agent, 'cleanup'):
                    task = agent.cleanup()
                    cleanup_tasks.append(task)
            
            if cleanup_tasks:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            logger.info("agents_cleanup_completed")
            
        except Exception as e:
            logger.error("agents_cleanup_failed", error=str(e))
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        try:
            return {
                'is_running': self.is_running,
                'agents_count': len(self.agents),
                'current_executions': len(self.current_executions),
                'stats': self.stats,
                'agent_statuses': {
                    name: execution.status.value 
                    for name, execution in self.current_executions.items()
                }
            }
            
        except Exception as e:
            logger.error("status_retrieval_failed", error=str(e))
            return {'error': str(e)}
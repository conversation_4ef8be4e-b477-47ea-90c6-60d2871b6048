import asyncio
from typing import Dict, Any, List


class AGNOScheduler:
    """Very small task scheduler used in tests."""

    def __init__(self, config: Any, database: Any):
        self.config = config
        self.database = database
        self.agents: Dict[str, Any] = {}

    async def initialize(self):
        """Initialize scheduler state."""
        # Placeholder for future expansion
        return True

    def register_agent(self, name: str, agent: Any, deps: List[str] | None = None):
        """Register an agent with optional dependencies."""
        self.agents[name] = {"instance": agent, "deps": deps or []}

    def _resolve_execution_order(self, agent_names: List[str]) -> List[List[str]]:
        """Return execution layers in the order agents should run."""
        if not agent_names:
            return []
        return [agent_names]

    async def _execute_agent_batch(self, agent_names: List[str]):
        """Execute a list of agents sequentially."""
        for name in agent_names:
            agent = self.agents.get(name, {}).get("instance")
            if agent is None:
                continue
            run = getattr(agent, "run", None)
            if asyncio.iscoroutinefunction(run):
                await run()
            elif callable(run):
                run()

"""
Dy-Flow v3 Supabase 數據庫包裝器
提供統一的數據庫操作接口和 v3 表結構支援
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Union
from dataclasses import asdict
import asyncpg
from supabase import create_client, Client

from ..utils.models_v3 import PoolRaw, PoolScore, Plan, AgentResult
from ..utils.config import get_config

logger = logging.getLogger(__name__)


class SupabaseManager:
    """Supabase 數據庫管理器"""
    
    def __init__(self):
        self.config = get_config()
        self.client: Optional[Client] = None
        self.pool: Optional[asyncpg.Pool] = None
        
    async def initialize(self):
        """初始化數據庫連接"""
        try:
            # 初始化 Supabase 客戶端
            supabase_url = self.config.get('database', {}).get('url')
            service_key = self.config.get('database', {}).get('service_key')
            
            if not supabase_url or not service_key:
                raise ValueError("缺少 Supabase URL 或 Service Key")
                
            self.client = create_client(supabase_url, service_key)
            
            # 初始化 asyncpg 連接池用於批次操作
            database_url = supabase_url.replace('https://', 'postgresql://postgres:')
            database_url += f"@{supabase_url.split('//')[1]}/postgres"
            
            self.pool = await asyncpg.create_pool(
                database_url,
                min_size=2,
                max_size=self.config.get('database', {}).get('connection_pool_size', 10),
                command_timeout=self.config.get('database', {}).get('query_timeout', 30)
            )
            
            logger.info("Supabase 數據庫連接初始化成功")
            
        except Exception as e:
            logger.error(f"數據庫初始化失敗: {e}")
            raise
    
    async def close(self):
        """關閉數據庫連接"""
        if self.pool:
            await self.pool.close()
            logger.info("數據庫連接已關閉")

    # === Pool 相關操作 ===
    
    async def insert_pools_raw(self, pools: List[PoolRaw]) -> bool:
        """批次插入原始池子數據"""
        try:
            data = []
            for pool in pools:
                pool_dict = asdict(pool)
                # 轉換 datetime 為 ISO 字符串
                if isinstance(pool_dict.get('created_at'), datetime):
                    pool_dict['created_at'] = pool_dict['created_at'].isoformat()
                data.append(pool_dict)
            
            result = self.client.table('pools_raw').upsert(data).execute()
            
            if result.data:
                logger.info(f"成功插入 {len(result.data)} 條原始池子數據")
                return True
            else:
                logger.warning("插入池子數據失敗，無返回數據")
                return False
                
        except Exception as e:
            logger.error(f"插入原始池子數據失敗: {e}")
            return False
    
    async def insert_pools_scored(self, pools: List[PoolScore]) -> bool:
        """批次插入評分池子數據"""
        try:
            data = []
            for pool in pools:
                pool_dict = asdict(pool)
                # 轉換 datetime 為 ISO 字符串
                if isinstance(pool_dict.get('created_at'), datetime):
                    pool_dict['created_at'] = pool_dict['created_at'].isoformat()
                data.append(pool_dict)
            
            result = self.client.table('pools_scored').upsert(data).execute()
            
            if result.data:
                logger.info(f"成功插入 {len(result.data)} 條評分池子數據")
                return True
            else:
                logger.warning("插入評分池子數據失敗")
                return False
                
        except Exception as e:
            logger.error(f"插入評分池子數據失敗: {e}")
            return False
    
    async def get_latest_pools_raw(
        self, 
        chain: Optional[str] = None, 
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """獲取最新的原始池子數據"""
        try:
            query = self.client.table('pools_raw').select('*').order('created_at', desc=True)
            
            if chain:
                query = query.eq('chain', chain)
                
            result = query.limit(limit).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"獲取原始池子數據失敗: {e}")
            return []
    
    async def get_latest_pools_scored(
        self, 
        min_score: float = 0.5, 
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """獲取最新的評分池子數據"""
        try:
            result = self.client.table('pools_scored')\
                .select('*')\
                .gte('score', min_score)\
                .order('created_at', desc=True)\
                .limit(limit)\
                .execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"獲取評分池子數據失敗: {e}")
            return []

    # === Plan 相關操作 ===
    
    async def insert_plans(self, plans: List[Plan]) -> bool:
        """批次插入策略計劃"""
        try:
            data = []
            for plan in plans:
                plan_dict = asdict(plan)
                # 轉換 datetime 為 ISO 字符串
                if isinstance(plan_dict.get('created_at'), datetime):
                    plan_dict['created_at'] = plan_dict['created_at'].isoformat()
                # 序列化 params 為 JSON
                if plan_dict.get('params'):
                    plan_dict['params'] = json.dumps(plan_dict['params'])
                data.append(plan_dict)
            
            result = self.client.table('plans').upsert(data).execute()
            
            if result.data:
                logger.info(f"成功插入 {len(result.data)} 條策略計劃")
                return True
            else:
                logger.warning("插入策略計劃失敗")
                return False
                
        except Exception as e:
            logger.error(f"插入策略計劃失敗: {e}")
            return False
    
    async def get_active_plans(self) -> List[Dict[str, Any]]:
        """獲取活躍的策略計劃"""
        try:
            result = self.client.table('plans')\
                .select('*')\
                .eq('status', 'active')\
                .order('created_at', desc=True)\
                .execute()
            
            plans = result.data if result.data else []
            
            # 反序列化 params
            for plan in plans:
                if plan.get('params') and isinstance(plan['params'], str):
                    try:
                        plan['params'] = json.loads(plan['params'])
                    except json.JSONDecodeError:
                        plan['params'] = {}
            
            return plans
            
        except Exception as e:
            logger.error(f"獲取活躍計劃失敗: {e}")
            return []
    
    async def update_plan_status(self, plan_id: str, status: str) -> bool:
        """更新計劃狀態"""
        try:
            result = self.client.table('plans')\
                .update({'status': status, 'updated_at': datetime.now(timezone.utc).isoformat()})\
                .eq('id', plan_id)\
                .execute()
            
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"更新計劃狀態失敗: {e}")
            return False

    # === Position 相關操作 ===
    
    async def insert_position(self, position_data: Dict[str, Any]) -> bool:
        """插入頭寸數據"""
        try:
            # 確保時間字段格式正確
            if 'created_at' not in position_data:
                position_data['created_at'] = datetime.now(timezone.utc).isoformat()
            
            result = self.client.table('positions').insert(position_data).execute()
            
            if result.data:
                logger.info(f"成功插入頭寸數據: {position_data.get('pool_id', 'unknown')}")
                return True
            else:
                logger.warning("插入頭寸數據失敗")
                return False
                
        except Exception as e:
            logger.error(f"插入頭寸數據失敗: {e}")
            return False
    
    async def get_active_positions(self, chain: Optional[str] = None) -> List[Dict[str, Any]]:
        """獲取活躍頭寸"""
        try:
            query = self.client.table('positions')\
                .select('*')\
                .eq('status', 'active')
            
            if chain:
                query = query.eq('chain', chain)
                
            result = query.execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"獲取活躍頭寸失敗: {e}")
            return []
    
    async def update_position_status(self, position_id: str, status: str) -> bool:
        """更新頭寸狀態"""
        try:
            result = self.client.table('positions')\
                .update({'status': status, 'updated_at': datetime.now(timezone.utc).isoformat()})\
                .eq('id', position_id)\
                .execute()
            
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"更新頭寸狀態失敗: {e}")
            return False

    # === Risk Event 相關操作 ===
    
    async def insert_risk_event(self, event_data: Dict[str, Any]) -> bool:
        """插入風險事件"""
        try:
            if 'created_at' not in event_data:
                event_data['created_at'] = datetime.now(timezone.utc).isoformat()
            
            result = self.client.table('risk_events').insert(event_data).execute()
            
            if result.data:
                logger.info(f"記錄風險事件: {event_data.get('event_type', 'unknown')}")
                return True
            else:
                logger.warning("記錄風險事件失敗")
                return False
                
        except Exception as e:
            logger.error(f"記錄風險事件失敗: {e}")
            return False
    
    async def get_recent_risk_events(self, hours: int = 24) -> List[Dict[str, Any]]:
        """獲取最近的風險事件"""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            result = self.client.table('risk_events')\
                .select('*')\
                .gte('created_at', cutoff_time.isoformat())\
                .order('created_at', desc=True)\
                .execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"獲取風險事件失敗: {e}")
            return []

    # === Transaction 相關操作 ===
    
    async def insert_transaction(self, tx_data: Dict[str, Any]) -> bool:
        """插入交易記錄"""
        try:
            if 'created_at' not in tx_data:
                tx_data['created_at'] = datetime.now(timezone.utc).isoformat()
            
            result = self.client.table('txs').insert(tx_data).execute()
            
            if result.data:
                logger.info(f"記錄交易: {tx_data.get('tx_hash', 'unknown')}")
                return True
            else:
                logger.warning("記錄交易失敗")
                return False
                
        except Exception as e:
            logger.error(f"記錄交易失敗: {e}")
            return False
    
    async def get_recent_transactions(
        self, 
        status: Optional[str] = None, 
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """獲取最近交易記錄"""
        try:
            query = self.client.table('txs').select('*').order('created_at', desc=True)
            
            if status:
                query = query.eq('status', status)
                
            result = query.limit(limit).execute()
            
            return result.data if result.data else []
            
        except Exception as e:
            logger.error(f"獲取交易記錄失敗: {e}")
            return []

    # === 統計查詢 ===
    
    async def get_performance_stats(self, days: int = 7) -> Dict[str, Any]:
        """獲取性能統計"""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
            
            # 使用原生 SQL 進行複雜統計查詢
            if not self.pool:
                logger.error("數據庫連接池未初始化")
                return {}
            
            async with self.pool.acquire() as conn:
                # 總收益統計
                earnings_result = await conn.fetchrow("""
                    SELECT 
                        COUNT(*) as total_positions,
                        SUM(pnl_usd) as total_pnl,
                        AVG(pnl_usd) as avg_pnl,
                        SUM(fees_earned) as total_fees
                    FROM positions 
                    WHERE created_at >= $1 AND status = 'closed'
                """, cutoff_time)
                
                # 風險事件統計
                risk_result = await conn.fetchrow("""
                    SELECT 
                        COUNT(*) as total_events,
                        COUNT(CASE WHEN severity = 'high' THEN 1 END) as high_risk_events,
                        COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical_events
                    FROM risk_events 
                    WHERE created_at >= $1
                """, cutoff_time)
                
                return {
                    "period_days": days,
                    "total_positions": earnings_result['total_positions'] or 0,
                    "total_pnl_usd": float(earnings_result['total_pnl'] or 0),
                    "avg_pnl_usd": float(earnings_result['avg_pnl'] or 0),
                    "total_fees_usd": float(earnings_result['total_fees'] or 0),
                    "total_risk_events": risk_result['total_events'] or 0,
                    "high_risk_events": risk_result['high_risk_events'] or 0,
                    "critical_events": risk_result['critical_events'] or 0,
                    "updated_at": datetime.now(timezone.utc).isoformat()
                }
                
        except Exception as e:
            logger.error(f"獲取性能統計失敗: {e}")
            return {}
    
    async def cleanup_old_data(self, retention_days: int = 30) -> bool:
        """清理舊數據"""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(days=retention_days)
            
            # 清理各表的舊數據
            tables_to_clean = ['pools_raw', 'risk_events']
            
            for table in tables_to_clean:
                result = self.client.table(table)\
                    .delete()\
                    .lt('created_at', cutoff_time.isoformat())\
                    .execute()
                
                logger.info(f"已清理表 {table} 中 {retention_days} 天前的數據")
            
            return True
            
        except Exception as e:
            logger.error(f"清理舊數據失敗: {e}")
            return False


# 全局數據庫實例
_db_manager: Optional[SupabaseManager] = None


async def get_db() -> SupabaseManager:
    """獲取數據庫管理器實例"""
    global _db_manager
    
    if _db_manager is None:
        _db_manager = SupabaseManager()
        await _db_manager.initialize()
    
    return _db_manager


async def close_db():
    """關閉數據庫連接"""
    global _db_manager
    
    if _db_manager:
        await _db_manager.close()
        _db_manager = None


# 便捷函數
async def insert_pools_raw(pools: List[PoolRaw]) -> bool:
    """便捷函數：插入原始池子數據"""
    db = await get_db()
    return await db.insert_pools_raw(pools)


async def insert_pools_scored(pools: List[PoolScore]) -> bool:
    """便捷函數：插入評分池子數據"""
    db = await get_db()
    return await db.insert_pools_scored(pools)


async def get_active_plans() -> List[Dict[str, Any]]:
    """便捷函數：獲取活躍計劃"""
    db = await get_db()
    return await db.get_active_plans()
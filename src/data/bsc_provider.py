"""
DyFlow BSC数据提供者
负责从BSC网络获取PancakeSwap池子数据
增强版：整合 BSCScan API 和 OKX DEX API，提供准确的钱包分析
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Optional, Any
import structlog

from .base_provider import BaseDataProvider
from ..utils.models import PoolMetrics
from ..utils.exceptions import DataProviderException
from ..utils.helpers import get_utc_timestamp
from ..integrations.pancakeswap import PancakeSwapIntegration
from ..integrations.bscscan_api import BSCScanAPI
from ..integrations.okx_dex_api import OKXDexAPI, BSC_CHAIN_ID

logger = structlog.get_logger(__name__)


class BSCProvider(BaseDataProvider):
    """BSC数据提供者"""
    
    def __init__(self, config, chain_name: str):
        super().__init__(config, chain_name)
        self.web3 = None
        self.contracts = {}
        self.network_config = config.get_network_config('bsc')
        
        # 获取 BSCScan API Key
        self.bscscan_api_key = getattr(config, 'bscscan_api_key', '**********************************')
        
        # PancakeSwap 集成配置
        pancakeswap_config = {
            'endpoint': 'https://bsc.streamingfast.io/subgraphs/name/pancakeswap/exchange-v2',
            'min_tvl': 50000
        }
        self.pancakeswap_integration = PancakeSwapIntegration(pancakeswap_config)
    
    async def _setup_connections(self):
        """设置BSC网络连接"""
        try:
            # 这里应该初始化Web3连接
            # from web3 import Web3
            # self.web3 = Web3(Web3.HTTPProvider(self.network_config.rpc_url))
            
            logger.info("bsc_connection_setup", rpc_url=self.network_config.rpc_url)
            await asyncio.sleep(0.1)  # 模拟网络延迟
            
        except Exception as e:
            raise DataProviderException(f"BSC网络连接设置失败: {e}")
    
    async def _verify_connections(self):
        """验证BSC网络连接"""
        try:
            # 这里应该验证Web3连接
            # latest_block = self.web3.eth.block_number
            # if latest_block > 0:
            #     self._connection_healthy = True
            
            # 模拟实现
            await asyncio.sleep(0.05)  # 模拟网络请求
            self._connection_healthy = True
            logger.debug("bsc_connection_verified")
            
        except Exception as e:
            self._connection_healthy = False
            raise DataProviderException(f"BSC网络连接验证失败: {e}")
    
    async def analyze_wallet_portfolio(self, wallet_address: str) -> Dict[str, Any]:
        """分析钱包投资组合（新增方法）"""
        try:
            logger.info("analyzing_wallet_portfolio", wallet=wallet_address)
            
            portfolio = {
                'wallet_address': wallet_address,
                'bnb_balance': 0.0,
                'bnb_value_usd': 0.0,
                'legitimate_tokens': [],
                'spam_tokens_filtered': [],
                'lp_positions': [],
                'total_portfolio_value': 0.0,
                'analysis_timestamp': get_utc_timestamp().isoformat()
            }
            
            # 使用 BSCScan API 获取基础数据
            async with BSCScanAPI(self.bscscan_api_key) as bscscan:
                # 获取 BNB 余额
                portfolio['bnb_balance'] = await bscscan.get_bnb_balance(wallet_address)
                
                # 获取增强的代币余额分析
                token_analysis = await bscscan.get_enhanced_token_balances(wallet_address)
                portfolio['legitimate_tokens'] = token_analysis['legitimate_tokens']
                portfolio['spam_tokens_filtered'] = token_analysis['spam_tokens_filtered']
                
                # 检测 LP 持仓
                portfolio['lp_positions'] = await bscscan.detect_lp_positions(wallet_address)
            
            # 使用 OKX DEX API 获取价格信息
            async with OKXDexAPI() as okx:
                # 获取 BNB 价格
                bnb_price = await okx.get_bnb_price()
                portfolio['bnb_value_usd'] = portfolio['bnb_balance'] * bnb_price
                
                # 为合法代币添加价格信息
                if portfolio['legitimate_tokens']:
                    portfolio['legitimate_tokens'] = await okx.enrich_tokens_with_prices(
                        portfolio['legitimate_tokens'], BSC_CHAIN_ID
                    )
            
            # 计算总投资组合价值
            total_token_value = sum(token.get('value_usd', 0) for token in portfolio['legitimate_tokens'])
            total_lp_value = sum(lp.get('value_usd', 0) for lp in portfolio['lp_positions'])
            portfolio['total_portfolio_value'] = portfolio['bnb_value_usd'] + total_token_value + total_lp_value
            
            logger.info("wallet_portfolio_analyzed", 
                       wallet=wallet_address,
                       bnb_balance=portfolio['bnb_balance'],
                       token_count=len(portfolio['legitimate_tokens']),
                       lp_count=len(portfolio['lp_positions']),
                       total_value=portfolio['total_portfolio_value'])
            
            return portfolio
            
        except Exception as e:
            logger.error("wallet_portfolio_analysis_failed", wallet=wallet_address, error=str(e))
            raise DataProviderException(f"钱包投资组合分析失败: {e}")
    
    async def get_pool_metrics(self, pool_address: str) -> PoolMetrics:
        """获取BSC池子指标数据"""
        try:
            logger.debug("fetching_bsc_pool_metrics", pool=pool_address)
            
            # 使用 PancakeSwap 集成获取真实数据
            async with self.pancakeswap_integration as integration:
                pool_data = await integration.get_pool_details(pool_address)
                if pool_data:
                    return await integration.convert_to_pool_metrics(pool_data)
                else:
                    raise DataProviderException(f"未找到池子: {pool_address}")
            
        except Exception as e:
            logger.error("bsc_pool_metrics_failed", pool=pool_address, error=str(e))
            raise DataProviderException(f"获取BSC池子数据失败: {e}")
    
    async def get_token_prices(self, tokens: List[str]) -> Dict[str, float]:
        """获取BSC代币价格"""
        try:
            logger.debug("fetching_bsc_token_prices", tokens=tokens)
            
            # 使用 OKX DEX API 获取实时价格
            async with OKXDexAPI() as okx:
                prices = {}
                for token_address in tokens:
                    try:
                        price_data = await okx.get_token_price(BSC_CHAIN_ID, token_address)
                        prices[token_address] = price_data.get('price_usd', 0.0)
                    except Exception as e:
                        logger.warning("token_price_fetch_failed", token=token_address, error=str(e))
                        prices[token_address] = 0.0
                
                return prices
            
        except Exception as e:
            logger.error("bsc_token_prices_failed", error=str(e))
            raise DataProviderException(f"获取BSC代币价格失败: {e}")
    
    async def get_pool_liquidity(self, pool_address: str) -> float:
        """获取BSC池子流动性"""
        try:
            logger.debug("fetching_bsc_pool_liquidity", pool=pool_address)
            
            # 使用 PancakeSwap 集成获取真实流动性数据
            async with self.pancakeswap_integration as integration:
                pool_data = await integration.get_pool_details(pool_address)
                if pool_data:
                    return float(pool_data.get('reserveUSD', 0))
                else:
                    return 0.0
            
        except Exception as e:
            logger.error("bsc_pool_liquidity_failed", pool=pool_address, error=str(e))
            raise DataProviderException(f"获取BSC池子流动性失败: {e}")
    
    async def get_pool_volume_24h(self, pool_address: str) -> float:
        """获取BSC池子24小时交易量"""
        try:
            logger.debug("fetching_bsc_pool_volume", pool=pool_address)
            
            # 使用 PancakeSwap 集成获取真实交易量数据
            async with self.pancakeswap_integration as integration:
                pool_data = await integration.get_pool_details(pool_address)
                if pool_data:
                    day_data = pool_data.get('dayData', [])
                    if day_data:
                        return float(day_data[0].get('dailyVolumeUSD', 0))
                return 0.0
            
        except Exception as e:
            logger.error("bsc_pool_volume_failed", pool=pool_address, error=str(e))
            raise DataProviderException(f"获取BSC池子交易量失败: {e}")
    
    async def get_pancake_pool_info(self, pool_address: str) -> Dict:
        """获取PancakeSwap特定的池子信息"""
        try:
            async with self.pancakeswap_integration as integration:
                return await integration.get_pool_details(pool_address)
            
        except Exception as e:
            logger.error("pancake_pool_info_failed", pool=pool_address, error=str(e))
            raise DataProviderException(f"获取PancakeSwap池子信息失败: {e}")
    
    async def get_top_pools(self, limit: int = 50) -> List[PoolMetrics]:
        """获取顶级流动性池"""
        try:
            async with self.pancakeswap_integration as integration:
                pools_data = await integration.get_top_pools(limit)
                pools = []
                for pool_data in pools_data:
                    try:
                        pool_metrics = await integration.convert_to_pool_metrics(pool_data)
                        pools.append(pool_metrics)
                    except Exception as e:
                        logger.warning("pool_conversion_failed",
                                     pool_id=pool_data.get('id'), error=str(e))
                        continue
                return pools
                
        except Exception as e:
            logger.error("get_top_pools_failed", error=str(e))
            raise DataProviderException(f"获取顶级池子失败: {e}")
    
    async def get_high_apr_pools(self, min_apr: float = 0.05) -> List[PoolMetrics]:
        """获取高 APR 池子"""
        try:
            async with self.pancakeswap_integration as integration:
                pools_data = await integration.get_pools_with_high_apr(min_apr)
                pools = []
                for pool_data in pools_data:
                    try:
                        pool_metrics = await integration.convert_to_pool_metrics(pool_data)
                        pools.append(pool_metrics)
                    except Exception as e:
                        logger.warning("high_apr_pool_conversion_failed",
                                     pool_id=pool_data.get('id'), error=str(e))
                        continue
                return pools
                
        except Exception as e:
            logger.error("get_high_apr_pools_failed", error=str(e))
            raise DataProviderException(f"获取高 APR 池子失败: {e}")
    
    async def get_user_lp_positions(self, wallet_address: str) -> List[Dict[str, Any]]:
        """获取用户 LP 持仓（新增方法）"""
        try:
            async with self.pancakeswap_integration as integration:
                return await integration.get_user_lp_positions(wallet_address)
                
        except Exception as e:
            logger.error("get_user_lp_positions_failed", wallet=wallet_address, error=str(e))
            raise DataProviderException(f"获取用户 LP 持仓失败: {e}")
"""
BSCScan API 集成
提供 BSC 鏈上數據查詢功能，支持錢包分析器
"""

import aiohttp
import asyncio
from typing import Dict, List, Any, Optional
import structlog

from ..utils.exceptions import DataProviderException

logger = structlog.get_logger(__name__)


class BSCScanAPI:
    """BSCScan API 客戶端"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.base_url = "https://api.bscscan.com/api"
        self.api_key = api_key
        self.session: Optional[aiohttp.ClientSession] = None
        self._request_delay = 0.2  # 200ms 延遲避免頻率限制
    
    async def __aenter__(self):
        """異步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'DyFlow-Portfolio-Manager/1.0'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """異步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def get_balance(self, address: str) -> int:
        """
        獲取錢包 BNB 餘額
        
        Args:
            address: 錢包地址
            
        Returns:
            餘額 (wei)
        """
        params = {
            'module': 'account',
            'action': 'balance',
            'address': address,
            'tag': 'latest'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if result['status'] == '1':
                return int(result['result'])
            else:
                raise DataProviderException(f"BSCScan 餘額查詢失敗: {result.get('message', 'Unknown error')}")
        except Exception as e:
            logger.error("get_balance_failed", address=address, error=str(e))
            raise DataProviderException(f"獲取餘額失敗: {e}")
    
    async def get_token_balance(self, address: str, contract_address: str) -> int:
        """
        獲取特定代幣餘額
        
        Args:
            address: 錢包地址
            contract_address: 代幣合約地址
            
        Returns:
            代幣餘額 (最小單位)
        """
        params = {
            'module': 'account',
            'action': 'tokenbalance',
            'contractaddress': contract_address,
            'address': address,
            'tag': 'latest'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if result['status'] == '1':
                return int(result['result'])
            else:
                logger.warning("token_balance_query_failed", 
                             address=address, 
                             contract=contract_address,
                             error=result.get('message'))
                return 0
        except Exception as e:
            logger.warning("get_token_balance_failed", 
                         address=address, 
                         contract=contract_address,
                         error=str(e))
            return 0
    
    async def get_transactions(self, address: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        獲取錢包交易記錄
        
        Args:
            address: 錢包地址
            limit: 交易數量限制
            
        Returns:
            交易記錄列表
        """
        params = {
            'module': 'account',
            'action': 'txlist',
            'address': address,
            'startblock': 0,
            'endblock': ********,
            'page': 1,
            'offset': min(limit, 10000),
            'sort': 'desc'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if result['status'] == '1':
                return result['result']
            else:
                logger.warning("get_transactions_failed", 
                             address=address, 
                             error=result.get('message'))
                return []
        except Exception as e:
            logger.error("get_transactions_error", address=address, error=str(e))
            return []
    
    async def get_token_transactions(self, address: str, limit: int = 1000) -> List[Dict[str, Any]]:
        """
        獲取代幣交易記錄
        
        Args:
            address: 錢包地址
            limit: 交易數量限制
            
        Returns:
            代幣交易記錄列表
        """
        params = {
            'module': 'account',
            'action': 'tokentx',
            'address': address,
            'startblock': 0,
            'endblock': ********,
            'page': 1,
            'offset': min(limit, 10000),
            'sort': 'desc'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if result['status'] == '1':
                return result['result']
            else:
                logger.warning("get_token_transactions_failed", 
                             address=address, 
                             error=result.get('message'))
                return []
        except Exception as e:
            logger.error("get_token_transactions_error", address=address, error=str(e))
            return []
    
    async def get_internal_transactions(self, address: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        獲取內部交易記錄
        
        Args:
            address: 錢包地址
            limit: 交易數量限制
            
        Returns:
            內部交易記錄列表
        """
        params = {
            'module': 'account',
            'action': 'txlistinternal',
            'address': address,
            'startblock': 0,
            'endblock': ********,
            'page': 1,
            'offset': min(limit, 10000),
            'sort': 'desc'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if result['status'] == '1':
                return result['result']
            else:
                logger.warning("get_internal_transactions_failed", 
                             address=address, 
                             error=result.get('message'))
                return []
        except Exception as e:
            logger.error("get_internal_transactions_error", address=address, error=str(e))
            return []
    
    async def get_contract_abi(self, contract_address: str) -> Optional[str]:
        """
        獲取合約 ABI
        
        Args:
            contract_address: 合約地址
            
        Returns:
            合約 ABI (JSON 字符串) 或 None
        """
        params = {
            'module': 'contract',
            'action': 'getabi',
            'address': contract_address
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if result['status'] == '1':
                return result['result']
            else:
                logger.warning("get_contract_abi_failed", 
                             contract=contract_address,
                             error=result.get('message'))
                return None
        except Exception as e:
            logger.error("get_contract_abi_error", contract=contract_address, error=str(e))
            return None
    
    async def get_token_info(self, contract_address: str) -> Dict[str, Any]:
        """
        獲取代幣基本信息
        
        Args:
            contract_address: 代幣合約地址
            
        Returns:
            代幣信息字典
        """
        # BSCScan 沒有直接的代幣信息 API，需要通過其他方式獲取
        # 這裡返回基本結構，實際信息需要通過合約調用獲取
        return {
            'contract_address': contract_address,
            'name': 'Unknown',
            'symbol': 'Unknown',
            'decimals': 18,
            'total_supply': '0',
            'source': 'bscscan_placeholder'
        }
    
    async def get_block_by_number(self, block_number: int) -> Dict[str, Any]:
        """
        獲取區塊信息
        
        Args:
            block_number: 區塊號
            
        Returns:
            區塊信息
        """
        params = {
            'module': 'proxy',
            'action': 'eth_getBlockByNumber',
            'tag': hex(block_number),
            'boolean': 'true'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if 'result' in result:
                return result['result']
            else:
                logger.warning("get_block_failed", 
                             block=block_number,
                             error=result)
                return {}
        except Exception as e:
            logger.error("get_block_error", block=block_number, error=str(e))
            return {}
    
    async def get_transaction_receipt(self, tx_hash: str) -> Dict[str, Any]:
        """
        獲取交易收據
        
        Args:
            tx_hash: 交易哈希
            
        Returns:
            交易收據
        """
        params = {
            'module': 'proxy',
            'action': 'eth_getTransactionReceipt',
            'txhash': tx_hash
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if 'result' in result:
                return result['result']
            else:
                logger.warning("get_transaction_receipt_failed", 
                             tx_hash=tx_hash,
                             error=result)
                return {}
        except Exception as e:
            logger.error("get_transaction_receipt_error", tx_hash=tx_hash, error=str(e))
            return {}
    
    async def get_gas_price(self) -> int:
        """
        獲取當前 Gas 價格
        
        Returns:
            Gas 價格 (wei)
        """
        params = {
            'module': 'proxy',
            'action': 'eth_gasPrice'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if 'result' in result:
                return int(result['result'], 16)
            else:
                logger.warning("get_gas_price_failed", error=result)
                return 5000000000  # 5 Gwei 默認值
        except Exception as e:
            logger.error("get_gas_price_error", error=str(e))
            return 5000000000
    
    async def get_latest_block_number(self) -> int:
        """
        獲取最新區塊號
        
        Returns:
            最新區塊號
        """
        params = {
            'module': 'proxy',
            'action': 'eth_blockNumber'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if 'result' in result:
                return int(result['result'], 16)
            else:
                logger.warning("get_latest_block_failed", error=result)
                return 0
        except Exception as e:
            logger.error("get_latest_block_error", error=str(e))
            return 0
    
    async def _make_request(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        發起 BSCScan API 請求
        
        Args:
            params: 請求參數
            
        Returns:
            API 響應
        """
        if not self.session:
            raise DataProviderException("HTTP session 未初始化")
        
        try:
            # 添加請求延遲避免頻率限制
            await asyncio.sleep(self._request_delay)
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise DataProviderException(f"BSCScan API 請求失敗: {response.status} - {error_text}")
                
                result = await response.json()
                
                # 檢查 API 錯誤
                if result.get('status') == '0' and 'rate limit' in result.get('message', '').lower():
                    logger.warning("bscscan_rate_limit_hit", message=result.get('message'))
                    # 增加延遲
                    self._request_delay = min(self._request_delay * 2, 2.0)
                    await asyncio.sleep(1.0)
                
                return result
                
        except aiohttp.ClientError as e:
            raise DataProviderException(f"網絡請求失敗: {e}")
        except Exception as e:
            raise DataProviderException(f"請求執行失敗: {e}")
    
    # 便捷方法
    async def is_contract(self, address: str) -> bool:
        """
        檢查地址是否為合約
        
        Args:
            address: 要檢查的地址
            
        Returns:
            True 如果是合約，False 如果是普通地址
        """
        try:
            params = {
                'module': 'proxy',
                'action': 'eth_getCode',
                'address': address,
                'tag': 'latest'
            }
            
            if self.api_key:
                params['apikey'] = self.api_key
            
            result = await self._make_request(params)
            if 'result' in result:
                code = result['result']
                return code != '0x' and len(code) > 2
            return False
        except Exception as e:
            logger.warning("is_contract_check_failed", address=address, error=str(e))
            return False
    
    async def get_account_nonce(self, address: str) -> int:
        """
        獲取賬戶 nonce
        
        Args:
            address: 賬戶地址
            
        Returns:
            當前 nonce 值
        """
        params = {
            'module': 'proxy',
            'action': 'eth_getTransactionCount',
            'address': address,
            'tag': 'latest'
        }
        
        if self.api_key:
            params['apikey'] = self.api_key
        
        try:
            result = await self._make_request(params)
            if 'result' in result:
                return int(result['result'], 16)
            else:
                logger.warning("get_nonce_failed", address=address, error=result)
                return 0
        except Exception as e:
            logger.error("get_nonce_error", address=address, error=str(e))
            return 0
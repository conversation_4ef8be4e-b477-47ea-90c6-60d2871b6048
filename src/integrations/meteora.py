"""
Meteora DLMM REST API 集成
支持 Meteora 动态流动性做市商数据获取
"""

import asyncio
import aiohttp
from typing import List, Dict, Optional, Any
from datetime import datetime
import structlog

from ..utils.models import PoolMetrics
from ..utils.exceptions import DataProviderException
from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)


class MeteoraIntegration:
    """Meteora DLMM REST API 集成"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_url = config.get('endpoint', 'https://dlmm-api.meteora.ag')
        self.min_tvl = config.get('min_tvl', 50000)
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'DyFlow-AI-Agent/1.0'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def get_all_pools(self) -> List[Dict[str, Any]]:
        """获取所有 DLMM 池子"""
        try:
            url = f"{self.base_url}/pair/all"
            result = await self._make_request(url)
            
            # 筛选符合最小 TVL 要求的池子
            filtered_pools = []
            for pool in result:
                try:
                    tvl = float(pool.get('liquidity', 0))
                    if tvl >= self.min_tvl:
                        filtered_pools.append(pool)
                except (ValueError, TypeError):
                    continue
            
            return filtered_pools
            
        except Exception as e:
            logger.error("meteora_get_all_pools_failed", error=str(e))
            raise DataProviderException(f"获取 Meteora 所有池子失败: {e}")
    
    async def get_pool_details(self, pool_address: str) -> Optional[Dict[str, Any]]:
        """获取单个池子详情"""
        try:
            url = f"{self.base_url}/pair/{pool_address}"
            result = await self._make_request(url)
            return result
            
        except Exception as e:
            logger.error("meteora_get_pool_details_failed", pool_address=pool_address, error=str(e))
            raise DataProviderException(f"获取 Meteora 池子详情失败: {e}")
    
    async def get_pool_fees_and_apr(self, pool_address: str) -> Dict[str, Any]:
        """获取池子手续费和 APR 数据"""
        try:
            url = f"{self.base_url}/pair/{pool_address}/fees"
            result = await self._make_request(url)
            return result
            
        except Exception as e:
            logger.error("meteora_get_pool_fees_failed", pool_address=pool_address, error=str(e))
            return {}
    
    async def get_pool_bins(self, pool_address: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取池子的价格区间数据"""
        try:
            url = f"{self.base_url}/pair/{pool_address}/bins"
            params = {'limit': limit}
            result = await self._make_request(url, params=params)
            return result.get('bins', [])
            
        except Exception as e:
            logger.error("meteora_get_pool_bins_failed", pool_address=pool_address, error=str(e))
            return []
    
    async def get_top_pools_by_volume(self, limit: int = 50) -> List[Dict[str, Any]]:
        """按交易量获取顶级池子"""
        try:
            # 获取所有池子
            all_pools = await self.get_all_pools()
            
            # 按 24h 交易量排序
            sorted_pools = sorted(
                all_pools,
                key=lambda x: float(x.get('volume24h', 0)),
                reverse=True
            )
            
            return sorted_pools[:limit]
            
        except Exception as e:
            logger.error("meteora_get_top_pools_failed", error=str(e))
            raise DataProviderException(f"获取 Meteora 顶级池子失败: {e}")
    
    async def get_pools_with_high_apr(self, min_apr: float = 0.05) -> List[Dict[str, Any]]:
        """获取高 APR 池子"""
        try:
            all_pools = await self.get_all_pools()
            high_apr_pools = []
            
            for pool in all_pools:
                try:
                    # 计算 APR
                    apr = await self._calculate_apr(pool)
                    if apr >= min_apr:
                        pool['calculated_apr'] = apr
                        high_apr_pools.append(pool)
                except Exception as e:
                    logger.warning("meteora_apr_calculation_failed", 
                                 pool_address=pool.get('address'), error=str(e))
                    continue
            
            # 按 APR 排序
            high_apr_pools.sort(key=lambda x: x.get('calculated_apr', 0), reverse=True)
            return high_apr_pools
            
        except Exception as e:
            logger.error("meteora_get_high_apr_pools_failed", error=str(e))
            raise DataProviderException(f"获取 Meteora 高 APR 池子失败: {e}")
    
    async def convert_to_pool_metrics(self, pool_data: Dict[str, Any]) -> PoolMetrics:
        """将 Meteora 数据转换为 PoolMetrics"""
        try:
            # 获取代币信息
            mint_x = pool_data.get('mint_x', {})
            mint_y = pool_data.get('mint_y', {})
            
            # 计算 APR
            apr = await self._calculate_apr(pool_data)
            
            # 计算无常损失风险
            il_risk = await self._calculate_il_risk(pool_data)
            
            # 获取交易量数据
            volume_24h = float(pool_data.get('volume24h', 0))
            tvl = float(pool_data.get('liquidity', 0))
            
            # 计算手续费
            fee_rate = float(pool_data.get('base_fee_percentage', 0.0025))
            fees_24h = volume_24h * fee_rate
            
            # 获取储备量
            reserve_x = float(pool_data.get('reserve_x', 0))
            reserve_y = float(pool_data.get('reserve_y', 0))
            
            # 计算价格
            price_x = reserve_y / reserve_x if reserve_x > 0 else 0
            price_y = reserve_x / reserve_y if reserve_y > 0 else 0
            
            return PoolMetrics(
                pool_address=pool_data.get('address', ''),
                chain="solana",
                token_a=mint_x.get('symbol', 'Unknown'),
                token_b=mint_y.get('symbol', 'Unknown'),
                apr=apr,
                il_risk=il_risk,
                tvl=tvl,
                volume_24h=volume_24h,
                fee_rate=fee_rate,
                timestamp=get_utc_timestamp(),
                token_a_reserve=reserve_x,
                token_b_reserve=reserve_y,
                token_a_price=price_x,
                token_b_price=price_y,
                liquidity_token_supply=float(pool_data.get('supply', 0)),
                fees_24h=fees_24h,
                raw_data={
                    "source": "meteora_api",
                    "bin_step": pool_data.get('bin_step'),
                    "active_id": pool_data.get('active_id'),
                    "max_fee_percentage": pool_data.get('max_fee_percentage'),
                    "protocol_fee_percentage": pool_data.get('protocol_fee_percentage'),
                    "mint_x_address": mint_x.get('address'),
                    "mint_y_address": mint_y.get('address'),
                    "created_at": pool_data.get('created_at')
                }
            )
            
        except Exception as e:
            logger.error("meteora_pool_metrics_conversion_failed", 
                        pool_address=pool_data.get('address'), error=str(e))
            raise DataProviderException(f"转换 Meteora 池子数据失败: {e}")
    
    async def _make_request(self, url: str, params: Optional[Dict] = None) -> Any:
        """执行 HTTP 请求"""
        if not self.session:
            raise DataProviderException("HTTP session 未初始化")
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise DataProviderException(f"API 请求失败: {response.status} - {error_text}")
                
                result = await response.json()
                return result
                
        except aiohttp.ClientError as e:
            raise DataProviderException(f"网络请求失败: {e}")
        except Exception as e:
            raise DataProviderException(f"请求执行失败: {e}")
    
    async def _calculate_apr(self, pool_data: Dict[str, Any]) -> float:
        """计算年化收益率 APR"""
        try:
            volume_24h = float(pool_data.get('volume24h', 0))
            liquidity = float(pool_data.get('liquidity', 0))
            
            if liquidity <= 0:
                return 0.0
            
            # 获取手续费率
            fee_rate = float(pool_data.get('base_fee_percentage', 0.0025))
            
            # 计算日收益
            daily_fees = volume_24h * fee_rate
            
            # 年化收益率
            apr = (daily_fees / liquidity) * 365
            
            # 限制最大 APR
            return min(apr, 10.0)  # 最大 1000%
            
        except Exception as e:
            logger.warning("meteora_apr_calculation_failed", error=str(e))
            return 0.0
    
    async def _calculate_il_risk(self, pool_data: Dict[str, Any]) -> float:
        """计算无常损失风险"""
        try:
            # 获取 bin_step 作为风险指标
            bin_step = int(pool_data.get('bin_step', 25))
            
            # bin_step 越大，价格区间越宽，风险相对较低
            # bin_step 越小，价格区间越窄，风险相对较高
            if bin_step <= 10:
                base_risk = 0.08  # 8% 基础风险
            elif bin_step <= 25:
                base_risk = 0.05  # 5% 基础风险
            elif bin_step <= 50:
                base_risk = 0.03  # 3% 基础风险
            else:
                base_risk = 0.02  # 2% 基础风险
            
            # 根据交易量调整风险
            volume_24h = float(pool_data.get('volume24h', 0))
            liquidity = float(pool_data.get('liquidity', 1))
            
            if liquidity > 0:
                volume_ratio = volume_24h / liquidity
                # 高交易量相对于流动性意味着更高的波动性
                volatility_adjustment = min(volume_ratio * 0.1, 0.05)
                risk = base_risk + volatility_adjustment
            else:
                risk = base_risk
            
            return min(risk, 0.2)  # 最大 20% 风险
            
        except Exception as e:
            logger.warning("meteora_il_risk_calculation_failed", error=str(e))
            return 0.03  # 默认 3% 风险
    
    async def get_pool_historical_data(self, pool_address: str, days: int = 7) -> List[Dict[str, Any]]:
        """获取池子历史数据"""
        try:
            url = f"{self.base_url}/pair/{pool_address}/historical"
            params = {'days': days}
            result = await self._make_request(url, params=params)
            return result.get('data', [])
            
        except Exception as e:
            logger.error("meteora_get_historical_data_failed", 
                        pool_address=pool_address, error=str(e))
            return []
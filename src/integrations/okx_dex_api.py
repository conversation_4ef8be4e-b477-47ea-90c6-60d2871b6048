"""
OKX 官方 API 集成
包含 DEX API 和錢包 API，用于查询代币价格、余额和交易历史
整合官方錢包 API 以獲取準確的即時價格
"""

import aiohttp
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

from ..utils.exceptions import DataProviderException

logger = structlog.get_logger(__name__)

# BSC 链配置
BSC_CHAIN_ID = "56"
BSC_MAINNET = {
    'chain_id': BSC_CHAIN_ID,
    'chain_name': 'BSC',
    'native_token': 'BNB',
    'rpc_url': 'https://bsc-dataseed1.binance.org/'
}


class OKXWalletAPI:
    """OKX 官方錢包 API 客戶端"""
    
    def __init__(self, api_key: str = None, secret_key: str = None, passphrase: str = None):
        self.base_url = "https://www.okx.com/api/v5/wallet"
        self.session = None
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
    
    async def __aenter__(self):
        headers = {
            'User-Agent': 'DyFlow-Portfolio-Manager/1.0',
            'Content-Type': 'application/json'
        }
        
        # 如果有 API 密鑰，添加認證頭
        if self.api_key:
            headers.update({
                'OK-ACCESS-KEY': self.api_key,
                'OK-ACCESS-SIGN': '',
                'OK-ACCESS-TIMESTAMP': str(int(datetime.now().timestamp())),
                'OK-ACCESS-PASSPHRASE': self.passphrase or ''
            })
        
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers=headers
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_token_prices(self, token_addresses: List[str], chain_id: str = "56") -> Dict[str, Dict[str, Any]]:
        """使用 OKX 錢包 API 獲取代幣價格"""
        endpoint = f"{self.base_url}/token/current-price-list"
        
        token_list = []
        for addr in token_addresses:
            token_list.append({
                "chainIndex": chain_id,
                "tokenContractAddress": addr
            })
        
        params = {"tokenList": token_list}
        
        try:
            async with self.session.post(endpoint, json=params) as response:
                if response.status != 200:
                    logger.warning("okx_wallet_api_request_failed", status=response.status)
                    return {}
                
                result = await response.json()
                if result.get('code') == '0' and result.get('data'):
                    prices = {}
                    for token_data in result['data']:
                        addr = token_data.get('tokenContractAddress', '')
                        if addr:
                            prices[addr] = {
                                'price_usd': float(token_data.get('price', 0)),
                                'price_change_24h': float(token_data.get('priceChange24h', 0)),
                                'is_valid': True,
                                'source': 'okx_wallet_api'
                            }
                    return prices
                else:
                    logger.warning("okx_wallet_api_error", error=result.get('msg', 'Unknown error'))
                    return {}
        except Exception as e:
            logger.warning("okx_wallet_api_exception", error=str(e))
            return {}
    
    async def get_bnb_price(self) -> float:
        """獲取 BNB 價格"""
        bnb_address = "******************************************"
        
        try:
            prices = await self.get_token_prices([bnb_address], "56")
            if bnb_address in prices:
                return prices[bnb_address]['price_usd']
            else:
                return await self._get_bnb_price_coingecko()
        except Exception:
            return await self._get_bnb_price_coingecko()
    
    async def _get_bnb_price_coingecko(self) -> float:
        """備用：使用 CoinGecko 獲取 BNB 價格"""
        try:
            url = "https://api.coingecko.com/api/v3/simple/price?ids=binancecoin&vs_currencies=usd"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('binancecoin', {}).get('usd', 600.0)
        except Exception:
            pass
        return 600.0


class OKXDexAPI:
    """OKX DEX API 集成 - 增強版，整合官方錢包 API"""
    
    def __init__(self, api_key: str = None, secret_key: str = None, passphrase: str = None):
        self.base_url = "https://www.okx.com/api/v5/dex"
        self.session: Optional[aiohttp.ClientSession] = None
        
        # OKX 認證信息
        self.api_key = api_key
        self.secret_key = secret_key
        self.passphrase = passphrase
        
        # 初始化錢包 API
        self.wallet_api = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'DyFlow-Portfolio-Manager/1.0',
                'Content-Type': 'application/json'
            }
        )
        
        # 初始化錢包 API
        self.wallet_api = OKXWalletAPI(self.api_key, self.secret_key, self.passphrase)
        await self.wallet_api.__aenter__()
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.wallet_api:
            await self.wallet_api.__aexit__(exc_type, exc_val, exc_tb)
        if self.session:
            await self.session.close()
    
    async def get_token_prices_batch(self, chain_id: str, token_addresses: List[str]) -> Dict[str, Dict[str, Any]]:
        """批量獲取代幣價格 - 優先使用官方錢包 API"""
        prices = {}
        
        # 1. 首先嘗試使用官方錢包 API
        try:
            if self.wallet_api:
                wallet_prices = await self.wallet_api.get_token_prices(token_addresses, chain_id)
                if wallet_prices:
                    logger.info("using_okx_wallet_api_prices", count=len(wallet_prices))
                    return wallet_prices
        except Exception as e:
            logger.warning("okx_wallet_api_failed", error=str(e))
        
        # 2. 備用方案：使用 DEX API
        logger.info("falling_back_to_dex_api")
        batch_size = 10
        for i in range(0, len(token_addresses), batch_size):
            batch = token_addresses[i:i + batch_size]
            batch_prices = await self._fetch_prices_batch_dex(chain_id, batch)
            prices.update(batch_prices)
            await asyncio.sleep(0.1)
        
        # 3. 最後備用：CoinGecko
        if not prices:
            logger.info("falling_back_to_coingecko")
            prices = await self._fetch_prices_coingecko(token_addresses)
        
        return prices
    
    async def _fetch_prices_batch_dex(self, chain_id: str, token_addresses: List[str]) -> Dict[str, Dict[str, Any]]:
        """使用 DEX API 獲取價格"""
        prices = {}
        
        for token_address in token_addresses:
            try:
                price_data = await self.get_token_price(chain_id, token_address)
                if price_data:
                    prices[token_address] = price_data
            except Exception as e:
                logger.warning("dex_api_token_price_failed", token=token_address, error=str(e))
                prices[token_address] = {
                    'token_address': token_address,
                    'price_usd': 0.0,
                    'price_change_24h': 0.0,
                    'volume_24h': 0.0,
                    'market_cap': 0.0,
                    'error': str(e),
                    'source': 'dex_api_failed'
                }
        
        return prices
    
    async def _fetch_prices_coingecko(self, token_addresses: List[str]) -> Dict[str, Dict[str, Any]]:
        """使用 CoinGecko 作為最後備用方案"""
        prices = {}
        
        # CoinGecko 代幣映射
        coingecko_mapping = {
            "******************************************": "tether",
            "******************************************": "binance-usd",
            "******************************************": "usd-coin",
            "******************************************": "ethereum",
            "******************************************": "bitcoin",
            "******************************************": "pancakeswap-token",
        }
        
        coingecko_ids = []
        address_mapping = {}
        
        for addr in token_addresses:
            addr_lower = addr.lower()
            if addr_lower in coingecko_mapping:
                cg_id = coingecko_mapping[addr_lower]
                coingecko_ids.append(cg_id)
                address_mapping[cg_id] = addr
        
        if coingecko_ids:
            try:
                ids_str = ','.join(coingecko_ids)
                url = f"https://api.coingecko.com/api/v3/simple/price?ids={ids_str}&vs_currencies=usd&include_24hr_change=true"
                
                async with self.session.get(url) as response:
                    if response.status == 200:
                        price_data = await response.json()
                        
                        for cg_id, price_info in price_data.items():
                            if cg_id in address_mapping:
                                addr = address_mapping[cg_id]
                                prices[addr] = {
                                    'token_address': addr,
                                    'price_usd': float(price_info.get('usd', 0)),
                                    'price_change_24h': float(price_info.get('usd_24h_change', 0)),
                                    'volume_24h': 0.0,
                                    'market_cap': 0.0,
                                    'is_valid': True,
                                    'source': 'coingecko_fallback'
                                }
            except Exception as e:
                logger.warning("coingecko_fallback_failed", error=str(e))
        
        return prices
    
    async def get_token_price(self, chain_id: str, token_address: str) -> Dict[str, Any]:
        """獲取單個代幣價格 - DEX API"""
        endpoint = f"{self.base_url}/market/token-price"
        params = {
            'chainId': chain_id,
            'tokenContractAddress': token_address
        }
        
        try:
            result = await self._make_request('GET', endpoint, params=params)
            if result['code'] == '0' and result['data']:
                price_data = result['data'][0]
                return {
                    'token_address': token_address,
                    'price_usd': float(price_data.get('usdPrice', 0)),
                    'price_change_24h': float(price_data.get('priceChange24h', 0)),
                    'volume_24h': float(price_data.get('volume24h', 0)),
                    'market_cap': float(price_data.get('marketCap', 0)),
                    'timestamp': price_data.get('timestamp', ''),
                    'is_valid': True,
                    'source': 'dex_api'
                }
            else:
                logger.warning("dex_api_no_data", token=token_address, result=result)
                return {
                    'token_address': token_address,
                    'price_usd': 0.0,
                    'price_change_24h': 0.0,
                    'volume_24h': 0.0,
                    'market_cap': 0.0,
                    'is_valid': False,
                    'error': result.get('msg', 'No data'),
                    'source': 'dex_api_failed'
                }
                
        except Exception as e:
            logger.error("dex_api_error", token=token_address, error=str(e))
            return {
                'token_address': token_address,
                'price_usd': 0.0,
                'price_change_24h': 0.0,
                'volume_24h': 0.0,
                'market_cap': 0.0,
                'is_valid': False,
                'error': str(e),
                'source': 'dex_api_failed'
            }
    
    async def enrich_tokens_with_prices(self, tokens: List[Dict[str, Any]], chain_id: str = BSC_CHAIN_ID) -> List[Dict[str, Any]]:
        """為代幣列表添加價格信息 - 使用多重價格源"""
        if not tokens:
            return []
        
        # 提取代幣地址
        token_addresses = [token['address'] for token in tokens]
        
        # 批量獲取價格（使用多重備用方案）
        prices = await self.get_token_prices_batch(chain_id, token_addresses)
        
        # 合併價格信息
        enriched_tokens = []
        for token in tokens:
            token_address = token['address']
            price_info = prices.get(token_address, {})
            
            enriched_token = {
                **token,
                'price_usd': price_info.get('price_usd', 0.0),
                'price_change_24h': price_info.get('price_change_24h', 0.0),
                'volume_24h': price_info.get('volume_24h', 0.0),
                'market_cap': price_info.get('market_cap', 0.0),
                'value_usd': token.get('balance', 0) * price_info.get('price_usd', 0.0),
                'price_available': price_info.get('is_valid', False),
                'price_source': price_info.get('source', 'unavailable')
            }
            
            enriched_tokens.append(enriched_token)
        
        # 按美元價值排序
        enriched_tokens.sort(key=lambda x: x.get('value_usd', 0), reverse=True)
        
        return enriched_tokens
    
    async def get_bnb_price(self) -> float:
        """獲取 BNB 價格 - 優先使用官方錢包 API"""
        try:
            if self.wallet_api:
                return await self.wallet_api.get_bnb_price()
        except Exception as e:
            logger.warning("wallet_api_bnb_price_failed", error=str(e))
        
        # 備用方案：DEX API
        bnb_address = "******************************************"
        try:
            price_data = await self.get_token_price(BSC_CHAIN_ID, bnb_address)
            return price_data.get('price_usd', 600.0)
        except Exception as e:
            logger.warning("dex_api_bnb_price_failed", error=str(e))
            return 600.0
    
    async def get_wallet_balances(self, chain_id: str, address: str) -> List[Dict[str, Any]]:
        """獲取錢包代幣餘額"""
        endpoint = f"{self.base_url}/market/balance"
        params = {
            'chainId': chain_id,
            'address': address
        }
        
        try:
            result = await self._make_request('GET', endpoint, params=params)
            if result['code'] == '0' and result['data']:
                balances = []
                for token_data in result['data']:
                    balance_info = {
                        'token_address': token_data.get('tokenContractAddress', ''),
                        'token_symbol': token_data.get('tokenSymbol', ''),
                        'token_name': token_data.get('tokenName', ''),
                        'balance': float(token_data.get('balance', 0)),
                        'balance_usd': float(token_data.get('balanceUsd', 0)),
                        'decimals': int(token_data.get('tokenDecimal', 18)),
                        'logo_url': token_data.get('tokenLogoUrl', '')
                    }
                    if balance_info['balance'] > 0:
                        balances.append(balance_info)
                return balances
            else:
                logger.warning("get_wallet_balances_failed", address=address, error=result.get('msg'))
                return []
                
        except Exception as e:
            logger.error("get_wallet_balances_error", address=address, error=str(e))
            return []
    
    async def get_transaction_history(self, chain_id: str, address: str, limit: int = 50) -> List[Dict[str, Any]]:
        """獲取交易歷史"""
        endpoint = f"{self.base_url}/market/history"
        params = {
            'chainId': chain_id,
            'address': address,
            'limit': str(limit)
        }
        
        try:
            result = await self._make_request('GET', endpoint, params=params)
            if result['code'] == '0' and result['data']:
                transactions = []
                for tx_data in result['data']:
                    tx_info = {
                        'tx_hash': tx_data.get('txHash', ''),
                        'block_number': tx_data.get('blockNumber', ''),
                        'timestamp': tx_data.get('timestamp', ''),
                        'from_address': tx_data.get('from', ''),
                        'to_address': tx_data.get('to', ''),
                        'value': float(tx_data.get('value', 0)),
                        'gas_used': tx_data.get('gasUsed', ''),
                        'gas_price': tx_data.get('gasPrice', ''),
                        'token_transfers': tx_data.get('tokenTransfers', [])
                    }
                    transactions.append(tx_info)
                return transactions
            else:
                logger.warning("get_transaction_history_failed", address=address, error=result.get('msg'))
                return []
                
        except Exception as e:
            logger.error("get_transaction_history_error", address=address, error=str(e))
            return []
    
    async def get_supported_chains(self) -> List[Dict[str, Any]]:
        """獲取支持的鏈列表"""
        endpoint = f"{self.base_url}/market/chains"
        
        try:
            result = await self._make_request('GET', endpoint)
            if result['code'] == '0' and result['data']:
                chains = []
                for chain_data in result['data']:
                    chain_info = {
                        'chain_id': chain_data.get('chainId', ''),
                        'chain_name': chain_data.get('chainName', ''),
                        'native_token': chain_data.get('nativeToken', ''),
                        'logo_url': chain_data.get('logoUrl', '')
                    }
                    chains.append(chain_info)
                return chains
            else:
                logger.warning("get_supported_chains_failed", error=result.get('msg'))
                return []
                
        except Exception as e:
            logger.error("get_supported_chains_error", error=str(e))
            return []
    
    async def get_token_info(self, chain_id: str, token_address: str) -> Dict[str, Any]:
        """獲取代幣詳細信息"""
        endpoint = f"{self.base_url}/market/token-info"
        params = {
            'chainId': chain_id,
            'tokenContractAddress': token_address
        }
        
        try:
            result = await self._make_request('GET', endpoint, params=params)
            if result['code'] == '0' and result['data']:
                token_data = result['data']
                return {
                    'token_address': token_data.get('tokenContractAddress', ''),
                    'token_symbol': token_data.get('tokenSymbol', ''),
                    'token_name': token_data.get('tokenName', ''),
                    'decimals': int(token_data.get('tokenDecimal', 18)),
                    'total_supply': token_data.get('totalSupply', ''),
                    'logo_url': token_data.get('tokenLogoUrl', ''),
                    'website': token_data.get('website', ''),
                    'description': token_data.get('description', '')
                }
            else:
                logger.warning("get_token_info_failed", token=token_address, error=result.get('msg'))
                return {}
                
        except Exception as e:
            logger.error("get_token_info_error", token=token_address, error=str(e))
            return {}
    
    async def _make_request(self, method: str, url: str, params: Dict[str, Any] = None, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """發起 API 請求"""
        if not self.session:
            raise DataProviderException("HTTP session 未初始化")
        
        try:
            kwargs = {}
            if params:
                kwargs['params'] = params
            if data:
                kwargs['json'] = data
                
            async with self.session.request(method, url, **kwargs) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise DataProviderException(f"OKX API 請求失敗: {response.status} - {error_text}")
                
                result = await response.json()
                return result
                
        except aiohttp.ClientError as e:
            raise DataProviderException(f"網絡請求失敗: {e}")
        except Exception as e:
            raise DataProviderException(f"請求執行失敗: {e}")
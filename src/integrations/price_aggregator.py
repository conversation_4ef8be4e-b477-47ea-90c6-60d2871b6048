"""
價格聚合器 - 多重價格源備選方案
當 OKX API 失敗時，使用其他價格源獲取代幣價格
"""

import asyncio
import aiohttp
import structlog
from typing import Dict, List, Optional, Any
from decimal import Decimal

logger = structlog.get_logger(__name__)


class PriceAggregator:
    """價格聚合器 - 整合多個價格源"""
    
    def __init__(self):
        self.session = None
        
        # PancakeSwap V2 Router 地址
        self.pancake_router = "0x10ED43C718714eb63d5aA57B78B54704E256024E"
        
        # WBNB 地址
        self.wbnb_address = "0xbb4CdB9CBd36B01bD1cBaeBF2De08d9173bc095c"
        
        # 穩定幣地址用於價格計算
        self.stablecoins = {
            "0x55d398326f99059ff775485246999027b3197955": "USDT",
            "0xe9e7cea3dedca5984780bafc599bd69add087d56": "BUSD", 
            "0x8ac76a51cc950d9822d68b83fe1ad97b32cd580d": "USDC"
        }
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def get_token_price(self, token_address: str, chain_id: str = "56") -> Dict[str, Any]:
        """獲取單個代幣價格 - 多重備選方案"""
        
        price_sources = [
            self._get_price_from_coingecko,
            self._get_price_from_pancakeswap,
            self._get_price_from_bscscan_dex,
            self._get_price_from_dexscreener
        ]
        
        for source_func in price_sources:
            try:
                result = await source_func(token_address, chain_id)
                if result and result.get('price_usd', 0) > 0:
                    logger.info("price_source_success", 
                              token=token_address[:10], 
                              source=result.get('source'),
                              price=result.get('price_usd'))
                    return result
            except Exception as e:
                logger.warning("price_source_failed", 
                             source=source_func.__name__,
                             token=token_address[:10], 
                             error=str(e))
                continue
        
        # 所有價格源都失敗
        logger.error("all_price_sources_failed", token=token_address[:10])
        return {
            'price_usd': 0.0,
            'source': 'all_sources_failed',
            'timestamp': None
        }
    
    async def _get_price_from_coingecko(self, token_address: str, chain_id: str) -> Dict[str, Any]:
        """從 CoinGecko API 獲取價格"""
        
        # CoinGecko 的 BSC 平台 ID
        platform_id = "binance-smart-chain" if chain_id == "56" else "ethereum"
        
        url = f"https://api.coingecko.com/api/v3/simple/token_price/{platform_id}"
        params = {
            'contract_addresses': token_address.lower(),
            'vs_currencies': 'usd'
        }
        
        async with self.session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                token_data = data.get(token_address.lower(), {})
                price = token_data.get('usd', 0)
                
                if price > 0:
                    return {
                        'price_usd': float(price),
                        'source': 'coingecko',
                        'timestamp': None
                    }
        
        raise Exception("CoinGecko API 未返回有效價格")
    
    async def _get_price_from_pancakeswap(self, token_address: str, chain_id: str) -> Dict[str, Any]:
        """從 PancakeSwap 通過 WBNB 計算價格"""
        
        # 如果是 WBNB 本身，直接返回 BNB 價格
        if token_address.lower() == self.wbnb_address.lower():
            bnb_price = await self._get_bnb_price_fallback()
            return {
                'price_usd': bnb_price,
                'source': 'pancakeswap_wbnb',
                'timestamp': None
            }
        
        # 嘗試通過 PancakeSwap API 獲取價格
        try:
            url = "https://api.pancakeswap.info/api/v2/tokens"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    token_data = data.get('data', {}).get(token_address.lower())
                    
                    if token_data and token_data.get('price'):
                        price_bnb = float(token_data['price'])
                        bnb_price = await self._get_bnb_price_fallback()
                        price_usd = price_bnb * bnb_price
                        
                        return {
                            'price_usd': price_usd,
                            'source': 'pancakeswap_api',
                            'timestamp': None
                        }
        except Exception as e:
            logger.warning("pancakeswap_api_failed", error=str(e))
        
        raise Exception("PancakeSwap API 未返回有效價格")
    
    async def _get_price_from_bscscan_dex(self, token_address: str, chain_id: str) -> Dict[str, Any]:
        """通過 BSCScan 和 DEX 價格計算"""
        
        # 嘗試從 BSCScan 獲取代幣的流動性池信息
        # 這裡簡化實現，實際可以查詢 PancakeSwap 工廠合約來找到交易對
        
        # 先嘗試與 USDT 的交易對
        for stable_addr, stable_symbol in self.stablecoins.items():
            try:
                # 這裡可以實現更複雜的邏輯來查詢流動性池
                # 暫時返回一個占位符
                pass
            except Exception:
                continue
        
        raise Exception("BSCScan DEX 價格查詢失敗")
    
    async def _get_price_from_dexscreener(self, token_address: str, chain_id: str) -> Dict[str, Any]:
        """從 DexScreener API 獲取價格"""
        
        network = "bsc" if chain_id == "56" else "ethereum"
        url = f"https://api.dexscreener.com/latest/dex/tokens/{token_address}"
        
        async with self.session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                pairs = data.get('pairs', [])
                
                # 找到流動性最高的交易對
                best_pair = None
                max_liquidity = 0
                
                for pair in pairs:
                    if pair.get('chainId') == network:
                        liquidity = float(pair.get('liquidity', {}).get('usd', 0))
                        if liquidity > max_liquidity:
                            max_liquidity = liquidity
                            best_pair = pair
                
                if best_pair and best_pair.get('priceUsd'):
                    price = float(best_pair['priceUsd'])
                    return {
                        'price_usd': price,
                        'source': 'dexscreener',
                        'timestamp': None,
                        'liquidity_usd': max_liquidity
                    }
        
        raise Exception("DexScreener API 未返回有效價格")
    
    async def _get_bnb_price_fallback(self) -> float:
        """獲取 BNB 價格的備選方案"""
        
        bnb_sources = [
            ("https://api.coingecko.com/api/v3/simple/price?ids=binancecoin&vs_currencies=usd", 
             lambda data: data['binancecoin']['usd']),
            ("https://api.binance.com/api/v3/ticker/price?symbol=BNBUSDT", 
             lambda data: float(data['price']))
        ]
        
        for url, extract_func in bnb_sources:
            try:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        price = extract_func(data)
                        if price > 0:
                            return float(price)
            except Exception as e:
                logger.warning("bnb_price_source_failed", url=url, error=str(e))
                continue
        
        # 如果所有 API 都失敗，返回一個合理的默認值
        logger.warning("all_bnb_price_sources_failed_using_fallback")
        return 600.0  # 默認 BNB 價格
    
    async def get_token_info_from_contract(self, token_address: str, bscscan_api_key: str) -> Dict[str, Any]:
        """從合約獲取代幣基本信息（name, symbol, decimals）"""
        
        # BSCScan API 端點
        base_url = "https://api.bscscan.com/api"
        
        # 獲取代幣符號
        symbol_params = {
            'module': 'contract',
            'action': 'getsourcecode',
            'address': token_address,
            'apikey': bscscan_api_key
        }
        
        try:
            async with self.session.get(base_url, params=symbol_params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('status') == '1' and data.get('result'):
                        contract_info = data['result'][0]
                        contract_name = contract_info.get('ContractName', 'Unknown')
                        
                        # 嘗試從合約名稱推斷代幣符號
                        symbol = contract_name if contract_name != 'Unknown' else f'TOKEN_{token_address[-6:].upper()}'
                        
                        return {
                            'symbol': symbol,
                            'name': contract_name,
                            'decimals': 18,  # 默認值
                            'source': 'bscscan_contract'
                        }
        except Exception as e:
            logger.warning("get_token_info_failed", address=token_address, error=str(e))
        
        # 返回默認值
        return {
            'symbol': f'TOKEN_{token_address[-6:].upper()}',
            'name': 'Unknown Token',
            'decimals': 18,
            'source': 'default'
        }
    
    async def get_multiple_token_prices(self, token_addresses: List[str], chain_id: str = "56") -> Dict[str, Dict[str, Any]]:
        """批量獲取多個代幣價格"""
        
        results = {}
        
        # 並行獲取價格
        tasks = []
        for address in token_addresses:
            task = asyncio.create_task(self.get_token_price(address, chain_id))
            tasks.append((address, task))
        
        # 等待所有任務完成
        for address, task in tasks:
            try:
                result = await task
                results[address] = result
            except Exception as e:
                logger.error("batch_price_fetch_failed", address=address, error=str(e))
                results[address] = {
                    'price_usd': 0.0,
                    'source': 'batch_fetch_failed',
                    'timestamp': None
                }
        
        return results
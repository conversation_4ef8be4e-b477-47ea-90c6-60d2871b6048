"""
Ollama LLM 客户端
提供异步 LLM 调用接口，支持错误处理和重试机制
"""

import asyncio
import json
from typing import Dict, Any, Optional, List
import aiohttp
import structlog
from datetime import datetime, timedelta

from ..utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)


class OllamaClient:
    """Ollama LLM 客户端"""
    
    def __init__(self, 
                 base_url: str = "http://localhost:11434",
                 default_model: str = "qwen2.5:7b",
                 default_temperature: float = 0.2,
                 timeout: int = 60,
                 max_retries: int = 3,
                 retry_delay: float = 1.0):
        """
        初始化 Ollama 客户端
        
        Args:
            base_url: Ollama 服务器地址
            default_model: 默认模型名称
            default_temperature: 默认温度参数
            timeout: 请求超时时间(秒)
            max_retries: 最大重试次数
            retry_delay: 重试延迟时间(秒)
        """
        self.base_url = base_url.rstrip('/')
        self.default_model = default_model
        self.default_temperature = default_temperature
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens': 0,
            'total_time': 0.0,
            'last_request_time': None
        }
        
        # 模型缓存
        self._available_models: Optional[List[str]] = None
        self._models_cache_time: Optional[datetime] = None
        self._models_cache_ttl = timedelta(minutes=30)
    
    async def generate(self, 
                      prompt: str,
                      model: Optional[str] = None,
                      temperature: Optional[float] = None,
                      max_tokens: Optional[int] = None,
                      system_prompt: Optional[str] = None,
                      format_json: bool = False) -> str:
        """
        生成文本响应
        
        Args:
            prompt: 用户提示词
            model: 使用的模型，默认使用 default_model
            temperature: 温度参数，控制随机性
            max_tokens: 最大token数量
            system_prompt: 系统提示词
            format_json: 是否强制JSON格式输出
            
        Returns:
            生成的文本响应
            
        Raises:
            DyFlowException: LLM调用失败
        """
        model = model or self.default_model
        temperature = temperature if temperature is not None else self.default_temperature
        
        # 构建请求payload
        payload = {
            "model": model,
            "prompt": prompt,
            "temperature": temperature,
            "stream": False,
            "options": {}
        }
        
        # 添加系统提示词
        if system_prompt:
            payload["system"] = system_prompt
        
        # 设置最大token数
        if max_tokens:
            payload["options"]["num_predict"] = max_tokens
        
        # JSON格式输出
        if format_json:
            payload["format"] = "json"
        
        return await self._make_request("/api/generate", payload)
    
    async def chat(self, 
                   messages: List[Dict[str, str]],
                   model: Optional[str] = None,
                   temperature: Optional[float] = None,
                   max_tokens: Optional[int] = None,
                   format_json: bool = False) -> str:
        """
        对话式生成
        
        Args:
            messages: 消息列表，格式为 [{"role": "user", "content": "..."}, ...]
            model: 使用的模型
            temperature: 温度参数
            max_tokens: 最大token数量
            format_json: 是否强制JSON格式输出
            
        Returns:
            生成的响应文本
        """
        model = model or self.default_model
        temperature = temperature if temperature is not None else self.default_temperature
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "stream": False,
            "options": {}
        }
        
        if max_tokens:
            payload["options"]["num_predict"] = max_tokens
            
        if format_json:
            payload["format"] = "json"
        
        response = await self._make_request("/api/chat", payload)
        
        # 如果返回的是完整响应，提取message内容
        try:
            response_data = json.loads(response)
            if "message" in response_data and "content" in response_data["message"]:
                return response_data["message"]["content"]
        except json.JSONDecodeError:
            pass
            
        return response
    
    async def _make_request(self, endpoint: str, payload: Dict[str, Any]) -> str:
        """
        发送HTTP请求到Ollama服务器
        
        Args:
            endpoint: API端点
            payload: 请求payload
            
        Returns:
            响应内容
            
        Raises:
            DyFlowException: 请求失败
        """
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(self.max_retries + 1):
            try:
                start_time = datetime.now()
                
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        url,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=self.timeout)
                    ) as response:
                        
                        if response.status == 200:
                            response_text = await response.text()
                            
                            # 更新统计信息
                            end_time = datetime.now()
                            duration = (end_time - start_time).total_seconds()
                            
                            self._update_stats(True, duration, response_text)
                            
                            # 解析响应
                            try:
                                response_data = json.loads(response_text)
                                
                                # 处理generate API响应
                                if "response" in response_data:
                                    return response_data["response"]
                                
                                # 处理chat API响应
                                elif "message" in response_data:
                                    return response_data["message"]["content"]
                                
                                # 其他情况返回原始文本
                                else:
                                    return response_text
                                    
                            except json.JSONDecodeError:
                                # 如果无法解析JSON，返回原始文本
                                return response_text
                        
                        else:
                            error_text = await response.text()
                            error_msg = f"HTTP {response.status}: {error_text}"
                            
                            if attempt < self.max_retries:
                                logger.warning("llm_request_failed_retrying",
                                             attempt=attempt + 1,
                                             max_retries=self.max_retries,
                                             error=error_msg)
                                await asyncio.sleep(self.retry_delay * (2 ** attempt))
                                continue
                            else:
                                self._update_stats(False)
                                raise DyFlowException(f"LLM请求失败: {error_msg}")
            
            except asyncio.TimeoutError:
                error_msg = f"请求超时 (超过 {self.timeout} 秒)"
                
                if attempt < self.max_retries:
                    logger.warning("llm_request_timeout_retrying",
                                 attempt=attempt + 1,
                                 max_retries=self.max_retries)
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    self._update_stats(False)
                    raise DyFlowException(f"LLM请求超时: {error_msg}")
            
            except Exception as e:
                error_msg = str(e)
                
                if attempt < self.max_retries:
                    logger.warning("llm_request_error_retrying",
                                 attempt=attempt + 1,
                                 max_retries=self.max_retries,
                                 error=error_msg)
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
                    continue
                else:
                    self._update_stats(False)
                    raise DyFlowException(f"LLM请求异常: {error_msg}")
        
        # 不应该到达这里
        self._update_stats(False)
        raise DyFlowException("LLM请求失败: 超过最大重试次数")
    
    def _update_stats(self, success: bool, duration: float = 0.0, response: str = "") -> None:
        """更新统计信息"""
        self.stats['total_requests'] += 1
        self.stats['last_request_time'] = datetime.now()
        
        if success:
            self.stats['successful_requests'] += 1
            self.stats['total_time'] += duration
            
            # 估算token数量（粗略估计：4个字符=1个token）
            estimated_tokens = len(response) // 4
            self.stats['total_tokens'] += estimated_tokens
        else:
            self.stats['failed_requests'] += 1
    
    async def get_available_models(self, force_refresh: bool = False) -> List[str]:
        """
        获取可用模型列表
        
        Args:
            force_refresh: 是否强制刷新缓存
            
        Returns:
            可用模型名称列表
        """
        now = datetime.now()
        
        # 检查缓存
        if (not force_refresh and 
            self._available_models is not None and 
            self._models_cache_time is not None and
            now - self._models_cache_time < self._models_cache_ttl):
            return self._available_models
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.base_url}/api/tags",
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    
                    if response.status == 200:
                        data = await response.json()
                        models = [model["name"] for model in data.get("models", [])]
                        
                        # 更新缓存
                        self._available_models = models
                        self._models_cache_time = now
                        
                        logger.info("available_models_updated", models=models)
                        return models
                    else:
                        error_text = await response.text()
                        logger.error("failed_to_get_models", 
                                   status=response.status, 
                                   error=error_text)
                        return []
        
        except Exception as e:
            logger.error("failed_to_get_models", error=str(e))
            return []
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        try:
            # 测试简单请求
            test_prompt = "测试连接"
            start_time = datetime.now()
            
            response = await self.generate(test_prompt)
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            # 获取可用模型
            models = await self.get_available_models()
            
            return {
                'status': 'healthy',
                'base_url': self.base_url,
                'default_model': self.default_model,
                'response_time': response_time,
                'available_models': models,
                'stats': self.get_stats()
            }
        
        except Exception as e:
            return {
                'status': 'unhealthy',
                'base_url': self.base_url,
                'error': str(e),
                'stats': self.get_stats()
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        # 计算成功率
        if stats['total_requests'] > 0:
            stats['success_rate'] = stats['successful_requests'] / stats['total_requests']
        else:
            stats['success_rate'] = 0.0
        
        # 计算平均响应时间
        if stats['successful_requests'] > 0:
            stats['avg_response_time'] = stats['total_time'] / stats['successful_requests']
        else:
            stats['avg_response_time'] = 0.0
        
        # 格式化最后请求时间
        if stats['last_request_time']:
            stats['last_request_time'] = stats['last_request_time'].isoformat()
        
        return stats
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_tokens': 0,
            'total_time': 0.0,
            'last_request_time': None
        }
        logger.info("llm_stats_reset")
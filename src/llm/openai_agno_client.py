"""
OpenAI + Agno Framework 集成客户端
基于现有的 wallet_analyzer_agno.py 架构模式
为Dy-Flow v3 Agent提供智能化决策支持
"""

import asyncio
import os
from datetime import datetime
from typing import Dict, Any, Optional, List
import structlog

from ..utils.exceptions import DyFlowException

# Agno Framework 导入
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    Agent = None
    OpenAIChat = None

logger = structlog.get_logger(__name__)


class DyFlowAgnoClient:
    """基于Agno框架的DyFlow智能客户端"""
    
    def __init__(self, 
                 agent_name: str,
                 openai_api_key: str,
                 model: str = "gpt-4o-mini"):
        """
        初始化DyFlow Agno客户端
        
        Args:
            agent_name: Agent名称
            openai_api_key: OpenAI API密钥
            model: 使用的模型名称
        """
        if not AGNO_AVAILABLE:
            raise DyFlowException("Agno framework not available. Please install: pip install agno")
        
        self.agent_name = agent_name
        self.openai_api_key = openai_api_key
        self.model = model
        self.agno_agent = None
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0
        }
        
        logger.info("dyflow_agno_client_initialized",
                   agent_name=agent_name,
                   model=model)
    
    async def initialize(self) -> None:
        """初始化Agno Agent"""
        try:
            self.agno_agent = Agent(
                name=f"DyFlow {self.agent_name}",
                description=f"Dy-Flow v3 {self.agent_name} 智能分析专家",
                model=OpenAIChat(
                    id=self.model,
                    api_key=self.openai_api_key
                ),
                instructions=[
                    f"你是Dy-Flow v3系统的{self.agent_name}智能助手",
                    "专门处理DeFi投资分析和决策支持",
                    "始终提供结构化的JSON响应",
                    "包含详细的分析reasoning",
                    "标明置信度和风险等级"
                ],
                show_tool_calls=True,
                markdown=True
            )
            
            logger.info("agno_agent_initialized", agent_name=self.agent_name)
            
        except Exception as e:
            logger.error("agno_agent_initialization_failed", error=str(e))
            raise DyFlowException(f"Failed to initialize Agno agent: {e}")
    
    async def analyze_pools(self, pools_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析池子数据
        
        Args:
            pools_data: 池子数据列表
            
        Returns:
            分析结果
        """
        if not self.agno_agent:
            await self.initialize()
        
        self.stats['total_requests'] += 1
        start_time = datetime.now()
        
        try:
            # 构建分析请求
            message = self._build_pool_analysis_message(pools_data)
            
            # 调用Agno Agent
            response = await self.agno_agent.arun(message)
            
            # 记录成功
            self.stats['successful_requests'] += 1
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info("pool_analysis_success",
                       agent_name=self.agent_name,
                       pools_count=len(pools_data),
                       duration=duration)
            
            return {
                "success": True,
                "analysis": response.content if hasattr(response, 'content') else str(response),
                "pools_analyzed": len(pools_data),
                "duration": duration,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error("pool_analysis_failed", error=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "pools_analyzed": len(pools_data),
                "timestamp": datetime.now().isoformat()
            }
    
    async def handle_complex_planning(self, situation: Dict[str, Any], plans: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理复杂规划情况（needs_llm=True的情况）
        
        Args:
            situation: 复杂情况描述
            plans: 现有计划列表
            
        Returns:
            处理建议
        """
        if not self.agno_agent:
            await self.initialize()
        
        self.stats['total_requests'] += 1
        start_time = datetime.now()
        
        try:
            # 构建复杂情况分析请求
            message = self._build_complex_planning_message(situation, plans)
            
            # 调用Agno Agent
            response = await self.agno_agent.arun(message)
            
            # 记录成功
            self.stats['successful_requests'] += 1
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info("complex_planning_success",
                       agent_name=self.agent_name,
                       plans_count=len(plans),
                       duration=duration)
            
            return {
                "success": True,
                "recommendation": response.content if hasattr(response, 'content') else str(response),
                "plans_analyzed": len(plans),
                "duration": duration,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error("complex_planning_failed", error=str(e))
            
            return {
                "success": False,
                "error": str(e),
                "plans_analyzed": len(plans),
                "timestamp": datetime.now().isoformat()
            }
    
    def _build_pool_analysis_message(self, pools_data: List[Dict[str, Any]]) -> str:
        """构建池子分析消息"""
        message = f"""请分析以下{len(pools_data)}个DeFi流动性池，并提供投资建议：

"""
        
        # 添加池子详细信息
        for i, pool in enumerate(pools_data[:5], 1):  # 只显示前5个
            message += f"""池子{i}:
- ID: {pool.get('id', 'N/A')[:10]}...
- 链: {pool.get('chain', 'N/A')}
- TVL: ${pool.get('tvl_usd', 0):,.0f}
- 24h费用: ${pool.get('fee24h', 0):,.2f}
- 费率年化: {pool.get('fee_tvl', 0):.2f}%

"""
        
        if len(pools_data) > 5:
            message += f"... 还有{len(pools_data) - 5}个池子待分析\n\n"
        
        message += """请提供JSON格式的分析结果，包含：
1. 每个池子的风险评分（1-10，10最安全）
2. 推荐投资策略（Delta-Neutral/Ladder/Passive）
3. 预期年化收益率
4. 主要风险因素
5. 投资优先级排序

输出格式：
```json
{
  "analysis_summary": {
    "total_pools": number,
    "recommended_pools": number,
    "average_risk_score": number
  },
  "pool_recommendations": [
    {
      "pool_id": "string",
      "risk_score": number,
      "strategy": "string",
      "expected_apy": number,
      "priority": "high/medium/low",
      "reasoning": "string"
    }
  ]
}
```
"""
        return message
    
    def _build_complex_planning_message(self, situation: Dict[str, Any], plans: List[Dict[str, Any]]) -> str:
        """构建复杂规划分析消息"""
        message = f"""遇到复杂投资情况需要你的专业分析：

复杂情况：
{situation}

当前计划（{len(plans)}个）：
"""
        
        for i, plan in enumerate(plans, 1):
            message += f"""计划{i}:
- 池子ID: {plan.get('pool_id', 'N/A')[:10]}...
- 策略: {plan.get('strategy', 'N/A')}
- 行动: {plan.get('action', 'N/A')}
- 参数: {plan.get('params', {})}

"""
        
        message += """请提供JSON格式的专业建议：

```json
{
  "situation_analysis": {
    "complexity_level": "high/medium/low",
    "main_risks": ["risk1", "risk2"],
    "market_conditions": "string"
  },
  "recommendations": {
    "immediate_actions": ["action1", "action2"],
    "plan_modifications": [
      {
        "plan_id": "string",
        "suggested_changes": "string",
        "new_parameters": {}
      }
    ],
    "risk_mitigation": "string",
    "confidence_level": number
  }
}
```
"""
        return message
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "agent_name": self.agent_name,
            "model": self.model,
            "initialized": self.agno_agent is not None,
            **self.stats
        }


class DyFlowAgnoManager:
    """DyFlow Agno客户端管理器"""
    
    def __init__(self, openai_api_key: str):
        self.openai_api_key = openai_api_key
        self.clients: Dict[str, DyFlowAgnoClient] = {}
    
    async def get_pool_analyzer(self) -> DyFlowAgnoClient:
        """获取池子分析客户端"""
        if "pool_analyzer" not in self.clients:
            self.clients["pool_analyzer"] = DyFlowAgnoClient(
                agent_name="PoolAnalyzer",
                openai_api_key=self.openai_api_key
            )
            await self.clients["pool_analyzer"].initialize()
        
        return self.clients["pool_analyzer"]
    
    async def get_strategy_planner(self) -> DyFlowAgnoClient:
        """获取策略规划客户端"""
        if "strategy_planner" not in self.clients:
            self.clients["strategy_planner"] = DyFlowAgnoClient(
                agent_name="StrategyPlanner",
                openai_api_key=self.openai_api_key
            )
            await self.clients["strategy_planner"].initialize()
        
        return self.clients["strategy_planner"]
    
    async def get_risk_analyst(self) -> DyFlowAgnoClient:
        """获取风险分析客户端"""
        if "risk_analyst" not in self.clients:
            self.clients["risk_analyst"] = DyFlowAgnoClient(
                agent_name="RiskAnalyst", 
                openai_api_key=self.openai_api_key
            )
            await self.clients["risk_analyst"].initialize()
        
        return self.clients["risk_analyst"]
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有客户端统计"""
        return {
            "total_clients": len(self.clients),
            "client_stats": {name: client.get_stats() for name, client in self.clients.items()}
        }


# 全局管理器实例
_global_manager: Optional[DyFlowAgnoManager] = None

def get_agno_manager(openai_api_key: Optional[str] = None) -> DyFlowAgnoManager:
    """获取全局Agno管理器"""
    global _global_manager
    
    if _global_manager is None:
        if not openai_api_key:
            openai_api_key = os.getenv("OPENAI_API_KEY")
        
        if not openai_api_key:
            raise DyFlowException("OpenAI API key not provided")
        
        _global_manager = DyFlowAgnoManager(openai_api_key)
    
    return _global_manager
"""
LLM 提示词管理模块
集中管理 Scorer 和 Planner Agent 的提示词模板
"""

from typing import Dict, Any, Optional
from datetime import datetime


class PromptManager:
    """LLM 提示词管理器"""
    
    def __init__(self):
        """初始化提示词管理器"""
        self.scorer_prompts = self._init_scorer_prompts()
        self.planner_prompts = self._init_planner_prompts()
        self.common_prompts = self._init_common_prompts()
    
    def _init_scorer_prompts(self) -> Dict[str, str]:
        """初始化评分器提示词"""
        return {
            'pool_analysis': """你是一个专业的DeFi流动性挖矿分析师。请分析以下流动性池子的数据，并提供综合评估。

## 池子信息
- 交易对: {token_a}/{token_b}
- 区块链: {chain}
- APR: {apr:.2f}%
- TVL: ${tvl:,.2f}
- 24小时交易量: ${volume_24h:,.2f}
- 手续费率: {fee_rate:.4f}%
- 流动性深度评估: {depth_score:.2f}
- 价格波动率估算: {volatility:.2f}%
- 无常损失风险: {il_risk:.2f}%

## 分析要求
请基于以下因素进行深度分析：

1. **收益潜力分析**
   - APR的可持续性
   - 手续费收入预期
   - 奖励代币价值

2. **流动性质量评估**
   - TVL规模和稳定性
   - 交易活跃度
   - 市场深度

3. **风险因素识别**
   - 无常损失风险
   - 价格波动风险
   - 智能合约风险
   - 流动性枯竭风险

4. **市场地位分析**
   - 代币质量和信誉
   - 项目发展前景
   - 竞争优势

## 输出要求
请返回严格的JSON格式：
```json
{{
    "score": 0-100的综合评分,
    "risk_level": "low/medium/high/critical",
    "analysis": {{
        "strengths": ["优势1", "优势2", "优势3"],
        "weaknesses": ["劣势1", "劣势2"],
        "opportunities": ["机会1", "机会2"],
        "threats": ["威胁1", "威胁2"]
    }},
    "recommendation": {{
        "action": "strong_buy/buy/hold/sell/strong_sell",
        "reasoning": "具体投资建议理由",
        "target_allocation": "建议分配比例(0.05-0.30)",
        "time_horizon": "short/medium/long"
    }},
    "confidence": 0.0-1.0的置信度,
    "key_metrics": {{
        "yield_attractiveness": 0.0-1.0,
        "risk_adjusted_return": 0.0-1.0,
        "liquidity_quality": 0.0-1.0,
        "market_stability": 0.0-1.0
    }}
}}
```

## 注意事项
- 评分要客观公正，基于数据分析
- 风险评估要保守谨慎
- 投资建议要考虑风险收益平衡
- 置信度要反映数据质量和分析确定性""",

            'risk_assessment': """作为DeFi风险管理专家，请对以下流动性池进行风险评估：

## 池子数据
{pool_data}

## 风险评估维度

1. **市场风险**
   - 价格波动风险
   - 流动性风险
   - 相关性风险

2. **技术风险**
   - 智能合约风险
   - 协议风险
   - 基础设施风险

3. **操作风险**
   - 治理风险
   - 监管风险
   - 竞争风险

请返回JSON格式：
```json
{{
    "overall_risk_level": "low/medium/high/critical",
    "risk_score": 0-100的风险评分,
    "risk_factors": [
        {{
            "category": "市场/技术/操作",
            "factor": "具体风险因素",
            "severity": "low/medium/high/critical",
            "probability": 0.0-1.0,
            "impact": 0.0-1.0
        }}
    ],
    "mitigation_strategies": ["策略1", "策略2"],
    "monitoring_indicators": ["指标1", "指标2"],
    "exit_conditions": ["条件1", "条件2"]
}}
```""",

            'comparative_analysis': """请对以下多个流动性池进行比较分析：

## 候选池子
{pools_data}

## 比较维度
1. 收益率对比
2. 风险评估对比
3. 流动性质量对比
4. 市场前景对比

请返回JSON格式的比较结果：
```json
{{
    "ranking": [
        {{
            "pool_id": "池子ID",
            "rank": 排名,
            "overall_score": 综合评分,
            "key_advantages": ["优势1", "优势2"],
            "main_concerns": ["顾虑1", "顾虑2"]
        }}
    ],
    "recommendations": {{
        "top_pick": "最佳选择的池子ID",
        "diversification_combo": ["建议组合的池子ID列表"],
        "avoid_list": ["不建议的池子ID列表"]
    }}
}}
```"""
        }
    
    def _init_planner_prompts(self) -> Dict[str, str]:
        """初始化规划器提示词"""
        return {
            'strategy_planning': """你是一个专业的DeFi投资策略规划师。请基于以下信息制定最优的流动性挖矿投资策略。

## 市场分析
### 高分池子
{top_pools}

### 当前持仓
{current_positions}

### 资金状况
- 总资金: ${total_value:,.2f}
- 可用现金: ${available_cash:,.2f}
- 已投资金额: ${invested_value:,.2f}
- 当前敞口: {current_exposure:.1%}
- 持仓数量: {position_count}

### 市场环境
{market_analysis}

## 策略约束
- 单个位置最大占比: {max_position_size:.1%}
- 总投资敞口上限: {max_total_exposure:.1%}
- 最小评分要求: {min_score_threshold}
- 最大持仓数量: {max_positions}
- 最小单笔投资: ${min_position_value:,.0f}
- 最低置信度: {min_confidence:.1%}

## 策略目标
1. **收益最大化**: 在可接受风险下追求最高收益
2. **风险控制**: 严格控制下行风险和敞口
3. **分散投资**: 避免过度集中投资
4. **流动性管理**: 保持适当的资金流动性

## 决策类型
- **enter**: 开仓新投资
- **exit**: 清仓离场
- **rebalance**: 调整仓位
- **hold**: 保持现状

请返回JSON格式的策略决策：
```json
{{
    "strategy_summary": "整体策略描述和投资理念",
    "market_outlook": "市场前景判断和趋势分析",
    "actions": [
        {{
            "action": "enter/exit/rebalance/hold",
            "pool_id": "池子ID",
            "chain": "区块链",
            "token_pair": "交易对名称",
            "amount_usd": 投资金额,
            "allocation_percent": "占总资金比例",
            "confidence": 0.0-1.0置信度,
            "priority": 1-5优先级(1最高),
            "reasoning": "详细决策理由",
            "risk_level": "low/medium/high",
            "expected_apr": "预期年化收益率",
            "time_horizon": "short/medium/long投资期限",
            "stop_loss": "止损条件",
            "take_profit": "止盈条件"
        }}
    ],
    "portfolio_allocation": {{
        "target_cash_ratio": "目标现金比例",
        "target_risk_exposure": "目标风险敞口",
        "sector_diversification": "行业分散情况"
    }},
    "risk_management": {{
        "overall_risk_level": "整体风险等级",
        "max_drawdown_estimate": "最大回撤预估",
        "correlation_analysis": "持仓相关性分析",
        "hedging_suggestions": ["对冲建议"]
    }},
    "monitoring_plan": {{
        "key_indicators": ["关键监控指标"],
        "review_frequency": "复查频率",
        "trigger_conditions": ["触发调整的条件"]
    }}
}}
```

## 重要提醒
- 所有金额必须为正数且合理
- 确保资金分配不超过可用现金
- 优先级设置要合理，重要决策设为1-2
- 置信度要基于分析质量和市场确定性
- 考虑交易成本和滑点影响""",

            'position_management': """请对当前投资组合进行管理决策分析：

## 当前持仓详情
{portfolio_details}

## 市场变化
{market_changes}

## 绩效分析
{performance_analysis}

请评估每个持仓并提供管理建议：
```json
{{
    "portfolio_health": {{
        "overall_score": 0-100总体健康度,
        "risk_level": "当前风险等级",
        "diversification_score": "分散化评分",
        "performance_summary": "绩效总结"
    }},
    "position_actions": [
        {{
            "pool_id": "池子ID",
            "current_action": "hold/reduce/increase/exit",
            "reasoning": "操作理由",
            "urgency": "low/medium/high",
            "target_adjustment": "目标调整幅度"
        }}
    ],
    "rebalancing_plan": {{
        "need_rebalancing": true/false,
        "rebalancing_urgency": "low/medium/high",
        "suggested_changes": ["具体调整建议"]
    }}
}}
```""",

            'risk_scenario_analysis': """请进行风险情景分析，评估不同市场条件下的投资组合表现：

## 投资组合
{portfolio_data}

## 情景设定
1. **牛市情景**: 市场上涨20%，流动性充足
2. **熊市情景**: 市场下跌30%，流动性紧张
3. **波动情景**: 高波动率环境，频繁震荡
4. **黑天鹅事件**: 重大负面事件冲击

请返回情景分析结果：
```json
{{
    "scenario_analysis": [
        {{
            "scenario": "情景名称",
            "probability": "发生概率",
            "portfolio_impact": {{
                "expected_return": "预期收益率",
                "max_drawdown": "最大回撤",
                "liquidity_impact": "流动性影响",
                "risk_level": "风险等级"
            }},
            "recommended_actions": ["应对建议"]
        }}
    ],
    "stress_test_results": {{
        "worst_case_loss": "最坏情况损失",
        "recovery_time_estimate": "恢复时间预估",
        "resilience_score": "抗风险能力评分"
    }}
}}
```"""
        }
    
    def _init_common_prompts(self) -> Dict[str, str]:
        """初始化通用提示词"""
        return {
            'system_role_scorer': """你是一个专业的DeFi分析师，具有以下特征：
- 深度理解流动性挖矿机制和风险
- 熟悉各种DeFi协议和代币经济学
- 擅长数据分析和风险评估
- 保持客观理性的分析态度
- 注重风险控制和收益平衡

请始终以专业、客观的态度进行分析，提供基于数据的洞察。""",

            'system_role_planner': """你是一个经验丰富的DeFi投资策略师，具备：
- 丰富的投资组合管理经验
- 深度的DeFi市场理解
- 卓越的风险管理能力
- 数据驱动的决策方法
- 长期价值投资理念

请制定科学合理的投资策略，平衡收益与风险，确保投资组合的长期稳健增长。""",

            'json_format_reminder': """
重要提醒：
1. 必须严格按照JSON格式返回结果
2. 所有数值必须为有效数字，不能包含特殊字符
3. 字符串字段不能为空，必须提供有意义的内容
4. 枚举值必须在指定范围内选择
5. 数组字段至少包含一个元素
6. 确保JSON语法正确，可以被解析

如果无法确定某个值，请使用合理的默认值。""",

            'market_context': """
当前DeFi市场环境：
- 整体TVL: 处于{tvl_trend}趋势
- 收益率环境: {yield_environment}
- 风险偏好: {risk_appetite}
- 监管环境: {regulatory_environment}
- 技术发展: {tech_development}

请结合这些宏观因素进行分析。"""
        }
    
    def get_scorer_prompt(self, prompt_type: str, **kwargs) -> str:
        """
        获取评分器提示词
        
        Args:
            prompt_type: 提示词类型
            **kwargs: 格式化参数
            
        Returns:
            格式化后的提示词
        """
        if prompt_type not in self.scorer_prompts:
            raise ValueError(f"未知的评分器提示词类型: {prompt_type}")
        
        template = self.scorer_prompts[prompt_type]
        return template.format(**kwargs)
    
    def get_planner_prompt(self, prompt_type: str, **kwargs) -> str:
        """
        获取规划器提示词
        
        Args:
            prompt_type: 提示词类型
            **kwargs: 格式化参数
            
        Returns:
            格式化后的提示词
        """
        if prompt_type not in self.planner_prompts:
            raise ValueError(f"未知的规划器提示词类型: {prompt_type}")
        
        template = self.planner_prompts[prompt_type]
        return template.format(**kwargs)
    
    def get_system_prompt(self, agent_type: str) -> str:
        """
        获取系统角色提示词
        
        Args:
            agent_type: Agent类型 ('scorer' 或 'planner')
            
        Returns:
            系统提示词
        """
        key = f"system_role_{agent_type}"
        if key not in self.common_prompts:
            return self.common_prompts.get("system_role_scorer", "")
        
        return self.common_prompts[key]
    
    def build_comprehensive_prompt(self, 
                                 agent_type: str,
                                 prompt_type: str,
                                 context_data: Dict[str, Any],
                                 include_market_context: bool = True) -> Dict[str, str]:
        """
        构建完整的提示词，包括系统角色和主要内容
        
        Args:
            agent_type: Agent类型
            prompt_type: 提示词类型
            context_data: 上下文数据
            include_market_context: 是否包含市场环境信息
            
        Returns:
            包含system和user prompt的字典
        """
        # 系统提示词
        system_prompt = self.get_system_prompt(agent_type)
        
        # 主要提示词
        if agent_type == "scorer":
            main_prompt = self.get_scorer_prompt(prompt_type, **context_data)
        elif agent_type == "planner":
            main_prompt = self.get_planner_prompt(prompt_type, **context_data)
        else:
            raise ValueError(f"不支持的Agent类型: {agent_type}")
        
        # 添加市场环境信息
        if include_market_context:
            market_info = context_data.get('market_context', {})
            if market_info:
                market_context = self.common_prompts['market_context'].format(**market_info)
                main_prompt = market_context + "\n\n" + main_prompt
        
        # 添加JSON格式提醒
        main_prompt += "\n\n" + self.common_prompts['json_format_reminder']
        
        return {
            'system': system_prompt,
            'user': main_prompt
        }
    
    def validate_prompt_params(self, prompt_type: str, agent_type: str, params: Dict[str, Any]) -> bool:
        """
        验证提示词参数的完整性
        
        Args:
            prompt_type: 提示词类型
            agent_type: Agent类型
            params: 参数字典
            
        Returns:
            是否验证通过
        """
        try:
            if agent_type == "scorer":
                template = self.scorer_prompts.get(prompt_type, "")
            elif agent_type == "planner":
                template = self.planner_prompts.get(prompt_type, "")
            else:
                return False
            
            # 尝试格式化模板，检查是否有缺失的参数
            template.format(**params)
            return True
            
        except KeyError as e:
            missing_param = str(e).strip("'")
            print(f"缺失参数: {missing_param}")
            return False
        except Exception:
            return False
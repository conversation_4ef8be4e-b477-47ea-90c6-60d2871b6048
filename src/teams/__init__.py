"""
DyFlow Teams System
基于Agno Teams的LP监控和自动调整团队架构
"""

from .lp_monitoring_team import LPMonitoringTeam
from .lp_adjustment_team import LPAdjustmentTeam
from .risk_management_team import RiskManagementTeam
from .base_team import DyFlowBaseTeam


def create_lp_monitoring_team(chains, debug_mode=False):
    """创建LP监控团队"""
    team = LPMonitoringTeam(
        name="lp_monitoring_team",
        description="LP池子监控和数据收集团队",
        debug_mode=debug_mode
    )
    return team


def create_lp_adjustment_team(positions, debug_mode=False):
    """创建LP调整团队"""
    team = LPAdjustmentTeam(
        name="lp_adjustment_team",
        description="LP位置自动调整和重新平衡团队",
        debug_mode=debug_mode
    )
    return team


def create_risk_management_team(risk_level="medium", debug_mode=False):
    """创建风险管理团队"""
    team = RiskManagementTeam(
        name="risk_management_team",
        description="风险监控和自动对冲团队",
        debug_mode=debug_mode
    )
    return team


__all__ = [
    "DyFlowBaseTeam",
    "LPMonitoringTeam",
    "LPAdjustmentTeam",
    "RiskManagementTeam",
    "create_lp_monitoring_team",
    "create_lp_adjustment_team",
    "create_risk_management_team"
]

"""
PoolScannerTool - DeFi池子扫描工具
整合BSC PancakeSwap和Solana Meteora扫描功能，为Agno Framework提供标准化接口
"""

import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, Literal
from datetime import datetime
import structlog

try:
    from agno.sdk import Tool
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # 如果Agno不可用，创建一个基础的Tool类作为fallback
    class Tool:
        name = ""
        description = ""
        
        async def run(self, *args, **kwargs):
            raise NotImplementedError("Agno Framework not available")

logger = structlog.get_logger(__name__)


class PoolScannerTool(Tool):
    """DeFi池子扫描工具，整合BSC和Meteora功能"""
    
    name = "pool_scanner"
    description = "扫描BSC PancakeSwap和Solana Meteora上的DeFi池子数据"
    
    def __init__(self):
        self.bsc_api_endpoints = {
            'pools': 'https://api.pancakeswap.info/api/v2/pools',
            'tokens': 'https://api.pancakeswap.info/api/v2/tokens'
        }
        
        self.meteora_api_endpoints = {
            'pools': 'https://dlmm-api.meteora.ag/pair/all',
            'markets': 'https://dlmm-api.meteora.ag/market/all'
        }
        
        # 默认过滤条件
        self.default_filters = {
            'min_tvl': 50000,  # 最小TVL $50K
            'min_volume_24h': 10000,  # 最小24h交易量 $10K
            'max_pools': 50,  # 最大返回池子数量
            'min_fee_tvl': 5.0,  # 最小年化费率 5%
            'target_tokens': {
                'bsc': ['WBNB', 'WETH', 'BTCB', 'USDT', 'USDC', 'BUSD'],
                'solana': ['SOL', 'WSOL', 'USDC', 'USDT', 'BONK', 'WIF', 'JTO']
            }
        }
    
    async def run(self, chain: str, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        扫描指定链上的池子数据
        
        Args:
            chain: 'bsc' 或 'solana'
            filters: 过滤条件字典，可选参数：
                - min_tvl: 最小TVL (USD)
                - min_volume_24h: 最小24h交易量 (USD)
                - max_pools: 最大返回池子数量
                - min_fee_tvl: 最小年化费率 (%)
                - target_tokens: 目标代币列表
                
        Returns:
            Dict包含pools数据和统计信息
        """
        try:
            if not AGNO_AVAILABLE:
                return {
                    "error": "Agno Framework not available",
                    "pools": [],
                    "metadata": {}
                }
            
            # 验证链参数
            if chain not in ['bsc', 'solana']:
                return {
                    "error": f"不支持的链: {chain}，仅支持 'bsc' 或 'solana'",
                    "pools": [],
                    "metadata": {}
                }
            
            # 合并过滤条件
            effective_filters = self._merge_filters(filters)
            
            logger.info("pool_scanner_started", 
                       chain=chain, 
                       filters=effective_filters)
            
            # 根据链选择扫描方法
            if chain == 'bsc':
                pools_data = await self._scan_bsc_pools(effective_filters)
            else:  # solana
                pools_data = await self._scan_meteora_pools(effective_filters)
            
            # 处理和过滤数据
            processed_pools = self._process_pools(pools_data, chain, effective_filters)
            
            # 限制数量并排序
            sorted_pools = sorted(processed_pools, key=lambda p: p.get('fee_tvl', 0), reverse=True)
            final_pools = sorted_pools[:effective_filters['max_pools']]
            
            result = {
                "pools": final_pools,
                "metadata": {
                    "chain": chain,
                    "total_scanned": len(pools_data),
                    "pools_filtered": len(final_pools),
                    "filters_applied": effective_filters,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
            
            logger.info("pool_scanner_completed",
                       chain=chain,
                       pools_found=len(final_pools),
                       total_scanned=len(pools_data))
            
            return result
            
        except Exception as e:
            logger.error("pool_scanner_failed", chain=chain, error=str(e))
            return {
                "error": str(e),
                "pools": [],
                "metadata": {
                    "chain": chain,
                    "timestamp": datetime.utcnow().isoformat()
                }
            }
    
    def _merge_filters(self, filters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """合并用户提供的过滤条件和默认条件"""
        effective_filters = self.default_filters.copy()
        if filters:
            effective_filters.update(filters)
        return effective_filters
    
    async def _scan_bsc_pools(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """扫描BSC PancakeSwap池子"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.bsc_api_endpoints['pools'],
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status != 200:
                        raise Exception(f"BSC API错误: {response.status}")
                    
                    data = await response.json()
                    return data.get('data', [])
                    
        except Exception as e:
            logger.error("bsc_pool_fetch_failed", error=str(e))
            raise Exception(f"BSC池子数据获取失败: {e}")
    
    async def _scan_meteora_pools(self, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """扫描Solana Meteora池子"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    self.meteora_api_endpoints['pools'],
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status != 200:
                        raise Exception(f"Meteora API错误: {response.status}")
                    
                    data = await response.json()
                    # Meteora API直接返回list
                    return data if isinstance(data, list) else data.get('data', [])
                    
        except Exception as e:
            logger.error("meteora_pool_fetch_failed", error=str(e))
            raise Exception(f"Meteora池子数据获取失败: {e}")
    
    def _process_pools(self, pools_data: List[Dict[str, Any]], chain: str, filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理和标准化池子数据"""
        processed_pools = []
        
        for pool_data in pools_data:
            try:
                if chain == 'bsc':
                    pool = self._parse_bsc_pool(pool_data)
                else:  # solana
                    pool = self._parse_meteora_pool(pool_data)
                
                if pool and self._meets_criteria(pool, chain, filters):
                    processed_pools.append(pool)
                    
            except Exception as e:
                logger.warning("pool_processing_failed",
                             pool_address=pool_data.get('address'),
                             error=str(e))
                continue
        
        return processed_pools
    
    def _parse_bsc_pool(self, pool_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析BSC池子数据"""
        try:
            pool_address = pool_data.get('address')
            if not pool_address:
                return None
            
            # 获取代币信息
            token0 = pool_data.get('token0', {})
            token1 = pool_data.get('token1', {})
            
            token0_symbol = token0.get('symbol', '').upper()
            token1_symbol = token1.get('symbol', '').upper()
            
            if not token0_symbol or not token1_symbol:
                return None
            
            # 计算指标
            tvl_usd = float(pool_data.get('tvlUSD', 0))
            volume_24h = float(pool_data.get('volumeUSD24h', 0))
            fee_rate = float(pool_data.get('feeTier', 300)) / 1000000  # 转换为小数
            
            # 计算fee24h和fee_tvl
            fee24h = volume_24h * fee_rate
            fee_tvl = (fee24h / tvl_usd * 365) if tvl_usd > 0 else 0.0
            
            return {
                'id': pool_address,
                'chain': 'BSC',
                'token0': token0_symbol,
                'token1': token1_symbol,
                'tvl_usd': tvl_usd,
                'volume_24h': volume_24h,
                'fee24h': fee24h,
                'fee_tvl': fee_tvl,
                'fee_rate': fee_rate,
                'token0_price': float(token0.get('price', 0)),
                'token1_price': float(token1.get('price', 0)),
                'pair_name': f"{token0_symbol}/{token1_symbol}"
            }
            
        except (ValueError, KeyError, TypeError) as e:
            logger.warning("bsc_pool_parse_failed",
                         pool_address=pool_data.get('address'),
                         error=str(e))
            return None
    
    def _parse_meteora_pool(self, pool_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """解析Meteora池子数据"""
        try:
            pool_address = pool_data.get('address')
            if not pool_address:
                return None
            
            # 获取代币信息
            token_x = pool_data.get('mint_x', {})
            token_y = pool_data.get('mint_y', {})
            
            token0_symbol = token_x.get('symbol', '').upper()
            token1_symbol = token_y.get('symbol', '').upper()
            
            if not token0_symbol or not token1_symbol:
                return None
            
            # 计算指标
            tvl_usd = float(pool_data.get('liquidity_usd', 0))
            volume_24h = float(pool_data.get('volume_24h', 0))
            fee_rate = float(pool_data.get('fee_rate', 0)) / 100  # 转换为小数
            
            # 计算fee24h和fee_tvl
            fee24h = volume_24h * fee_rate
            fee_tvl = (fee24h / tvl_usd * 365) if tvl_usd > 0 else 0.0
            
            return {
                'id': pool_address,
                'chain': 'SOL',
                'token0': token0_symbol,
                'token1': token1_symbol,
                'tvl_usd': tvl_usd,
                'volume_24h': volume_24h,
                'fee24h': fee24h,
                'fee_tvl': fee_tvl,
                'fee_rate': fee_rate,
                'token0_price': float(token_x.get('price', 0)),
                'token1_price': float(token_y.get('price', 0)),
                'pair_name': f"{token0_symbol}/{token1_symbol}"
            }
            
        except (ValueError, KeyError, TypeError) as e:
            logger.warning("meteora_pool_parse_failed",
                         pool_address=pool_data.get('address'),
                         error=str(e))
            return None
    
    def _meets_criteria(self, pool: Dict[str, Any], chain: str, filters: Dict[str, Any]) -> bool:
        """检查池子是否满足筛选条件"""
        try:
            # TVL检查
            if pool['tvl_usd'] < filters['min_tvl']:
                return False
            
            # 交易量检查
            if pool['volume_24h'] < filters['min_volume_24h']:
                return False
            
            # 代币白名单检查
            target_tokens = filters.get('target_tokens', {}).get(chain, [])
            if target_tokens:
                if not (pool['token0'] in target_tokens or pool['token1'] in target_tokens):
                    return False
            
            # 费率合理性检查
            if pool['fee_rate'] <= 0 or pool['fee_rate'] > 0.1:  # 最高10%
                return False
            
            # fee_tvl检查（年化费率）
            if pool['fee_tvl'] < filters.get('min_fee_tvl', 0):
                return False
            
            # 防止异常高费率
            if pool['fee_tvl'] > 5000:  # 年化费率不应超过5000%
                return False
            
            return True
            
        except Exception as e:
            logger.warning("criteria_check_failed",
                         pool_id=pool.get('id'),
                         error=str(e))
            return False


# 为了向后兼容，如果Agno不可用，提供同步版本
class PoolScannerToolSync:
    """PoolScannerTool的同步版本，用于非Agno环境"""
    
    def __init__(self):
        self.tool = PoolScannerTool()
    
    def scan_pools(self, chain: str, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """同步版本的池子扫描"""
        try:
            # 检查是否已有运行中的事件循环
            try:
                loop = asyncio.get_running_loop()
                # 如果有运行中的循环，使用线程池执行
                import concurrent.futures
                import threading

                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(self.tool.run(chain, filters))
                    finally:
                        new_loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result(timeout=60)  # 60秒超时

            except RuntimeError:
                # 没有运行中的循环，可以直接创建
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    return loop.run_until_complete(self.tool.run(chain, filters))
                finally:
                    loop.close()

        except Exception as e:
            logger.error("sync_pool_scanner_failed", error=str(e))
            return {
                "error": str(e),
                "pools": [],
                "metadata": {}
            }


# 导出标准接口
__all__ = ['PoolScannerTool', 'PoolScannerToolSync', 'AGNO_AVAILABLE']
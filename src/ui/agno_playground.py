"""
Agno Framework Playground 部署
為 Dy-Flow v3 系統提供可視化管理界面
"""

import asyncio
import sys
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import structlog

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Agno Framework imports
try:
    from agno.playground import Playground, serve_playground_app
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.models.anthropic import Claude
    from agno.tools.reasoning import ReasoningTools
    from agno.tools.duckduckgo import DuckDuckGoTools
    from agno.storage.sqlite import SqliteStorage
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # 定義替代類
    class Playground:
        def __init__(self, agents=None):
            pass
        def get_app(self):
            return None

# Dy-Flow v3 imports
from src.agents.scorer_v2_agno import ScorerV2AgnoAgent
from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent
from src.agents.planner_agno import PlannerAgnoAgent
from src.agents.wallet_analyzer_agno import WalletAnalyzerAgent
from src.utils.config import load_config

logger = structlog.get_logger(__name__)

class DyFlowAgnoPlayground:
    """Dy-Flow v3 Agno Playground 管理類"""
    
    def __init__(self):
        self.config = load_config()
        self.agno_available = AGNO_AVAILABLE
        self.agents = {}
        self.playground_agents = []
        self.playground_app = None
        
        if not self.agno_available:
            logger.warning("agno_framework_not_available",
                         message="Agno Framework not available, playground disabled")
    
    async def setup_playground_agents(self) -> List[Agent]:
        """設置 Playground 專用的 Agno Agents"""
        if not self.agno_available:
            return []
        
        try:
            logger.info("setting_up_playground_agents")
            
            playground_agents = []
            
            # 1. DeFi 池子分析專家
            pool_analyzer = Agent(
                name="DeFi Pool Analyzer",
                role="分析 DeFi 流動性池的評分和風險",
                agent_id="defi-pool-analyzer",
                model=OpenAIChat(id="gpt-4o"),
                tools=[ReasoningTools(add_instructions=True)],
                storage=SqliteStorage(
                    table_name="pool_analyzer_sessions",
                    db_file="data/agno_memory/playground_sessions.db",
                    auto_upgrade_schema=True
                ),
                session_id="pool_analysis_session",
                instructions=[
                    "你是 DeFi 流動性池分析專家",
                    "能夠評估池子的收益潛力、風險水平和投資價值",
                    "使用 6 因子評分模型：費率、交易量、TVL、代幣品質、流動性深度、波動率",
                    "提供具體的投資建議和風險提示",
                    "考慮當前市場環境和趨勢",
                ],
                markdown=True,
                show_tool_calls=True,
                add_datetime_to_instructions=True,
            )
            playground_agents.append(pool_analyzer)
            
            # 2. DeFi 風險管理顧問
            risk_advisor = Agent(
                name="DeFi Risk Advisor",
                role="提供 DeFi 投資風險評估和管理建議",
                agent_id="defi-risk-advisor",
                model=Claude(id="claude-3-7-sonnet-latest"),
                tools=[
                    ReasoningTools(add_instructions=True),
                    DuckDuckGoTools()
                ],
                storage=SqliteStorage(
                    table_name="risk_advisor_sessions",
                    db_file="data/agno_memory/playground_sessions.db",
                    auto_upgrade_schema=True
                ),
                session_id="risk_advisory_session",
                instructions=[
                    "你是 DeFi 風險管理專家",
                    "專注於識別和評估各種 DeFi 風險",
                    "包括無常損失、智能合約風險、流動性風險、市場風險",
                    "提供風險緩解策略和保護措施",
                    "關注最新的 DeFi 安全事件和趨勢",
                ],
                markdown=True,
                show_tool_calls=True,
                add_datetime_to_instructions=True,
            )
            playground_agents.append(risk_advisor)
            
            # 3. DeFi 策略規劃師
            strategy_planner = Agent(
                name="DeFi Strategy Planner",
                role="制定個性化的 DeFi 投資策略",
                agent_id="defi-strategy-planner",
                model=OpenAIChat(id="gpt-4o"),
                tools=[ReasoningTools(add_instructions=True)],
                storage=SqliteStorage(
                    table_name="strategy_planner_sessions",
                    db_file="data/agno_memory/playground_sessions.db",
                    auto_upgrade_schema=True
                ),
                session_id="strategy_planning_session",
                instructions=[
                    "你是 DeFi 投資策略專家",
                    "能夠根據用戶的風險偏好和資金規模制定策略",
                    "熟悉三大核心策略：Delta 中性、階梯單邊、被動高 TVL",
                    "考慮資金效率、風險分散和收益優化",
                    "提供具體的執行步驟和時機建議",
                ],
                markdown=True,
                show_tool_calls=True,
                add_datetime_to_instructions=True,
            )
            playground_agents.append(strategy_planner)
            
            # 4. 錢包分析助手
            wallet_assistant = Agent(
                name="Wallet Analysis Assistant",
                role="分析錢包資產和提供優化建議",
                agent_id="wallet-analysis-assistant",
                model=OpenAIChat(id="gpt-4o"),
                tools=[ReasoningTools(add_instructions=True)],
                storage=SqliteStorage(
                    table_name="wallet_assistant_sessions",
                    db_file="data/agno_memory/playground_sessions.db",
                    auto_upgrade_schema=True
                ),
                session_id="wallet_analysis_session",
                instructions=[
                    "你是區塊鏈錢包分析專家",
                    "能夠分析錢包資產組合和交易歷史",
                    "識別潛在的詐騙代幣和風險資產",
                    "評估投資組合的健康度和多樣化程度",
                    "提供資產配置和優化建議",
                ],
                markdown=True,
                show_tool_calls=True,
                add_datetime_to_instructions=True,
            )
            playground_agents.append(wallet_assistant)
            
            # 5. 市場趨勢分析師
            market_analyst = Agent(
                name="DeFi Market Analyst",
                role="分析 DeFi 市場趨勢和機會",
                agent_id="defi-market-analyst",
                model=Claude(id="claude-3-7-sonnet-latest"),
                tools=[
                    ReasoningTools(add_instructions=True),
                    DuckDuckGoTools()
                ],
                storage=SqliteStorage(
                    table_name="market_analyst_sessions",
                    db_file="data/agno_memory/playground_sessions.db",
                    auto_upgrade_schema=True
                ),
                session_id="market_analysis_session",
                instructions=[
                    "你是 DeFi 市場分析專家",
                    "關注整體市場趨勢、協議發展和創新動向",
                    "分析 TVL 變化、新興協議和市場機會",
                    "提供宏觀市場觀點和投資時機建議",
                    "監控監管動態和政策影響",
                ],
                markdown=True,
                show_tool_calls=True,
                add_datetime_to_instructions=True,
            )
            playground_agents.append(market_analyst)
            
            logger.info("playground_agents_setup_complete", 
                       agents_count=len(playground_agents))
            
            return playground_agents
            
        except Exception as e:
            logger.error("playground_agents_setup_failed", error=str(e))
            return []
    
    async def create_playground_app(self):
        """創建 Playground 應用"""
        if not self.agno_available:
            logger.warning("cannot_create_playground_agno_unavailable")
            return None
        
        try:
            # 設置 Playground 專用 Agents
            self.playground_agents = await self.setup_playground_agents()
            
            if not self.playground_agents:
                logger.warning("no_playground_agents_available")
                return None
            
            # 創建 Playground 實例
            self.playground_app = Playground(agents=self.playground_agents).get_app()
            
            logger.info("playground_app_created", 
                       agents_count=len(self.playground_agents))
            
            return self.playground_app
            
        except Exception as e:
            logger.error("playground_app_creation_failed", error=str(e))
            return None
    
    async def get_agent_insights(self) -> Dict[str, Any]:
        """獲取 Playground Agents 的洞察信息"""
        insights = {
            "agno_available": self.agno_available,
            "agents_count": len(self.playground_agents),
            "app_ready": self.playground_app is not None,
            "agents_info": []
        }
        
        if self.playground_agents:
            for agent in self.playground_agents:
                agent_info = {
                    "name": agent.name,
                    "role": agent.role,
                    "agent_id": agent.agent_id,
                    "model": str(agent.model),
                    "tools_count": len(agent.tools) if agent.tools else 0,
                    "has_storage": agent.storage is not None,
                    "session_id": agent.session_id
                }
                insights["agents_info"].append(agent_info)
        
        return insights

def create_dyflow_playground():
    """創建 Dy-Flow Playground 實例"""
    return DyFlowAgnoPlayground()

async def setup_and_serve_playground(host: str = "127.0.0.1", port: int = 7777, reload: bool = True):
    """設置並啟動 Playground 服務"""
    
    # 檢查 Agno 可用性
    if not AGNO_AVAILABLE:
        print("❌ Agno Framework 不可用")
        print("請安裝 Agno Framework:")
        print("pip install agno openai anthropic duckduckgo-search")
        return False
    
    print("🚀 啟動 Dy-Flow Agno Playground...")
    
    try:
        # 創建 Playground 管理器
        playground_manager = create_dyflow_playground()
        
        # 創建 Playground 應用
        app = await playground_manager.create_playground_app()
        
        if not app:
            print("❌ Playground 應用創建失敗")
            return False
        
        # 顯示 Agents 信息
        insights = await playground_manager.get_agent_insights()
        print(f"✅ Playground 準備就緒:")
        print(f"   - 可用 Agents: {insights['agents_count']}")
        print(f"   - 應用狀態: {'就緒' if insights['app_ready'] else '未就緒'}")
        
        print(f"\n📋 可用的 DeFi 專家:")
        for i, agent_info in enumerate(insights["agents_info"], 1):
            print(f"   {i}. {agent_info['name']} - {agent_info['role']}")
        
        print(f"\n🌐 啟動 Web 服務...")
        print(f"   地址: http://{host}:{port}")
        print(f"   重載模式: {reload}")
        print(f"\n💡 使用指南:")
        print(f"   - 在瀏覽器中打開上述地址")
        print(f"   - 選擇相應的 DeFi 專家進行對話")
        print(f"   - 詢問池子分析、風險評估、策略規劃等問題")
        print(f"   - 所有對話都會被記錄和學習")
        
        # 啟動 Playground 服務
        serve_playground_app(
            app="src.ui.agno_playground:app",
            host=host,
            port=port,
            reload=reload
        )
        
        return True
        
    except Exception as e:
        print(f"❌ Playground 啟動失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

# 全局應用實例（用於 serve_playground_app）
app = None

async def initialize_global_app():
    """初始化全局應用實例"""
    global app
    if app is None:
        playground_manager = create_dyflow_playground()
        app = await playground_manager.create_playground_app()
    return app

def get_app():
    """獲取應用實例（同步版本）"""
    global app
    if app is None:
        # 在異步環境中初始化
        asyncio.create_task(initialize_global_app())
    return app

# 啟動腳本
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Dy-Flow Agno Playground")
    parser.add_argument("--host", default="127.0.0.1", help="服務器主機地址")
    parser.add_argument("--port", type=int, default=7777, help="服務器端口")
    parser.add_argument("--no-reload", action="store_true", help="禁用自動重載")
    
    args = parser.parse_args()
    
    # 設置結構化日誌
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.dev.ConsoleRenderer()
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # 創建必要的目錄
    Path("data/agno_memory").mkdir(parents=True, exist_ok=True)
    
    # 啟動 Playground
    try:
        asyncio.run(setup_and_serve_playground(
            host=args.host,
            port=args.port,
            reload=not args.no_reload
        ))
    except KeyboardInterrupt:
        print("\n⏹️  Playground 已停止")
    except Exception as e:
        print(f"\n💥 Playground 運行錯誤: {e}")
        import traceback
        traceback.print_exc()

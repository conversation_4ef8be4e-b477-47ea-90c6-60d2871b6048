"""
DyFlow Rich仪表板
提供实时的CLI界面显示
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from rich.console import Console
from rich.layout import Layout
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeRemainingColumn
from rich.columns import Columns
from rich.align import Align
import structlog

from ..utils.models import (
    PoolMetrics, StrategyDecision, RiskAlert, SystemStatus,
    RiskLevel, ActionType
)
from ..utils.helpers import format_currency, format_percentage, get_utc_timestamp

logger = structlog.get_logger(__name__)


class Dashboard:
    """Rich仪表板"""
    
    def __init__(self, config):
        self.config = config
        self.console = Console()
        self.layout = Layout()
        self.live: Optional[Live] = None
        self.is_running = False
        
        # 数据缓存
        self.pool_data: List[PoolMetrics] = []
        self.strategy_decisions: List[StrategyDecision] = []
        self.risk_alerts: List[RiskAlert] = []
        self.system_status: Optional[SystemStatus] = None
        self.current_positions: List[Dict[str, Any]] = []
        self.opportunities: List[Dict[str, Any]] = []
        
        # 仪表板配置
        self.update_interval = config.get('dashboard', {}).get('update_interval', 5)
        self.max_positions = config.get('dashboard', {}).get('max_positions', 5)
        self.max_opportunities = config.get('dashboard', {}).get('max_opportunities', 10)
        
        # 计时器
        self.last_scan_time = get_utc_timestamp()
        self.next_scan_time = self.last_scan_time + timedelta(minutes=5)
        self.total_pnl_24h = 0.0
        
        # 设置布局
        self._setup_layout()
    
    async def initialize(self):
        """初始化仪表板"""
        try:
            logger.info("dashboard_initializing")
            logger.info("dashboard_initialized")
        except Exception as e:
            logger.error("dashboard_init_failed", error=str(e))
            raise
    
    async def start(self):
        """启动仪表板"""
        if self.is_running:
            return
        
        try:
            logger.info("dashboard_starting")
            self.is_running = True
            
            # 启动Live显示
            self.live = Live(
                self.layout,
                console=self.console,
                refresh_per_second=1,
                screen=True
            )
            self.live.start()
            
            # 启动定期更新任务
            asyncio.create_task(self._update_task())
            
            logger.info("dashboard_started")
            
        except Exception as e:
            logger.error("dashboard_start_failed", error=str(e))
    
    async def stop(self):
        """停止仪表板"""
        if not self.is_running:
            return
        
        try:
            self.is_running = False
            
            if self.live:
                self.live.stop()
                self.live = None
            
            logger.info("dashboard_stopped")
            
        except Exception as e:
            logger.error("dashboard_stop_failed", error=str(e))
    
    def _setup_layout(self):
        """设置布局 - 按照任务规格"""
        self.layout.split_column(
            Layout(name="current_positions", size=12),  # 当前持仓表格
            Layout(name="opportunities", size=3),       # 投资机会
            Layout(name="status_bar", size=3)           # 状态栏
        )
    
    async def _update_task(self):
        """定期更新任务"""
        while self.is_running:
            try:
                self._update_display()
                await asyncio.sleep(1)  # 每秒更新一次
                
                # 每5分钟更新一次数据
                if (get_utc_timestamp() - self.last_scan_time).total_seconds() >= 300:
                    await self._refresh_data()
                    
            except Exception as e:
                logger.error("dashboard_update_failed", error=str(e))
                await asyncio.sleep(5)  # 出错时等待5秒
    
    def _update_display(self):
        """更新显示内容 - 按照任务规格"""
        try:
            # 更新各个区域
            self.layout["current_positions"].update(self._create_current_positions_panel())
            self.layout["opportunities"].update(self._create_opportunities_panel())
            self.layout["status_bar"].update(self._create_status_bar())
            
        except Exception as e:
            logger.error("display_update_failed", error=str(e))
    
    def _create_current_positions_panel(self) -> Panel:
        """创建当前持仓面板 - 按照任务规格"""
        table = Table(show_header=True, header_style="bold magenta", title="Current LP (max 5)")
        table.add_column("Pool", width=12)
        table.add_column("Chain", width=6)
        table.add_column("APR", justify="right", width=6)
        table.add_column("IL", justify="right", width=5)
        table.add_column("TVL($)", justify="right", width=8)
        table.add_column("Daily$", justify="right", width=8)
        table.add_column("Status", width=10)
        
        # 显示当前持仓（最多5个）
        positions_to_show = self.current_positions[:self.max_positions]
        
        if not positions_to_show:
            # 如果没有持仓，显示从池子数据中选择的顶级池子
            top_pools = self.pool_data[:self.max_positions] if self.pool_data else []
            for pool in top_pools:
                status_icon, status_text = self._get_position_status(pool)
                daily_pnl = self._calculate_daily_pnl(pool)
                
                table.add_row(
                    f"{pool.token_a}-{pool.token_b}",
                    pool.chain.upper(),
                    f"{pool.apr:.1%}",
                    f"{pool.il_risk:.1f}",
                    self._format_tvl(pool.tvl),
                    f"{daily_pnl:+.1f}" if daily_pnl != 0 else "0.0",
                    f"{status_icon} {status_text}"
                )
        else:
            for pos in positions_to_show:
                table.add_row(
                    pos['pool_name'],
                    pos['chain'].upper(),
                    f"{pos['apr']:.1%}",
                    f"{pos['il_risk']:.1f}",
                    self._format_tvl(pos['tvl']),
                    f"{pos['daily_pnl']:+.1f}",
                    f"{pos['status_icon']} {pos['status_text']}"
                )
        
        return Panel(table, border_style="blue")
    
    def _create_opportunities_panel(self) -> Panel:
        """创建投资机会面板 - 按照任务规格"""
        if not self.opportunities:
            # 从池子数据中生成机会列表
            self.opportunities = self._generate_opportunities()
        
        opportunity_text = Text()
        opportunity_text.append("Top-5 Opportunities → ", style="bold cyan")
        
        top_opportunities = self.opportunities[:5]
        for i, opp in enumerate(top_opportunities):
            if i > 0:
                opportunity_text.append("  ", style="white")
            opportunity_text.append(f"{opp['name']} ({opp['score']:.2f})", style="yellow")
        
        if len(top_opportunities) > 0:
            opportunity_text.append(" ...", style="dim")
        
        return Panel(opportunity_text, border_style="green")
    
    def _create_status_bar(self) -> Panel:
        """创建状态栏 - 按照任务规格"""
        # 计算下次扫描倒计时
        now = get_utc_timestamp()
        time_until_scan = self.next_scan_time - now
        
        if time_until_scan.total_seconds() <= 0:
            countdown_text = "00:00"
            self.next_scan_time = now + timedelta(minutes=5)
        else:
            minutes = int(time_until_scan.total_seconds() // 60)
            seconds = int(time_until_scan.total_seconds() % 60)
            countdown_text = f"{minutes:02d}:{seconds:02d}"
        
        # 构建状态栏文本
        status_text = Text()
        status_text.append("Next scan in ", style="white")
        status_text.append(countdown_text, style="bold cyan")
        status_text.append(" ‖ Net 24h PnL ", style="white")
        
        pnl_color = "green" if self.total_pnl_24h >= 0 else "red"
        pnl_sign = "+" if self.total_pnl_24h >= 0 else ""
        status_text.append(f"{pnl_sign}{self.total_pnl_24h:.1f} $", style=f"bold {pnl_color}")
        
        # 添加状态图标说明
        status_text.append("\n", style="white")
        status_text.append("状态图标：", style="dim")
        status_text.append("✅Hold ", style="green")
        status_text.append("🔄Rebal ", style="yellow")
        status_text.append("⚠️Exit ", style="red")
        status_text.append("待撤 ", style="orange1")
        status_text.append("❌Exited", style="red")
        
        status_text.append("\nDaily$ = 已收手续费 – Gas – Swap 滑点", style="dim")
        
        return Panel(status_text, border_style="cyan")
    
    def _create_status_panel(self) -> Panel:
        """创建系统状态面板"""
        if not self.system_status:
            return Panel("⚡ 系统初始化中...", title="系统状态")
        
        status_text = Text()
        
        # 系统运行状态
        if self.system_status.is_running:
            status_text.append("🟢 系统运行正常\n", style="green")
        else:
            status_text.append("🔴 系统已停止\n", style="red")
        
        # 网络连接状态
        status_text.append("📡 网络连接:\n", style="bold")
        bsc_status = "🟢" if self.system_status.bsc_connection else "🔴"
        solana_status = "🟢" if self.system_status.solana_connection else "🔴"
        status_text.append(f"  BSC: {bsc_status} | Solana: {solana_status}\n")
        
        # 投资组合信息
        status_text.append("\n💰 投资组合:\n", style="bold")
        status_text.append(f"  总价值: {format_currency(self.system_status.total_value_managed)}\n")
        status_text.append(f"  活跃持仓: {self.system_status.active_positions}\n")
        status_text.append(f"  待确认交易: {self.system_status.pending_transactions}\n")
        
        # 最后检查时间
        if self.system_status.last_check_time:
            time_str = self.system_status.last_check_time.strftime("%H:%M:%S")
            status_text.append(f"\n⏰ 最后检查: {time_str}")
        
        return Panel(status_text, title="⚡ 系统状态")
    
    def _create_alerts_panel(self) -> Panel:
        """创建风险警报面板"""
        if not self.risk_alerts:
            return Panel("✅ 暂无风险警报", title="风险警报", style="green")
        
        # 统计不同级别的警报
        critical_count = len([a for a in self.risk_alerts if a.level == RiskLevel.CRITICAL.value])
        high_count = len([a for a in self.risk_alerts if a.level == RiskLevel.HIGH.value])
        
        if critical_count > 0:
            panel_style = "red"
            title_icon = "🚨"
        elif high_count > 0:
            panel_style = "yellow"
            title_icon = "⚠️"
        else:
            panel_style = "green"
            title_icon = "✅"
        
        alert_text = Text()
        
        # 显示最近的5个警报
        recent_alerts = self.risk_alerts[-5:]
        for alert in recent_alerts:
            level_icon = {
                RiskLevel.CRITICAL.value: "🚨",
                RiskLevel.HIGH.value: "⚠️",
                RiskLevel.MEDIUM.value: "⚡",
                RiskLevel.LOW.value: "ℹ️"
            }.get(alert.level, "ℹ️")
            
            level_color = {
                RiskLevel.CRITICAL.value: "red",
                RiskLevel.HIGH.value: "yellow",
                RiskLevel.MEDIUM.value: "cyan",
                RiskLevel.LOW.value: "blue"
            }.get(alert.level, "white")
            
            alert_text.append(f"{level_icon} ", style=level_color)
            alert_text.append(f"{alert.message}\n", style=level_color)
        
        return Panel(alert_text, title=f"{title_icon} 风险警报", style=panel_style)
    
    def _create_footer(self) -> Panel:
        """创建底部面板"""
        footer_text = Text()
        footer_text.append("💡 提示: ", style="bold cyan")
        footer_text.append("按 Ctrl+C 停止系统 | ", style="white")
        footer_text.append("📖 查看日志: python main.py logs", style="cyan")
        
        return Panel(footer_text, style="dim")
    
    async def update_pool_data(self, pool_data: List[PoolMetrics]):
        """更新池子数据"""
        self.pool_data = pool_data
        logger.debug("dashboard_pool_data_updated", count=len(pool_data))
    
    async def update_strategy_decisions(self, decisions: List[StrategyDecision]):
        """更新策略决策"""
        self.strategy_decisions.extend(decisions)
        # 保持最近50个决策
        if len(self.strategy_decisions) > 50:
            self.strategy_decisions = self.strategy_decisions[-50:]
        logger.debug("dashboard_decisions_updated", count=len(decisions))
    
    async def update_risk_alerts(self, alerts: List[RiskAlert]):
        """更新风险警报"""
        self.risk_alerts.extend(alerts)
        # 保持最近30个警报
        if len(self.risk_alerts) > 30:
            self.risk_alerts = self.risk_alerts[-30:]
        logger.debug("dashboard_alerts_updated", count=len(alerts))
    
    async def update_system_status(self, status: SystemStatus):
        """更新系统状态"""
        self.system_status = status
        logger.debug("dashboard_status_updated")
    
    def show_startup_message(self):
        """显示启动信息"""
        self.console.print("\n🚀 DyFlow LP策略AI Agent 启动中...\n", style="bold blue")
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console,
        ) as progress:
            task = progress.add_task("初始化系统组件...", total=None)
            # 这里可以添加具体的初始化步骤
            import time
            time.sleep(2)  # 模拟初始化时间
            progress.update(task, description="系统初始化完成!")
        
        self.console.print("✅ 系统启动成功! 监控界面已激活.\n", style="bold green")
    
    def show_shutdown_message(self):
        """显示关闭信息"""
        self.console.print("\n🛑 DyFlow系统正在关闭...", style="bold yellow")
        self.console.print("✅ 系统已安全关闭.", style="bold green")
    
    def _get_position_status(self, pool: PoolMetrics) -> tuple:
        """获取持仓状态"""
        # 根据 APR 和风险确定状态
        if pool.apr >= 0.1 and pool.il_risk <= 0.05:  # 高收益低风险
            return "✅", "Hold"
        elif pool.apr >= 0.05 and pool.il_risk <= 0.1:  # 中等收益
            return "🔄", "Rebal"
        elif pool.il_risk > 0.15:  # 高风险
            return "⚠️", "Exit"
        else:
            return "✅", "Hold"
    
    def _calculate_daily_pnl(self, pool: PoolMetrics) -> float:
        """计算每日收益"""
        # 简化计算：基于 APR 和 TVL
        if pool.tvl > 0:
            daily_apr = pool.apr / 365
            fees_earned = pool.tvl * daily_apr * 0.1  # 假设我们占池子的10%
            # 减去估计的 gas 费用
            gas_cost = 5.0  # 估计每日 gas 费用
            return fees_earned - gas_cost
        return 0.0
    
    def _format_tvl(self, tvl: float) -> str:
        """格式化 TVL 显示"""
        if tvl >= 1_000_000:
            return f"{tvl/1_000_000:.1f}M"
        elif tvl >= 1_000:
            return f"{tvl/1_000:.1f}k"
        else:
            return f"{tvl:.0f}"
    
    def _generate_opportunities(self) -> List[Dict[str, Any]]:
        """生成投资机会列表"""
        opportunities = []
        
        for pool in self.pool_data:
            # 计算机会评分（综合 APR、风险、TVL）
            apr_score = min(pool.apr * 10, 1.0)  # APR 得分
            risk_score = 1.0 - min(pool.il_risk * 5, 1.0)  # 风险得分（越低越好）
            tvl_score = min(pool.tvl / 1_000_000, 1.0)  # TVL 得分
            
            total_score = (apr_score * 0.5 + risk_score * 0.3 + tvl_score * 0.2)
            
            opportunities.append({
                'name': f"{pool.token_a}-{pool.token_b}",
                'score': total_score,
                'pool': pool
            })
        
        # 按评分排序
        opportunities.sort(key=lambda x: x['score'], reverse=True)
        return opportunities
    
    async def _refresh_data(self):
        """刷新数据"""
        try:
            logger.info("refreshing_dashboard_data")
            self.last_scan_time = get_utc_timestamp()
            self.next_scan_time = self.last_scan_time + timedelta(minutes=5)
            
            # 计算 24h PnL
            self.total_pnl_24h = sum(self._calculate_daily_pnl(pool) for pool in self.pool_data)
            
            logger.debug("dashboard_data_refreshed", pnl_24h=self.total_pnl_24h)
            
        except Exception as e:
            logger.error("dashboard_data_refresh_failed", error=str(e))
    
    async def update_positions(self, positions: List[Dict[str, Any]]):
        """更新当前持仓"""
        self.current_positions = positions
        logger.debug("dashboard_positions_updated", count=len(positions))
    
    async def update_opportunities(self, opportunities: List[Dict[str, Any]]):
        """更新投资机会"""
        self.opportunities = opportunities
        logger.debug("dashboard_opportunities_updated", count=len(opportunities))
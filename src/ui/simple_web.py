"""
Simple Web Dashboard for Portfolio Manager
使用 TailwindCSS 的简洁 Web 界面
"""

from flask import Flask, render_template_string, jsonify
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
import structlog

from ..utils.helpers import get_utc_timestamp

logger = structlog.get_logger(__name__)


class SimpleWebDashboard:
    """简单的 Portfolio Manager Web Dashboard"""
    
    def __init__(self, config, portfolio_manager=None):
        self.config = config
        self.portfolio_manager = portfolio_manager
        self.app = Flask(__name__)
        
        # 配置
        self.host = config.get('web_dashboard', {}).get('host', '0.0.0.0')
        self.port = config.get('web_dashboard', {}).get('port', 8080)
        
        # 设置路由
        self._setup_routes()
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.route('/')
        def dashboard():
            """主仪表板页面"""
            return render_template_string(self._get_dashboard_template())
        
        @self.app.route('/api/status')
        def api_status():
            """获取状态 API"""
            try:
                if self.portfolio_manager:
                    portfolio_summary = self.portfolio_manager.get_current_portfolio_summary()
                    decision_summary = self.portfolio_manager.get_last_decision_summary()
                else:
                    portfolio_summary = {'total_positions': 0, 'total_allocated': 0.0}
                    decision_summary = None
                
                return jsonify({
                    'success': True,
                    'portfolio': portfolio_summary,
                    'last_decision': decision_summary,
                    'timestamp': get_utc_timestamp().isoformat()
                })
            except Exception as e:
                return jsonify({
                    'success': False,
                    'error': str(e)
                }), 500
    
    def _get_dashboard_template(self) -> str:
        """返回 Dashboard HTML 模板"""
        return '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Manager Dashboard - Dy-Flow 2.1</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen" x-data="dashboard()">
    <!-- 顶部导航栏 -->
    <nav class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-bold">🚀 Dy-Flow 2.1 Portfolio Manager</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm" x-text="'最后更新: ' + lastUpdate"></span>
                    <div class="w-3 h-3 rounded-full" :class="isOnline ? 'bg-green-400' : 'bg-red-400'"></div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 py-6">
        <!-- 状态卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <!-- 投资组合状态 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-500 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总持仓</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="portfolio.total_positions || 0"></p>
                    </div>
                </div>
            </div>

            <!-- 总投资金额 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-green-500 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">总投资</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="'$' + (portfolio.total_allocated || 0).toFixed(2)"></p>
                    </div>
                </div>
            </div>

            <!-- 对冲头寸 -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-500 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">对冲头寸</p>
                        <p class="text-2xl font-bold text-gray-900" x-text="portfolio.hedged_positions || 0"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最新决策 -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">📋 最新投资决策</h3>
            </div>
            <div class="p-6">
                <template x-if="lastDecision">
                    <div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <p class="text-sm text-gray-600">决策ID</p>
                                <p class="font-mono text-sm" x-text="lastDecision.decision_id"></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">触发原因</p>
                                <p class="text-sm" x-text="lastDecision.trigger"></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">总分配</p>
                                <p class="text-sm font-bold" x-text="'$' + (lastDecision.total_allocation || 0).toFixed(2)"></p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">置信度</p>
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-600 h-2 rounded-full" :style="'width: ' + (lastDecision.confidence * 100) + '%'"></div>
                                    </div>
                                    <span class="text-sm" x-text="(lastDecision.confidence * 100).toFixed(1) + '%'"></span>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4">
                            <p class="text-sm text-gray-600">决策时间</p>
                            <p class="text-sm" x-text="lastDecision.timestamp"></p>
                        </div>
                    </div>
                </template>
                <template x-if="!lastDecision">
                    <div class="text-center py-8 text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p>暂无决策记录</p>
                    </div>
                </template>
            </div>
        </div>

        <!-- 系统状态 -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">⚡ 系统状态</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="text-2xl mb-2" :class="isOnline ? 'text-green-500' : 'text-red-500'">
                            <span x-text="isOnline ? '🟢' : '🔴'"></span>
                        </div>
                        <p class="text-sm text-gray-600">系统状态</p>
                        <p class="text-sm font-medium" x-text="isOnline ? '运行中' : '离线'"></p>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl mb-2 text-blue-500">📊</div>
                        <p class="text-sm text-gray-600">Portfolio Manager</p>
                        <p class="text-sm font-medium">已启用</p>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl mb-2 text-purple-500">🔄</div>
                        <p class="text-sm text-gray-600">自动决策</p>
                        <p class="text-sm font-medium">已启用</p>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl mb-2 text-green-500">🛡️</div>
                        <p class="text-sm text-gray-600">风险控制</p>
                        <p class="text-sm font-medium">已启用</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alpine.js 数据和逻辑 -->
    <script>
        function dashboard() {
            return {
                portfolio: {},
                lastDecision: null,
                isOnline: true,
                lastUpdate: '',
                
                init() {
                    this.fetchData();
                    setInterval(() => {
                        this.fetchData();
                    }, 5000); // 每5秒刷新
                },
                
                async fetchData() {
                    try {
                        const response = await fetch('/api/status');
                        const data = await response.json();
                        
                        if (data.success) {
                            this.portfolio = data.portfolio || {};
                            this.lastDecision = data.last_decision;
                            this.isOnline = true;
                            this.lastUpdate = new Date().toLocaleTimeString('zh-CN');
                        } else {
                            this.isOnline = false;
                            console.error('API 错误:', data.error);
                        }
                    } catch (error) {
                        this.isOnline = false;
                        console.error('获取数据失败:', error);
                    }
                }
            }
        }
    </script>
</body>
</html>
'''
    
    def run(self, **kwargs):
        """启动 Web 服务器"""
        try:
            logger.info("starting_web_dashboard", host=self.host, port=self.port)
            self.app.run(
                host=self.host,
                port=self.port,
                debug=kwargs.get('debug', False),
                **kwargs
            )
        except Exception as e:
            logger.error("web_dashboard_start_failed", error=str(e))
            raise
    
    async def start_async(self):
        """异步启动"""
        import threading
        thread = threading.Thread(target=self.run, kwargs={'debug': False})
        thread.daemon = True
        thread.start()
        logger.info("web_dashboard_started_async", host=self.host, port=self.port)
        return thread
#!/usr/bin/env python3
"""
Portfolio Manager Web Server
启动简单的 Web Dashboard
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.ui.simple_web import SimpleWebDashboard
from src.utils.config import Config
from src.utils.database import Database
from src.agents.portfolio_manager import PortfolioManagerAgent


class MockConfig:
    """模拟配置"""
    def get(self, section, default=None):
        configs = {
            'web_dashboard': {
                'host': '0.0.0.0',
                'port': 8080,
                'debug': False
            },
            'portfolio_manager_agent': {
                'full_amount_per_pool': 500.0,
                'partial_amount_per_pool': 250.0,
                'min_score_threshold': 60.0,
                'max_positions': 5,
                'max_total_allocation': 2500.0,
                'min_usdt_reserve': 100.0,
                'hedge_provider': 'local',
                'hedge_ratio': 0.8,
                'enable_hedging': True,
                'default_slippage': 0.005,
                'gas_cost_estimate': 10.0
            }
        }
        return configs.get(section, default)


class MockDatabase:
    """模拟数据库"""
    def __init__(self):
        self.data = {}
    
    async def insert_record(self, table: str, data: dict) -> str:
        if table not in self.data:
            self.data[table] = []
        record_id = f"{table}_{len(self.data[table])}"
        data['id'] = record_id
        self.data[table].append(data)
        return record_id
    
    async def get_records(self, table: str, filters: dict = None) -> list:
        return self.data.get(table, [])


async def create_portfolio_manager():
    """创建 Portfolio Manager 实例"""
    try:
        config = MockConfig()
        database = MockDatabase()
        
        # 创建 Portfolio Manager
        portfolio_manager = PortfolioManagerAgent(config, database)
        await portfolio_manager.initialize()
        
        print("✅ Portfolio Manager 初始化成功")
        return portfolio_manager
        
    except Exception as e:
        print(f"❌ Portfolio Manager 初始化失败: {e}")
        return None


def start_web_server(portfolio_manager=None):
    """启动 Web 服务器"""
    try:
        config = MockConfig()
        
        # 创建 Web Dashboard
        dashboard = SimpleWebDashboard(config, portfolio_manager)
        
        print("🚀 启动 Portfolio Manager Web Dashboard...")
        print(f"📱 访问地址: http://localhost:8080")
        print("按 Ctrl+C 停止服务器")
        
        # 启动服务器
        dashboard.run(debug=False)
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ Web 服务器启动失败: {e}")


async def main():
    """主函数"""
    print("Portfolio Manager Web Dashboard")
    print("Dy-Flow 2.1 中央投资组合管理器")
    print("=" * 50)
    
    # 创建 Portfolio Manager（可选）
    portfolio_manager = await create_portfolio_manager()
    
    # 启动 Web 服务器
    start_web_server(portfolio_manager)


if __name__ == "__main__":
    asyncio.run(main())
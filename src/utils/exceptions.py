"""
DyFlow 异常定义模块
定义项目中使用的所有自定义异常类
"""

from typing import Optional


class DyFlowException(Exception):
    """DyFlow基础异常类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class NetworkException(DyFlowException):
    """网络连接异常"""
    pass


class InsufficientFundsException(DyFlowException):
    """资金不足异常"""
    pass


class RiskLimitExceededException(DyFlowException):
    """风险限制超出异常"""
    pass


class InvalidConfigurationException(DyFlowException):
    """配置错误异常"""
    pass


class TransactionFailedException(DyFlowException):
    """交易失败异常"""
    pass


class DataProviderException(DyFlowException):
    """数据提供者异常"""
    pass


class StrategyException(DyFlowException):
    """策略执行异常"""
    pass


class WalletAnalysisError(DyFlowException):
    """钱包分析异常"""
    pass


class Web3ConnectionError(NetworkException):
    """Web3连接异常"""
    pass


class ContractCallError(DyFlowException):
    """智能合约调用异常"""
    pass


class ExecutorException(DyFlowException):
    """执行器异常"""
    pass


class StateManagerException(DyFlowException):
    """状态管理异常"""
    pass


class PriceDataException(DyFlowException):
    """价格数据异常"""
    pass


class PoolNotExistException(DyFlowException):
    """池子不存在异常"""
    pass


class SlippageExceededException(DyFlowException):
    """滑点超出限制异常"""
    pass
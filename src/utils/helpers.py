"""
DyFlow 辅助工具模块
提供通用的工具函数和装饰器
"""

import asyncio
import time
import hashlib
from decimal import Decimal, ROUND_DOWN
from datetime import datetime, timezone
from typing import Any, Callable, Optional, Union, Dict, List
from functools import wraps
import structlog

from .exceptions import DyFlowException

logger = structlog.get_logger(__name__)


def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """异步重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        wait_time = delay * (backoff ** attempt)
                        logger.warning(
                            "function_retry",
                            function=func.__name__,
                            attempt=attempt + 1,
                            max_retries=max_retries,
                            wait_time=wait_time,
                            error=str(e)
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(
                            "function_failed_after_retries",
                            function=func.__name__,
                            max_retries=max_retries,
                            error=str(e)
                        )
            
            raise last_exception
        return wrapper
    return decorator


def rate_limit(calls_per_second: float):
    """速率限制装饰器"""
    min_interval = 1.0 / calls_per_second
    last_called = [0.0]
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                await asyncio.sleep(left_to_wait)
            ret = await func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator


def format_number(value: Union[int, float, Decimal], decimals: int = 2) -> str:
    """格式化数字显示"""
    if value is None:
        return "N/A"
    
    try:
        if isinstance(value, str):
            value = float(value)
        
        if value >= 1_000_000_000:
            return f"{value / 1_000_000_000:.{decimals}f}B"
        elif value >= 1_000_000:
            return f"{value / 1_000_000:.{decimals}f}M"
        elif value >= 1_000:
            return f"{value / 1_000:.{decimals}f}K"
        else:
            return f"{value:.{decimals}f}"
    except (ValueError, TypeError):
        return "N/A"


def format_percentage(value: Union[int, float], decimals: int = 2) -> str:
    """格式化百分比显示"""
    if value is None:
        return "N/A"
    
    try:
        if isinstance(value, str):
            value = float(value)
        return f"{value * 100:.{decimals}f}%"
    except (ValueError, TypeError):
        return "N/A"


def format_currency(value: Union[int, float, Decimal], currency: str = "USD", decimals: int = 2) -> str:
    """格式化货币显示"""
    if value is None:
        return "N/A"
    
    try:
        if isinstance(value, str):
            value = float(value)
        
        formatted_value = format_number(value, decimals)
        return f"${formatted_value}" if currency == "USD" else f"{formatted_value} {currency}"
    except (ValueError, TypeError):
        return "N/A"


def normalize_token_amount(amount: Union[int, float, str], decimals: int = 18) -> Decimal:
    """标准化代币数量（考虑小数位数）"""
    try:
        if isinstance(amount, str):
            amount = int(amount) if amount.isdigit() else float(amount)
        
        # 如果是整数形式的wei，需要除以10^decimals
        if isinstance(amount, int) and amount > 10**12:  # 假设大于1T的数字是wei
            return Decimal(amount) / Decimal(10 ** decimals)
        else:
            return Decimal(str(amount))
    except Exception:
        return Decimal('0')


def denormalize_token_amount(amount: Union[int, float, Decimal], decimals: int = 18) -> int:
    """反标准化代币数量（转换为wei）"""
    try:
        if isinstance(amount, (int, float)):
            amount = Decimal(str(amount))
        return int(amount * Decimal(10 ** decimals))
    except Exception:
        return 0


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """计算百分比变化"""
    if old_value == 0:
        return 0.0
    return (new_value - old_value) / old_value


def calculate_price_impact(amount_in: float, reserve_in: float, reserve_out: float) -> float:
    """计算价格影响"""
    if reserve_in == 0 or reserve_out == 0:
        return 1.0  # 100% 影响
    
    # 使用常数乘积公式计算价格影响
    k = reserve_in * reserve_out
    new_reserve_in = reserve_in + amount_in
    new_reserve_out = k / new_reserve_in
    
    amount_out = reserve_out - new_reserve_out
    expected_amount_out = (amount_in * reserve_out) / reserve_in
    
    if expected_amount_out == 0:
        return 1.0
    
    return 1.0 - (amount_out / expected_amount_out)


def calculate_impermanent_loss(price_ratio_change: float) -> float:
    """计算无常损失"""
    if price_ratio_change <= 0:
        return 0.0
    
    import math
    
    # IL = 2 * sqrt(price_ratio) / (1 + price_ratio) - 1
    sqrt_ratio = math.sqrt(price_ratio_change)
    il = 2 * sqrt_ratio / (1 + price_ratio_change) - 1
    return abs(il)


def is_address_valid(address: str, chain: str) -> bool:
    """验证地址格式"""
    if not address:
        return False
    
    if chain.lower() == "bsc" or chain.lower() == "ethereum":
        # Ethereum地址格式验证
        return (
            address.startswith("0x") and 
            len(address) == 42 and 
            all(c in "0123456789abcdefABCDEF" for c in address[2:])
        )
    elif chain.lower() == "solana":
        # Solana地址格式验证（Base58编码，32字节）
        try:
            import base58
            decoded = base58.b58decode(address)
            return len(decoded) == 32
        except Exception:
            return False
    
    return False


def generate_transaction_id(chain: str, pool_address: str, action: str) -> str:
    """生成交易ID"""
    timestamp = str(int(time.time()))
    data = f"{chain}:{pool_address}:{action}:{timestamp}"
    return hashlib.md5(data.encode()).hexdigest()[:12]


def safe_float(value: Any, default: float = 0.0) -> float:
    """安全转换为浮点数"""
    try:
        if value is None:
            return default
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            # 移除逗号和其他格式字符
            clean_value = value.replace(",", "").replace("$", "").strip()
            return float(clean_value)
        return default
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """安全转换为整数"""
    try:
        if value is None:
            return default
        if isinstance(value, int):
            return value
        if isinstance(value, float):
            return int(value)
        if isinstance(value, str):
            clean_value = value.replace(",", "").replace("$", "").strip()
            return int(float(clean_value))
        return default
    except (ValueError, TypeError):
        return default


def get_utc_timestamp() -> datetime:
    """获取UTC时间戳"""
    return datetime.now(timezone.utc)


def chunks(lst: List[Any], n: int) -> List[List[Any]]:
    """将列表分块"""
    for i in range(0, len(lst), n):
        yield lst[i:i + n]


def merge_dictionaries(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """合并多个字典"""
    result = {}
    for d in dicts:
        if d:
            result.update(d)
    return result


class AsyncContextManager:
    """异步上下文管理器基类"""
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


class Timer:
    """计时器工具"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def start(self):
        """开始计时"""
        self.start_time = time.time()
    
    def stop(self):
        """停止计时"""
        self.end_time = time.time()
    
    def elapsed(self) -> float:
        """获取经过的时间（秒）"""
        if self.start_time is None:
            return 0.0
        
        end = self.end_time or time.time()
        return end - self.start_time
    
    def __enter__(self):
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.stop()


def setup_logging():
    """设置结构化日志"""
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
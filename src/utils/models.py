"""
模型別名文件 - 為了向後兼容
重新導出 models_v3.py 中的類型，並添加缺失的類型
"""

from .models_v3 import (
    ChainType,
    ActionType,
    RiskLevel,
    StrategyType,
    PoolRaw,
    PoolScore,
    Plan,
    RiskAlert,
    RiskThreshold,
    LPPosition,
    HedgePosition,
    Transaction,
    Earnings,
    AgentResult,
    ScoutResult,
    ScorerResult,
    PlannerResult,
    ExecutorResult,
    AuditorResult,
    SystemStatus,
    StrategyConfig,
    PerformanceKPI
)

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, List, Any


# 添加缺失的類型定義
@dataclass
class PerformanceMetrics:
    """性能指標類型 - 為向後兼容"""
    timestamp: datetime
    total_value: float
    total_invested: float
    total_pnl: float
    roi_percent: float
    daily_return_percent: float
    annualized_return_percent: float
    total_fees_earned: float
    gas_costs: float
    net_profit: float
    sharpe_ratio: float
    max_drawdown_percent: float
    volatility_percent: float
    active_positions: int
    trade_stats: Optional[Dict[str, Any]] = None


@dataclass
class PoolMetrics:
    """池子指標類型 - 為向後兼容"""
    pool_address: str
    chain: str
    token_a: str
    token_b: str
    apr: float
    il_risk: float
    tvl: float
    volume_24h: float
    fee_rate: float
    timestamp: datetime
    token_a_reserve: float = 0.0
    token_b_reserve: float = 0.0
    token_a_price: float = 0.0
    token_b_price: float = 0.0
    liquidity_token_supply: float = 0.0
    fees_24h: float = 0.0
    raw_data: Optional[Dict[str, Any]] = None


# 為了確保向後兼容，重新導出所有重要的類型
__all__ = [
    'ChainType',
    'ActionType', 
    'RiskLevel',
    'StrategyType',
    'PoolRaw',
    'PoolScore',
    'Plan',
    'RiskAlert',
    'RiskThreshold',
    'LPPosition',
    'HedgePosition',
    'Transaction',
    'Earnings',
    'AgentResult',
    'ScoutResult',
    'ScorerResult',
    'PlannerResult',
    'ExecutorResult',
    'AuditorResult',
    'SystemStatus',
    'StrategyConfig',
    'PerformanceKPI',
    'PerformanceMetrics',
    'PoolMetrics'
]
"""
Dy-Flow v3 數據模型模塊
完全按照 v3 架構文檔定義的數據結構
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional, Dict, List, Any, Literal
from enum import Enum


# ========== v3 核心數據類別 ==========

@dataclass
class PoolRaw:
    """原始池子數據"""
    id: str
    chain: str  # "BSC" or "SOL"
    tvl_usd: float
    fee24h: float
    fee_tvl: float
    created_at: datetime


@dataclass
class PoolScore:
    """池子評分數據"""
    id: str
    score: float
    hedgeable: bool


@dataclass
class Plan:
    """執行計劃"""
    pool_id: str
    strategy: str
    action: Literal["enter", "exit", "rebalance"]
    params: dict
    needs_llm: bool = False


# ========== v3 策略類型 ==========

class StrategyType(Enum):
    """三大策略類型"""
    DELTA_NEUTRAL_LP = "delta_neutral_lp"      # S-1 藍籌對沖
    LADDER_SINGLE_SIDED = "ladder_single_sided"  # S-2 Meme 火箭
    PASSIVE_HIGH_TVL = "passive_high_tvl"      # S-3 穩定池躺平


class ChainType(Enum):
    """支持的區塊鏈"""
    BSC = "BSC"
    SOL = "SOL"


class ActionType(Enum):
    """操作類型"""
    ENTER = "enter"
    EXIT = "exit"
    REBALANCE = "rebalance"


class RiskLevel(Enum):
    """風險級別"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# ========== v3 風險和警報 ==========

@dataclass
class RiskAlert:
    """風險警報"""
    level: str  # RiskLevel值
    message: str
    pool_id: str
    trigger_type: str  # "price_drop", "tvl_drain", "hedge_fail"
    timestamp: datetime
    resolved: bool = False


@dataclass
class RiskThreshold:
    """風控閾值配置"""
    k_drop: float = 2.5
    min_drop_pct: float = 0.06
    tvl_drop_threshold: float = 0.6  # TVL 30分鐘跌幅
    hedge_fail_max: int = 3
    delta_max_pct: float = 0.3


# ========== v3 位置和對沖 ==========

@dataclass
class LPPosition:
    """流動性位置"""
    id: str
    pool_id: str
    strategy: str
    qty0: float
    qty1: float
    range_lo: Optional[int] = None  # tick for V3
    range_hi: Optional[int] = None  # tick for V3
    ladder_layer: Optional[int] = None  # for ladder strategy
    pnl_unreal: float = 0.0
    status: str = "active"
    created_at: Optional[datetime] = None


@dataclass
class HedgePosition:
    """對沖位置"""
    id: str
    pos_id: str  # 關聯的 LP position ID
    size: float
    entry_px: float
    pnl_unreal: float = 0.0
    risk: str = "low"
    provider: str = "binance"  # "binance", "okx"
    updated_at: Optional[datetime] = None


# ========== v3 交易和收益 ==========

@dataclass
class Transaction:
    """交易記錄"""
    hash: str
    time: datetime
    action: str
    pos_id: Optional[str] = None
    gas_usd: float = 0.0
    status: str = "pending"


@dataclass
class Earnings:
    """收益記錄"""
    id: str
    pos_id: str
    date: datetime
    fee: float
    funding: float
    gas: float
    net_pnl: float


# ========== v3 Agent 結果類型 ==========

@dataclass
class AgentResult:
    """Agent 執行結果"""
    agent_name: str
    data: Any
    timestamp: datetime
    status: Literal["success", "error", "warning"]
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ScoutResult:
    """Scout Agent 結果"""
    pools: List[PoolRaw]
    chain: str
    timestamp: datetime
    total_pools: int


@dataclass
class ScorerResult:
    """Scorer Agent 結果"""
    scored_pools: List[PoolScore]
    timestamp: datetime
    scoring_criteria: Dict[str, float]


@dataclass
class PlannerResult:
    """Planner Agent 結果"""
    plans: List[Plan]
    timestamp: datetime
    total_plans: int
    llm_plans: int  # 需要 LLM 處理的計劃數


@dataclass
class ExecutorResult:
    """Executor Agent 結果"""
    executed_plans: List[str]  # plan IDs
    transactions: List[Transaction]
    timestamp: datetime
    success_count: int
    error_count: int


@dataclass
class AuditorResult:
    """Auditor Agent 結果"""
    report_period: str
    total_pnl: float
    total_fees: float
    total_gas: float
    net_profit: float
    positions_count: int
    timestamp: datetime


# ========== v3 配置和狀態 ==========

@dataclass
class SystemStatus:
    """系統狀態"""
    is_running: bool
    last_check: datetime
    active_positions: int
    total_value_usd: float
    daily_pnl: float
    errors_count: int
    
    # 網絡狀態
    bsc_connected: bool = True
    sol_connected: bool = True
    binance_connected: bool = True
    
    def is_healthy(self) -> bool:
        return (self.bsc_connected and 
                self.sol_connected and 
                self.errors_count < 10)


@dataclass
class StrategyConfig:
    """策略配置"""
    strategy_type: str
    params: Dict[str, Any]
    
    # 通用參數
    max_allocation_usd: float = 10000
    slippage_tolerance: float = 0.005
    gas_cap_usd: float = 10
    
    # 風控參數
    stop_loss_pct: float = 0.08  # 8% 最大回撤
    
    def get_param(self, key: str, default: Any = None) -> Any:
        return self.params.get(key, default)


# ========== v3 數據庫表結構 ==========

@dataclass
class PoolTable:
    """pools 表結構"""
    id: str  # PK
    chain: str
    protocol: str
    name: str
    tvl_usd: float
    fee24h: float
    fee_tvl: float
    created_at: datetime
    score: Optional[float] = None
    updated_at: Optional[datetime] = None


@dataclass
class PositionTable:
    """positions 表結構"""
    id: str  # PK
    wallet: str
    pool_id: str  # FK
    strategy: str
    qty0: float
    qty1: float
    range_lo: Optional[int] = None
    range_hi: Optional[int] = None
    ladder_layer: Optional[int] = None
    pnl_unreal: float = 0.0
    status: str = "active"
    created_at: Optional[datetime] = None


@dataclass
class HedgeTable:
    """hedges 表結構"""
    id: str  # PK
    pos_id: str  # FK
    size: float
    entry_px: float
    pnl_unreal: float = 0.0
    risk: str = "low"
    updated_at: Optional[datetime] = None


@dataclass
class TxTable:
    """txs 表結構"""
    hash: str  # PK
    time: datetime
    action: str
    pos_id: Optional[str] = None  # FK
    gas_usd: float = 0.0
    status: str = "pending"


@dataclass
class EarningsTable:
    """earnings 表結構"""
    id: str  # PK
    pos_id: str  # FK
    date: datetime
    fee: float
    funding: float
    gas: float
    net_pnl: float


@dataclass
class RiskEventTable:
    """risk_events 表結構"""
    id: str  # PK
    type: str  # 移動到前面，避免默認參數問題
    severity: str
    time: datetime
    pos_id: Optional[str] = None  # FK
    resolved: bool = False


@dataclass
class UserFilterTable:
    """user_filter 表結構"""
    id: str  # PK
    rules: Dict[str, Any]  # jsonb


# ========== v3 公式和計算 ==========

@dataclass
class ATRCalculation:
    """ATR 計算結果"""
    value: float
    period: int
    timestamp: datetime


@dataclass
class PriceThreshold:
    """價格閾值計算"""
    threshold_pct: float
    current_price: float
    baseline_price: float
    atr_component: float
    min_drop_component: float


@dataclass
class TVLAlert:
    """TVL 警報"""
    pool_id: str
    current_tvl: float
    baseline_tvl: float
    drop_pct: float
    alert_triggered: bool


# ========== v3 API 響應格式 ==========

@dataclass
class GraphQLPoolResponse:
    """PancakeSwap GraphQL 響應"""
    id: str
    token0: Dict[str, str]  # {symbol, decimals}
    token1: Dict[str, str]
    feeTier: int
    liquidity: str
    volumeUSD: str
    totalValueLockedUSD: str
    sqrtPrice: str
    createdAt: str


@dataclass
class MeteoraAPIResponse:
    """Meteora REST API 響應"""
    id: str
    symbol: str
    tvlUsd: float
    fee24h: float
    feeTvl: float
    createdAgo: str


# ========== v3 性能指標 ==========

@dataclass
class PerformanceKPI:
    """v3 KPI 指標"""
    annual_return_pct: float  # 年化收益率
    max_drawdown_pct: float   # 最大回撤
    exit_time_seconds: float  # 風險退出耗時
    error_rate_daily: float   # 日錯誤率
    fatal_errors_count: int   # Fatal 錯誤數
    
    def meets_targets(self) -> bool:
        """檢查是否達到 KPI 目標"""
        return (
            self.annual_return_pct >= 25.0 and  # 25% 年化
            self.max_drawdown_pct <= 8.0 and    # 8% 最大回撤
            self.exit_time_seconds <= 120 and   # 2分鐘退出
            self.error_rate_daily <= 0.5 and    # 0.5% 錯誤率
            self.fatal_errors_count == 0         # 0 fatal errors
        )
"""
Dy-Flow v3 價格計算和風控工具函數
實現 ATR、kDrop、價格轉換等核心算法
"""

import math
import numpy as np
from typing import List, Tuple, Optional
from decimal import Decimal, ROUND_HALF_UP
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class PriceData:
    """價格數據結構"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float = 0.0


@dataclass
class ATRResult:
    """ATR 計算結果"""
    atr_value: float
    true_ranges: List[float]
    period: int
    current_volatility: float


def calculate_true_range(current: PriceData, previous: PriceData) -> float:
    """
    計算真實波幅 (True Range)
    TR = max(H-L, |H-PC|, |L-PC|)
    """
    if previous is None:
        return current.high - current.low
    
    hl = current.high - current.low
    hc = abs(current.high - previous.close)
    lc = abs(current.low - previous.close)
    
    return max(hl, hc, lc)


def calculate_atr(price_data: List[PriceData], period: int = 14) -> ATRResult:
    """
    計算平均真實波幅 (Average True Range)
    
    Args:
        price_data: 價格數據列表，按時間正序排列
        period: ATR 計算週期，默認 14
        
    Returns:
        ATRResult: ATR 計算結果
    """
    if len(price_data) < period + 1:
        raise ValueError(f"需要至少 {period + 1} 個價格數據點")
    
    true_ranges = []
    
    # 計算所有 True Range
    for i in range(1, len(price_data)):
        tr = calculate_true_range(price_data[i], price_data[i-1])
        true_ranges.append(tr)
    
    if len(true_ranges) < period:
        raise ValueError(f"TR 數據不足，需要至少 {period} 個")
    
    # 計算初始 ATR (前 N 個 TR 的平均值)
    initial_atr = sum(true_ranges[:period]) / period
    
    # 使用指數移動平均更新 ATR
    atr_value = initial_atr
    for i in range(period, len(true_ranges)):
        atr_value = ((atr_value * (period - 1)) + true_ranges[i]) / period
    
    # 計算當前波動率 (相對於價格)
    current_price = price_data[-1].close
    current_volatility = atr_value / current_price if current_price > 0 else 0
    
    return ATRResult(
        atr_value=atr_value,
        true_ranges=true_ranges,
        period=period,
        current_volatility=current_volatility
    )


def calculate_kdrop_threshold(
    current_price: float,
    atr_value: float,
    k_drop: float = 2.5,
    min_drop_pct: float = 0.06
) -> float:
    """
    計算 kDrop 風險閾值
    threshold = max(k_drop * ATR / price, min_drop_pct)
    
    Args:
        current_price: 當前價格
        atr_value: ATR 值
        k_drop: kDrop 倍數，默認 2.5
        min_drop_pct: 最小跌幅閾值，默認 6%
        
    Returns:
        float: 風險閾值 (百分比)
    """
    if current_price <= 0 or atr_value <= 0:
        return min_drop_pct
    
    dynamic_threshold = k_drop * atr_value / current_price
    return max(dynamic_threshold, min_drop_pct)


def calculate_tvl_change_rate(
    current_tvl: float,
    historical_tvl: List[Tuple[datetime, float]],
    window_minutes: int = 30
) -> float:
    """
    計算 TVL 變化率
    
    Args:
        current_tvl: 當前 TVL
        historical_tvl: 歷史 TVL 數據 [(時間, TVL), ...]
        window_minutes: 監控窗口（分鐘）
        
    Returns:
        float: TVL 變化率 (負數表示下跌)
    """
    if not historical_tvl:
        return 0.0
    
    now = datetime.now()
    cutoff_time = now - timedelta(minutes=window_minutes)
    
    # 找到窗口開始時的 TVL
    baseline_tvl = None
    for timestamp, tvl in historical_tvl:
        if timestamp >= cutoff_time:
            baseline_tvl = tvl
            break
    
    if baseline_tvl is None or baseline_tvl <= 0:
        return 0.0
    
    change_rate = (current_tvl - baseline_tvl) / baseline_tvl
    return change_rate


def price_to_tick(price: float, tick_spacing: int) -> int:
    """
    將價格轉換為 tick (Uniswap v3 風格)
    
    Args:
        price: 價格
        tick_spacing: tick 間距
        
    Returns:
        int: tick 值
    """
    if price <= 0:
        raise ValueError("價格必須大於 0")
    
    tick = math.log(price) / math.log(1.0001)
    rounded_tick = round(tick / tick_spacing) * tick_spacing
    return int(rounded_tick)


def tick_to_price(tick: int) -> float:
    """
    將 tick 轉換為價格 (Uniswap v3 風格)
    
    Args:
        tick: tick 值
        
    Returns:
        float: 價格
    """
    return 1.0001 ** tick


def calculate_price_range(
    current_price: float,
    atr_value: float,
    range_multiplier: float = 1.5,
    tick_spacing: int = 60
) -> Tuple[int, int]:
    """
    基於 ATR 計算 LP 價格區間
    
    Args:
        current_price: 當前價格
        atr_value: ATR 值
        range_multiplier: 範圍倍數，默認 1.5
        tick_spacing: tick 間距
        
    Returns:
        Tuple[int, int]: (lower_tick, upper_tick)
    """
    range_size = atr_value * range_multiplier
    
    lower_price = current_price - range_size
    upper_price = current_price + range_size
    
    # 確保價格為正數
    lower_price = max(lower_price, current_price * 0.1)
    
    lower_tick = price_to_tick(lower_price, tick_spacing)
    upper_tick = price_to_tick(upper_price, tick_spacing)
    
    return lower_tick, upper_tick


def format_price(price: float, decimals: int = 6) -> str:
    """
    格式化價格顯示
    
    Args:
        price: 價格
        decimals: 小數位數
        
    Returns:
        str: 格式化後的價格字符串
    """
    if price == 0:
        return "0"
    
    # 使用 Decimal 進行精確計算
    decimal_price = Decimal(str(price))
    
    # 動態調整小數位數
    if price >= 1000:
        decimals = 2
    elif price >= 1:
        decimals = 4
    elif price >= 0.01:
        decimals = 6
    else:
        decimals = 8
    
    # 四捨五入並格式化
    factor = Decimal('10') ** (-decimals)
    rounded_price = decimal_price.quantize(factor, rounding=ROUND_HALF_UP)
    
    return str(rounded_price)


def calculate_impermanent_loss(
    initial_price: float,
    current_price: float
) -> float:
    """
    計算無常損失
    IL = 2 * sqrt(price_ratio) / (1 + price_ratio) - 1
    
    Args:
        initial_price: 初始價格
        current_price: 當前價格
        
    Returns:
        float: 無常損失百分比 (負數表示損失)
    """
    if initial_price <= 0 or current_price <= 0:
        return 0.0
    
    price_ratio = current_price / initial_price
    sqrt_ratio = math.sqrt(price_ratio)
    
    il = 2 * sqrt_ratio / (1 + price_ratio) - 1
    return il


def detect_price_anomaly(
    price_data: List[PriceData],
    z_score_threshold: float = 3.0
) -> Tuple[bool, float]:
    """
    使用 Z-score 檢測價格異常
    
    Args:
        price_data: 價格數據
        z_score_threshold: Z-score 閾值
        
    Returns:
        Tuple[bool, float]: (是否異常, Z-score 值)
    """
    if len(price_data) < 10:
        return False, 0.0
    
    prices = [data.close for data in price_data]
    current_price = prices[-1]
    
    # 計算前面價格的均值和標準差
    historical_prices = prices[:-1]
    mean_price = np.mean(historical_prices)
    std_price = np.std(historical_prices)
    
    if std_price == 0:
        return False, 0.0
    
    z_score = abs(current_price - mean_price) / std_price
    is_anomaly = z_score > z_score_threshold
    
    return is_anomaly, z_score


def calculate_volatility_percentile(
    price_data: List[PriceData],
    lookback_days: int = 30
) -> float:
    """
    計算當前波動率在歷史波動率中的百分位
    
    Args:
        price_data: 價格數據
        lookback_days: 回看天數
        
    Returns:
        float: 百分位 (0-100)
    """
    if len(price_data) < lookback_days:
        return 50.0  # 默認中位數
    
    # 計算歷史波動率
    volatilities = []
    for i in range(1, len(price_data)):
        daily_return = (price_data[i].close - price_data[i-1].close) / price_data[i-1].close
        volatilities.append(abs(daily_return))
    
    if len(volatilities) < 2:
        return 50.0
    
    current_volatility = volatilities[-1]
    historical_volatilities = volatilities[:-1]
    
    # 計算百分位
    rank = sum(1 for vol in historical_volatilities if vol <= current_volatility)
    percentile = (rank / len(historical_volatilities)) * 100
    
    return percentile


# 工具函數集合
class PriceCalculator:
    """價格計算工具類"""
    
    def __init__(self, k_drop: float = 2.5, min_drop_pct: float = 0.06):
        self.k_drop = k_drop
        self.min_drop_pct = min_drop_pct
    
    def get_risk_threshold(self, price_data: List[PriceData]) -> float:
        """獲取當前風險閾值"""
        atr_result = calculate_atr(price_data)
        current_price = price_data[-1].close
        return calculate_kdrop_threshold(
            current_price, 
            atr_result.atr_value, 
            self.k_drop, 
            self.min_drop_pct
        )
    
    def get_lp_range(
        self, 
        price_data: List[PriceData], 
        tick_spacing: int = 60
    ) -> Tuple[int, int]:
        """獲取 LP 價格區間"""
        atr_result = calculate_atr(price_data)
        current_price = price_data[-1].close
        return calculate_price_range(
            current_price, 
            atr_result.atr_value, 
            tick_spacing=tick_spacing
        )
    
    def assess_market_condition(self, price_data: List[PriceData]) -> dict:
        """評估市場狀況"""
        atr_result = calculate_atr(price_data)
        current_price = price_data[-1].close
        
        # 計算各項指標
        risk_threshold = self.get_risk_threshold(price_data)
        is_anomaly, z_score = detect_price_anomaly(price_data)
        volatility_percentile = calculate_volatility_percentile(price_data)
        
        return {
            "current_price": current_price,
            "atr_value": atr_result.atr_value,
            "volatility": atr_result.current_volatility,
            "risk_threshold": risk_threshold,
            "is_price_anomaly": is_anomaly,
            "z_score": z_score,
            "volatility_percentile": volatility_percentile,
            "market_condition": self._classify_market_condition(volatility_percentile)
        }
    
    def _classify_market_condition(self, volatility_percentile: float) -> str:
        """分類市場狀況"""
        if volatility_percentile <= 25:
            return "低波動"
        elif volatility_percentile <= 75:
            return "正常波動"
        else:
            return "高波動"
from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict

class AgentV3(ABC):
    """Base class for Dy-Flow v3 agents."""

    def __init__(self, name: str) -> None:
        self.name = name
        self.running = False

    async def start(self) -> None:
        self.running = True
        await self.run()

    async def stop(self) -> None:
        self.running = False

    @abstractmethod
    async def run(self) -> None:
        """Main execution loop implemented by subclasses."""

    def __repr__(self) -> str:  # pragma: no cover - simple helper
        return f"{self.__class__.__name__}({self.name})"

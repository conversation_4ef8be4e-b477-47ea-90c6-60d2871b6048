from __future__ import annotations

from typing import Dict, Any

from .base import AgentV3

class PlannerAgent(AgentV3):
    """Invokes LLM to provide strategic planning."""

    async def run(self) -> None:
        # Planner is triggered on demand; no loop here.
        pass

    async def generate_plan(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Call LLM with context and return structured plan."""
        # TODO: integrate with LLM service
        return {}

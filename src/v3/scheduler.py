from __future__ import annotations

import asyncio
from typing import List

from .agents.base import AgentV3

class AGNOSchedulerV3:
    """Simple scheduler orchestrating multiple agents."""

    def __init__(self) -> None:
        self.agents: List[AgentV3] = []

    def add_agent(self, agent: AgentV3) -> None:
        self.agents.append(agent)

    async def run(self) -> None:
        tasks = [asyncio.create_task(agent.start()) for agent in self.agents]
        try:
            await asyncio.gather(*tasks)
        finally:
            for agent in self.agents:
                await agent.stop()

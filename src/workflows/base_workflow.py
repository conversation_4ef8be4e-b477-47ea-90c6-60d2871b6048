"""
基础工作流程类
所有DyFlow工作流程的基类，集成Agno Framework
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
import structlog

# Agno Framework imports
try:
    from agno.agent import Agent
    from agno.models.openai import OpenAIChat
    from agno.tools.reasoning import ReasoningTools
    from agno.storage.sqlite import SqliteStorage
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False

logger = structlog.get_logger(__name__)


@dataclass
class WorkflowResult:
    """工作流程执行结果"""
    workflow_id: str
    workflow_name: str
    status: str  # "success", "failed", "partial"
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    steps_completed: int
    total_steps: int
    data: Dict[str, Any]
    errors: List[str]
    metadata: Dict[str, Any]


@dataclass
class WorkflowStep:
    """工作流程步骤"""
    step_id: str
    name: str
    description: str
    agent_name: Optional[str] = None
    dependencies: List[str] = None
    timeout_seconds: int = 300
    retry_attempts: int = 2
    critical: bool = True  # 是否为关键步骤


class BaseWorkflow(ABC):
    """基础工作流程类"""
    
    def __init__(self, workflow_name: str, config: Dict[str, Any] = None):
        self.workflow_name = workflow_name
        self.config = config or {}
        self.workflow_id = f"{workflow_name}_{int(datetime.now().timestamp())}"
        
        # Agno Framework配置
        self.agno_available = AGNO_AVAILABLE
        self.memory_storage = None
        self.agents: Dict[str, Agent] = {}
        
        # 工作流程状态
        self.is_running = False
        self.current_step = 0
        self.steps: List[WorkflowStep] = []
        self.step_results: Dict[str, Any] = {}
        self.errors: List[str] = []
        
        # 执行统计
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        
    async def initialize(self) -> bool:
        """初始化工作流程"""
        try:
            logger.info("workflow_initializing", 
                       workflow_name=self.workflow_name,
                       workflow_id=self.workflow_id)
            
            # 初始化Agno Framework
            if self.agno_available:
                await self._initialize_agno_framework()
            
            # 定义工作流程步骤
            self.steps = await self._define_workflow_steps()
            
            # 初始化Agent
            await self._initialize_agents()
            
            logger.info("workflow_initialized",
                       workflow_name=self.workflow_name,
                       steps_count=len(self.steps),
                       agno_available=self.agno_available)
            
            return True
            
        except Exception as e:
            logger.error("workflow_initialization_failed",
                        workflow_name=self.workflow_name,
                        error=str(e))
            self.errors.append(f"初始化失败: {e}")
            return False
    
    async def execute(self) -> WorkflowResult:
        """执行工作流程"""
        if self.is_running:
            raise RuntimeError("工作流程已在运行中")
        
        self.is_running = True
        self.start_time = datetime.now()
        
        try:
            logger.info("workflow_execution_started",
                       workflow_name=self.workflow_name,
                       workflow_id=self.workflow_id,
                       total_steps=len(self.steps))
            
            # 执行所有步骤
            for i, step in enumerate(self.steps):
                self.current_step = i
                
                try:
                    step_result = await self._execute_step(step)
                    self.step_results[step.step_id] = step_result
                    
                    logger.info("workflow_step_completed",
                               workflow_id=self.workflow_id,
                               step_id=step.step_id,
                               step_name=step.name)
                    
                except Exception as e:
                    error_msg = f"步骤 {step.name} 执行失败: {e}"
                    self.errors.append(error_msg)
                    logger.error("workflow_step_failed",
                                workflow_id=self.workflow_id,
                                step_id=step.step_id,
                                error=str(e))
                    
                    # 如果是关键步骤失败，停止执行
                    if step.critical:
                        break
            
            # 生成最终结果
            result = await self._generate_workflow_result()
            
            logger.info("workflow_execution_completed",
                       workflow_name=self.workflow_name,
                       workflow_id=self.workflow_id,
                       status=result.status,
                       duration=result.duration_seconds)
            
            return result
            
        except Exception as e:
            logger.error("workflow_execution_failed",
                        workflow_name=self.workflow_name,
                        error=str(e))
            self.errors.append(f"工作流程执行失败: {e}")
            return await self._generate_workflow_result()
            
        finally:
            self.is_running = False
            self.end_time = datetime.now()
    
    async def _initialize_agno_framework(self) -> None:
        """初始化Agno Framework"""
        if not self.agno_available:
            return
        
        try:
            # 初始化内存存储
            self.memory_storage = SqliteStorage(
                db_file=f"data/agno_memory/{self.workflow_name}_sessions.db",
                auto_upgrade_schema=True
            )
            
            logger.info("agno_framework_initialized",
                       workflow_name=self.workflow_name)
            
        except Exception as e:
            logger.warning("agno_framework_init_failed",
                          workflow_name=self.workflow_name,
                          error=str(e))
            self.agno_available = False
    
    async def _execute_step(self, step: WorkflowStep) -> Dict[str, Any]:
        """执行单个工作流程步骤"""
        step_start = datetime.now()
        
        try:
            # 检查依赖
            if step.dependencies:
                for dep in step.dependencies:
                    if dep not in self.step_results:
                        raise RuntimeError(f"依赖步骤 {dep} 未完成")
            
            # 执行步骤
            if step.agent_name and step.agent_name in self.agents:
                # 使用Agent执行
                agent = self.agents[step.agent_name]
                result = await self._execute_with_agent(agent, step)
            else:
                # 使用自定义方法执行
                result = await self._execute_custom_step(step)
            
            duration = (datetime.now() - step_start).total_seconds()
            
            return {
                "status": "success",
                "result": result,
                "duration_seconds": duration,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            duration = (datetime.now() - step_start).total_seconds()
            return {
                "status": "failed",
                "error": str(e),
                "duration_seconds": duration,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _execute_with_agent(self, agent: Agent, step: WorkflowStep) -> Any:
        """使用Agent执行步骤"""
        if not self.agno_available:
            raise RuntimeError("Agno Framework不可用")
        
        # 准备Agent输入
        agent_input = await self._prepare_agent_input(step)
        
        # 执行Agent
        response = await agent.arun(agent_input)
        
        return response.content if hasattr(response, 'content') else response
    
    async def _generate_workflow_result(self) -> WorkflowResult:
        """生成工作流程结果"""
        end_time = self.end_time or datetime.now()
        duration = (end_time - self.start_time).total_seconds() if self.start_time else 0
        
        completed_steps = sum(1 for result in self.step_results.values() 
                             if result.get('status') == 'success')
        
        # 确定状态
        if len(self.errors) == 0:
            status = "success"
        elif completed_steps > 0:
            status = "partial"
        else:
            status = "failed"
        
        return WorkflowResult(
            workflow_id=self.workflow_id,
            workflow_name=self.workflow_name,
            status=status,
            start_time=self.start_time,
            end_time=end_time,
            duration_seconds=duration,
            steps_completed=completed_steps,
            total_steps=len(self.steps),
            data=self.step_results,
            errors=self.errors,
            metadata={
                "agno_enhanced": self.agno_available,
                "agents_used": list(self.agents.keys()),
                "config": self.config
            }
        )
    
    # 抽象方法 - 子类必须实现
    @abstractmethod
    async def _define_workflow_steps(self) -> List[WorkflowStep]:
        """定义工作流程步骤"""
        pass
    
    @abstractmethod
    async def _initialize_agents(self) -> None:
        """初始化所需的Agent"""
        pass
    
    @abstractmethod
    async def _execute_custom_step(self, step: WorkflowStep) -> Any:
        """执行自定义步骤"""
        pass
    
    @abstractmethod
    async def _prepare_agent_input(self, step: WorkflowStep) -> str:
        """准备Agent输入"""
        pass

import asyncio
from src.tools.pool_scanner_tool import Pool<PERSON><PERSON><PERSON>Tool
from src.tools.pool_scoring_tool import PoolScoringTool
from src.tools.supabase_db_tool import SupabaseDbTool

class DataScout:
    def __init__(self):
        self.scanner = PoolScannerTool()

    def fetch_pool_data(self, chains):
        return self.scanner.scan_pools(chains)

class PoolAnalyzer:
    def __init__(self):
        self.scorer = PoolScoringTool()

    def score_pools(self, pools):
        return self.scorer.score_pools(pools)

class DataManager:
    def __init__(self):
        self.db = SupabaseDbTool()

    def save_results(self, results):
        return self.db.save(results)

class EnhancedLPMonitoringWorkflow:
    """
    增强版 LP 监控工作流，集成了 PoolScannerTool、PoolScoringTool、SupabaseDbTool，
    并由 DataScout、PoolAnalyzer、DataManager 三个 Agent 协作完成。
    """

    def __init__(self):
        self.data_scout = DataScout()
        self.pool_analyzer = PoolAnalyzer()
        self.data_manager = DataManager()

    def monitor_pools_with_tools(self, chains):
        pools = self.data_scout.fetch_pool_data(chains)
        scores = self.pool_analyzer.score_pools(pools)
        self.data_manager.save_results(scores)
        return scores

    async def monitor_pools_with_tools_async(self, chains):
        loop = asyncio.get_event_loop()
        pools = await loop.run_in_executor(None, self.data_scout.fetch_pool_data, chains)
        scores = await loop.run_in_executor(None, self.pool_analyzer.score_pools, pools)
        await loop.run_in_executor(None, self.data_manager.save_results, scores)
        return scores

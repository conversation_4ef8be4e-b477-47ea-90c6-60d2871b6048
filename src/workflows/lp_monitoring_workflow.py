"""
LP监控工作流程 - 基于Agno Workflow
专注于BSC和Solana LP池子的监控和分析
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

from agno.workflow import Workflow, RunResponse
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.storage.sqlite import SqliteStorage

logger = structlog.get_logger(__name__)


class LPMonitoringWorkflow(Workflow):
    """LP监控工作流程 - 继承自Agno Workflow"""

    description: str = "DyFlow LP池子监控和分析工作流程"

    # 数据收集Agent
    data_scout: Agent = Agent(
        name="DataScout",
        role="LP池子数据收集专家",
        model=OpenAIChat(id="gpt-4o"),
        instructions=[
            "你是DyFlow的数据收集专家。",
            "负责从BSC和Solana链上收集LP池子数据。",
            "专注于获取准确、实时的池子信息。",
            "包括TVL、交易量、手续费率等关键指标。",
            "确保数据质量和完整性。",
            "优先收集高TVL和高交易量的优质池子。"
        ],
        reasoning=False,
        show_tool_calls=False,
        markdown=True
    )

    # 池子分析Agent
    pool_analyzer: Agent = Agent(
        name="PoolAnalyzer",
        role="LP池子分析专家",
        model=OpenAIChat(id="gpt-4o"),
        instructions=[
            "你是DyFlow的LP池子分析专家。",
            "负责深度分析LP池子的性能指标。",
            "评估池子的收益潜力和风险水平。",
            "计算APR、无常损失、流动性深度等关键指标。",
            "提供池子质量评分和投资建议。",
            "专注于BSC和Solana链上的主要DEX协议。",
            "使用6因子评分系统进行综合评估。"
        ],
        reasoning=False,
        show_tool_calls=False,
        markdown=True
    )

    # 风险评估Agent
    risk_assessor: Agent = Agent(
        name="RiskAssessor",
        role="风险评估专家",
        model=OpenAIChat(id="gpt-4o"),
        instructions=[
            "你是DyFlow的风险评估专家。",
            "负责评估LP池子的各种风险。",
            "包括无常损失、流动性风险、价格风险等。",
            "监控市场条件和波动性。",
            "提供详细的风险评估报告。",
            "建议风险缓解策略和对冲方案。",
            "识别高风险池子并发出预警。"
        ],
        reasoning=False,
        show_tool_calls=False,
        markdown=True
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger = structlog.get_logger("lp_monitoring_workflow")

        # 配置监控参数
        self.monitoring_config = {
            'supported_chains': ['bsc', 'solana'],
            'max_pools_per_chain': 10,
            'min_tvl_threshold': 50000,
            'min_volume_24h': 10000,
            'check_interval_minutes': 5
        }
    
    def run(self, **kwargs):
        """执行LP池子监控工作流程"""
        chains = kwargs.get('chains', self.monitoring_config['supported_chains'])
        max_pools = kwargs.get('max_pools', self.monitoring_config['max_pools_per_chain'])

        self.logger.info("lp_monitoring_started",
                       chains=chains,
                       max_pools=max_pools)

        # 检查缓存
        cache_key = f"monitor_{'-'.join(chains)}_{max_pools}"
        cached_result = self.session_state.get(cache_key)
        if cached_result:
            self.logger.info("returning_cached_result")
            yield RunResponse(content=cached_result)
            return

        # 第1步: 真实数据收集
        print("📡 正在收集真实池子数据...")

        # 使用PoolScannerTool收集真实数据
        real_pools_data = self._collect_real_pools_data_sync(chains, max_pools)

        data_collection_prompt = f"""
        作为数据收集专家，我已经收集到以下真实的LP池子数据：

        收集结果：
        - 目标链: {', '.join(chains)}
        - 实际收集池子数: {len(real_pools_data)}
        - 数据来源: PancakeSwap V3 API (BSC) + Meteora API (Solana)

        池子数据样本 (前3个):
        {real_pools_data[:3] if real_pools_data else "无数据"}

        请分析这些真实数据的质量和特征，并提供数据收集总结。
        """

        data_result = self.data_scout.run(data_collection_prompt)

        # 第2步: 真实池子分析
        print("📊 正在分析真实池子数据...")

        analysis_prompt = f"""
        基于收集到的真实LP池子数据，请进行深度分析：

        真实池子数据 ({len(real_pools_data)} 个池子):
        {self._format_pools_for_analysis(real_pools_data)}

        分析维度：
        1. 收益潜力评估 - 基于真实APR和fee_tvl数据
        2. 流动性分析 - 基于真实TVL和交易量数据
        3. 池子质量评分 - 综合评分 (0-100分，6因子评分系统)
        4. 投资建议 - BUY/HOLD/AVOID
        5. 最佳池子推荐 - 前5名最佳池子

        数据收集总结：
        {data_result.content if hasattr(data_result, 'content') else str(data_result)}

        请基于这些真实数据提供详细的分析报告，包括具体的数值计算和推理过程。
        """

        analysis_result = self.pool_analyzer.run(analysis_prompt)

        # 第3步: 真实数据风险评估
        print("🛡️ 正在进行风险评估...")

        risk_prompt = f"""
        基于真实池子数据进行风险评估：

        真实池子风险数据:
        {self._format_risk_data(real_pools_data)}

        风险评估维度：
        1. 无常损失风险 - 基于代币价格相关性和历史波动
        2. 流动性风险 - 基于真实TVL稳定性和深度
        3. 智能合约风险 - 基于协议安全性和审计状态
        4. 市场风险 - 基于真实交易量波动性
        5. 集中度风险 - 基于池子分布和持仓集中度

        池子分析结果：
        {analysis_result.content if hasattr(analysis_result, 'content') else str(analysis_result)}

        请基于真实数据提供详细的风险评估报告，包括：
        - 每个池子的风险等级 (低/中/高)
        - 具体风险因素识别和量化
        - 风险缓解建议和策略
        - 需要避免的高风险池子清单
        """

        risk_result = self.risk_assessor.run(risk_prompt)

        # 整合结果 (包含真实数据)
        monitoring_result = {
            "workflow_id": f"monitor_{int(datetime.now().timestamp())}",
            "timestamp": datetime.now().isoformat(),
            "status": "completed",
            "chains": chains,
            "max_pools": max_pools,
            "real_data_used": True,  # 标记使用了真实数据
            "results": {
                "data_collection": {
                    "status": "completed",
                    "content": data_result.content if hasattr(data_result, 'content') else str(data_result),
                    "real_pools_count": len(real_pools_data),
                    "data_source": "PancakeSwap V3 API + Meteora API"
                },
                "pool_analysis": {
                    "status": "completed",
                    "content": analysis_result.content if hasattr(analysis_result, 'content') else str(analysis_result),
                    "analyzed_pools": len(real_pools_data)
                },
                "risk_assessment": {
                    "status": "completed",
                    "content": risk_result.content if hasattr(risk_result, 'content') else str(risk_result),
                    "risk_pools_identified": len([p for p in real_pools_data if p.get('fee_tvl', 0) > 100])
                },
                "real_pools_data": real_pools_data[:5] if real_pools_data else []  # 保存前5个真实池子数据
            },
            "workflow_metadata": {
                "agents_used": 3,
                "agno_enhanced": True,
                "real_api_calls": True,
                "data_sources": ["PancakeSwap V3", "Meteora"],
                "total_pools_scanned": len(real_pools_data)
            }
        }

        # 缓存结果
        self.session_state[cache_key] = monitoring_result

        # 返回RunResponse对象
        yield RunResponse(content=monitoring_result)
    
    def monitor_pools(self, chains: List[str] = None, max_pools: int = None) -> Dict[str, Any]:
        """执行LP池子监控 - 简化版本"""
        try:
            # 调用主要的run方法
            result_generator = self.run(chains=chains, max_pools=max_pools)
            # 获取第一个结果
            run_response = next(result_generator)
            # 提取content
            if hasattr(run_response, 'content'):
                return run_response.content
            else:
                return run_response
        except Exception as e:
            self.logger.error("lp_monitoring_failed", error=str(e))
            return {
                "workflow_id": f"monitor_failed_{int(datetime.now().timestamp())}",
                "timestamp": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e),
                "chains": chains or self.monitoring_config['supported_chains']
            }
    
    async def _collect_real_pools_data(self, chains: List[str], max_pools: int) -> List[Dict[str, Any]]:
        """收集真实的池子数据"""
        try:
            from src.tools.pool_scanner_tool import PoolScannerTool

            scanner = PoolScannerTool()
            all_pools = []

            for chain in chains:
                try:
                    self.logger.info("collecting_real_data", chain=chain)

                    # 设置过滤条件
                    filters = {
                        'min_tvl': self.monitoring_config.get('min_tvl_threshold', 50000),
                        'min_volume_24h': self.monitoring_config.get('min_volume_24h', 10000),
                        'max_pools': max_pools,
                        'min_fee_tvl': 5.0
                    }

                    # 调用真实API
                    result = await scanner.run(chain, filters)

                    if result.get('pools'):
                        pools = result['pools']
                        all_pools.extend(pools)
                        self.logger.info("real_data_collected",
                                       chain=chain,
                                       pools_count=len(pools))
                    else:
                        self.logger.warning("no_pools_found",
                                          chain=chain,
                                          error=result.get('error'))

                except Exception as e:
                    self.logger.error("real_data_collection_failed",
                                    chain=chain,
                                    error=str(e))
                    continue

            # 按fee_tvl排序并限制数量
            sorted_pools = sorted(all_pools, key=lambda p: p.get('fee_tvl', 0), reverse=True)
            final_pools = sorted_pools[:max_pools * len(chains)]

            self.logger.info("real_data_collection_completed",
                           total_pools=len(final_pools),
                           chains=chains)

            return final_pools

        except Exception as e:
            self.logger.error("real_data_collection_error", error=str(e))
            return []

    def _collect_real_pools_data_sync(self, chains: List[str], max_pools: int) -> List[Dict[str, Any]]:
        """收集真实的池子数据 - 同步版本"""
        try:
            import asyncio
            from src.tools.pool_scanner_tool import PoolScannerToolSync

            scanner = PoolScannerToolSync()
            all_pools = []

            for chain in chains:
                try:
                    self.logger.info("collecting_real_data_sync", chain=chain)

                    # 设置过滤条件
                    filters = {
                        'min_tvl': self.monitoring_config.get('min_tvl_threshold', 50000),
                        'min_volume_24h': self.monitoring_config.get('min_volume_24h', 10000),
                        'max_pools': max_pools,
                        'min_fee_tvl': 5.0
                    }

                    # 调用真实API (同步版本)
                    result = scanner.scan_pools(chain, filters)

                    if result.get('pools'):
                        pools = result['pools']
                        all_pools.extend(pools)
                        self.logger.info("real_data_collected_sync",
                                       chain=chain,
                                       pools_count=len(pools))
                    else:
                        self.logger.warning("no_pools_found_sync",
                                          chain=chain,
                                          error=result.get('error'))

                except Exception as e:
                    self.logger.error("real_data_collection_failed_sync",
                                    chain=chain,
                                    error=str(e))
                    continue

            # 按fee_tvl排序并限制数量
            sorted_pools = sorted(all_pools, key=lambda p: p.get('fee_tvl', 0), reverse=True)
            final_pools = sorted_pools[:max_pools * len(chains)]

            self.logger.info("real_data_collection_completed_sync",
                           total_pools=len(final_pools),
                           chains=chains)

            return final_pools

        except Exception as e:
            self.logger.error("real_data_collection_error_sync", error=str(e))
            return []

    def _format_pools_for_analysis(self, pools_data: List[Dict[str, Any]]) -> str:
        """格式化池子数据用于Agent分析"""
        if not pools_data:
            return "无池子数据"

        formatted_pools = []
        for i, pool in enumerate(pools_data[:10]):  # 只显示前10个
            formatted_pool = f"""
池子 #{i+1}:
- 地址: {pool.get('id', 'N/A')}
- 链: {pool.get('chain', 'N/A')}
- 交易对: {pool.get('pair_name', f"{pool.get('token0', 'N/A')}/{pool.get('token1', 'N/A')}")}
- TVL: ${pool.get('tvl_usd', 0):,.2f}
- 24h交易量: ${pool.get('volume_24h', 0):,.2f}
- 24h手续费: ${pool.get('fee24h', 0):,.2f}
- 年化费率: {pool.get('fee_tvl', 0):.2f}%
- 手续费率: {pool.get('fee_rate', 0)*100:.3f}%
"""
            formatted_pools.append(formatted_pool)

        summary = f"""
数据总结:
- 总池子数: {len(pools_data)}
- 平均TVL: ${sum(p.get('tvl_usd', 0) for p in pools_data) / len(pools_data):,.2f}
- 平均年化费率: {sum(p.get('fee_tvl', 0) for p in pools_data) / len(pools_data):.2f}%
- 涉及链: {', '.join(set(p.get('chain', 'N/A') for p in pools_data))}

详细池子信息:
{''.join(formatted_pools)}
"""
        return summary

    def _format_risk_data(self, pools_data: List[Dict[str, Any]]) -> str:
        """格式化池子数据用于风险评估"""
        if not pools_data:
            return "无池子数据"

        # 计算风险指标
        total_tvl = sum(p.get('tvl_usd', 0) for p in pools_data)
        avg_volume = sum(p.get('volume_24h', 0) for p in pools_data) / len(pools_data)
        high_risk_pools = [p for p in pools_data if p.get('fee_tvl', 0) > 100]  # 年化费率>100%

        risk_summary = f"""
风险数据总结:
- 总池子数: {len(pools_data)}
- 总TVL: ${total_tvl:,.2f}
- 平均24h交易量: ${avg_volume:,.2f}
- 高风险池子数 (年化费率>100%): {len(high_risk_pools)}
- TVL集中度: {max(p.get('tvl_usd', 0) for p in pools_data) / total_tvl * 100:.1f}% (最大池子占比)

风险分类:
"""

        # 按风险等级分类
        for i, pool in enumerate(pools_data[:5]):  # 只显示前5个
            tvl = pool.get('tvl_usd', 0)
            volume = pool.get('volume_24h', 0)
            fee_tvl = pool.get('fee_tvl', 0)

            # 简单风险评估
            risk_level = "低"
            if fee_tvl > 200 or tvl < 100000:
                risk_level = "高"
            elif fee_tvl > 50 or volume / tvl < 0.1:
                risk_level = "中"

            risk_summary += f"""
池子 #{i+1} - {pool.get('pair_name', 'N/A')}:
- 风险等级: {risk_level}
- TVL: ${tvl:,.2f}
- 交易量/TVL比率: {volume/tvl*100:.1f}% (流动性指标)
- 年化费率: {fee_tvl:.1f}% (收益风险指标)
"""

        return risk_summary

    def update_monitoring_config(self, **kwargs):
        """更新监控配置"""
        self.monitoring_config.update(kwargs)
        self.logger.info("monitoring_config_updated", config=self.monitoring_config)

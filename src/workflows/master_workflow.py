"""
主控制工作流程 - 基于Agno Workflow
协调所有子工作流程，提供完整的LP管理解决方案
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

from agno.workflow import Workflow, RunResponse
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.storage.sqlite import SqliteStorage

from .lp_monitoring_workflow import LPMonitoringWorkflow
from .lp_analysis_workflow import LPAnalysisWorkflow
from .lp_execution_workflow import LPExecutionWorkflow

logger = structlog.get_logger(__name__)


class MasterWorkflow(Workflow):
    """主控制工作流程 - 协调所有子工作流程"""
    
    description: str = "DyFlow主控制工作流程 - 完整的LP管理解决方案"
    
    # 主控制Agent
    master_controller: Agent = Agent(
        name="MasterController",
        role="主控制专家",
        model=OpenAIChat(id="gpt-4o"),
        instructions=[
            "你是DyFlow的主控制专家。",
            "负责协调所有子工作流程的执行。",
            "根据市场条件决定执行策略。",
            "优化工作流程的执行顺序和时机。",
            "监控整体系统性能和状态。",
            "提供全局决策和战略指导。"
        ],
        reasoning=False,
        show_tool_calls=False,
        markdown=True
    )
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger = structlog.get_logger("master_workflow")
        
        # 初始化子工作流程
        self.monitoring_workflow = LPMonitoringWorkflow()
        self.analysis_workflow = LPAnalysisWorkflow()
        self.execution_workflow = LPExecutionWorkflow()
        
        # 主控制配置
        self.master_config = {
            'auto_execution_enabled': False,   # 自动执行开关
            'risk_tolerance': 'medium',        # 风险容忍度
            'execution_threshold': 0.15,       # 15%执行阈值
            'monitoring_interval': 300,        # 5分钟监控间隔
            'max_concurrent_operations': 3     # 最大并发操作数
        }
    
    def run(self, **kwargs):
        """执行完整的LP管理工作流程"""
        operation_type = kwargs.get('operation_type', 'full_cycle')
        chains = kwargs.get('chains', ['bsc', 'solana'])
        max_pools = kwargs.get('max_pools', 10)
        
        self.logger.info("master_workflow_started", 
                       operation_type=operation_type,
                       chains=chains)
        
        # 检查缓存
        cache_key = f"master_{operation_type}_{'-'.join(chains)}_{max_pools}"
        cached_result = self.session_state.get(cache_key)
        if cached_result:
            self.logger.info("returning_cached_master_result")
            yield RunResponse(content=cached_result)
            return
        
        # 第1步: 监控阶段
        self.logger.info("executing_monitoring_phase")
        monitoring_result = self.monitoring_workflow.monitor_pools(chains, max_pools)
        
        # 第2步: 分析阶段
        self.logger.info("executing_analysis_phase")
        pools_data = monitoring_result.get('results', {}).get('data_collection', {}).get('content', [])
        analysis_result = self.analysis_workflow.analyze_pools(pools_data, 'comprehensive')
        
        # 第3步: 决策阶段
        decision_prompt = f"""
        基于监控和分析结果，请制定LP管理决策：
        
        监控结果: {monitoring_result.get('status', 'unknown')}
        分析结果: {analysis_result.get('status', 'unknown')}
        
        决策要求：
        1. 评估当前LP表现
        2. 识别需要调整的位置
        3. 制定调整策略和计划
        4. 评估执行风险和时机
        5. 提供具体的行动建议
        
        系统配置:
        - 自动执行: {'启用' if self.master_config['auto_execution_enabled'] else '禁用'}
        - 风险容忍度: {self.master_config['risk_tolerance']}
        - 执行阈值: {self.master_config['execution_threshold']*100}%
        
        请提供详细的决策报告和建议。
        """
        
        decision_result = self.master_controller.run(decision_prompt)
        
        # 第4步: 执行阶段 (如果启用自动执行)
        execution_result = None
        if self.master_config['auto_execution_enabled']:
            self.logger.info("executing_execution_phase")
            
            # 模拟调整计划
            adjustment_plan = {
                "type": "rebalance",
                "target_pools": pools_data[:3] if pools_data else [],
                "risk_level": self.master_config['risk_tolerance']
            }
            
            execution_result = self.execution_workflow.execute_adjustments(
                adjustment_plan, pools_data
            )
        else:
            self.logger.info("auto_execution_disabled")
            execution_result = {
                "status": "skipped",
                "reason": "自动执行未启用",
                "timestamp": datetime.now().isoformat()
            }
        
        # 整合主控制结果
        master_result = {
            "master_id": f"master_{int(datetime.now().timestamp())}",
            "timestamp": datetime.now().isoformat(),
            "status": "completed",
            "operation_type": operation_type,
            "chains": chains,
            "max_pools": max_pools,
            "phases": {
                "monitoring": {
                    "status": monitoring_result.get('status', 'unknown'),
                    "workflow_id": monitoring_result.get('workflow_id'),
                    "summary": f"监控了{len(chains)}条链，{max_pools}个池子"
                },
                "analysis": {
                    "status": analysis_result.get('status', 'unknown'),
                    "analysis_id": analysis_result.get('analysis_id'),
                    "summary": f"分析了{analysis_result.get('pools_analyzed', 0)}个池子"
                },
                "decision": {
                    "status": "completed",
                    "content": decision_result.content if hasattr(decision_result, 'content') else str(decision_result)
                },
                "execution": {
                    "status": execution_result.get('status', 'unknown'),
                    "execution_id": execution_result.get('execution_id'),
                    "auto_execution": self.master_config['auto_execution_enabled']
                }
            },
            "master_metadata": {
                "workflows_used": 4,
                "agno_enhanced": True,
                "config": self.master_config
            }
        }
        
        # 缓存结果
        self.session_state[cache_key] = master_result
        
        yield RunResponse(content=master_result)
    
    def execute_full_cycle(self, chains: List[str] = None, max_pools: int = None) -> Dict[str, Any]:
        """执行完整的LP管理周期"""
        try:
            # 调用主要的run方法
            result_generator = self.run(
                operation_type='full_cycle',
                chains=chains or ['bsc', 'solana'],
                max_pools=max_pools or 10
            )
            # 获取第一个结果
            run_response = next(result_generator)
            # 提取content
            if hasattr(run_response, 'content'):
                return run_response.content
            else:
                return run_response
        except Exception as e:
            self.logger.error("master_workflow_failed", error=str(e))
            return {
                "master_id": f"master_failed_{int(datetime.now().timestamp())}",
                "timestamp": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e),
                "operation_type": "full_cycle"
            }
    
    def execute_monitoring_only(self, chains: List[str] = None, max_pools: int = None) -> Dict[str, Any]:
        """只执行监控阶段"""
        return self.monitoring_workflow.monitor_pools(chains, max_pools)
    
    def execute_analysis_only(self, pools_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """只执行分析阶段"""
        return self.analysis_workflow.analyze_pools(pools_data)
    
    def execute_execution_only(self, adjustment_plan: Dict[str, Any], positions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """只执行执行阶段"""
        return self.execution_workflow.execute_adjustments(adjustment_plan, positions)
    
    def update_master_config(self, **kwargs):
        """更新主控制配置"""
        self.master_config.update(kwargs)
        self.logger.info("master_config_updated", config=self.master_config)
        
        # 同步更新子工作流程配置
        if 'monitoring_interval' in kwargs:
            self.monitoring_workflow.update_monitoring_config(
                check_interval_minutes=kwargs['monitoring_interval'] // 60
            )
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "master_workflow": "active",
            "sub_workflows": {
                "monitoring": "ready",
                "analysis": "ready", 
                "execution": "ready"
            },
            "config": self.master_config,
            "timestamp": datetime.now().isoformat()
        }

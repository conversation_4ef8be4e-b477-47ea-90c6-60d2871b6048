#!/bin/bash

# DyFlow CLI 启动脚本
# 提供快捷方式启动不同的CLI功能

echo "🌟 DyFlow LP Monitor CLI"
echo "========================"

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法:"
    echo "  ./start_cli.sh demo      # 显示使用说明"
    echo "  ./start_cli.sh snapshot  # 快速快照"
    echo "  ./start_cli.sh monitor   # 实时监控"
    echo "  ./start_cli.sh bsc       # BSC池子"
    echo "  ./start_cli.sh sol       # Solana池子"
    echo "  ./start_cli.sh both      # 两个链"
    exit 1
fi

# 激活虚拟环境（如果存在）
if [ -d ".venv" ]; then
    source .venv/bin/activate
fi

# 根据参数执行相应命令
case $1 in
    "demo")
        python dyflow_cli.py demo
        ;;
    "snapshot")
        python dyflow_cli.py snapshot
        ;;
    "monitor")
        echo "🔍 启动实时监控 (Ctrl+C 退出)..."
        python dyflow_cli.py monitor
        ;;
    "bsc")
        python dyflow_cli.py pools bsc -c 5
        ;;
    "sol"|"solana")
        python dyflow_cli.py pools solana -c 5
        ;;
    "both")
        python dyflow_cli.py pools both -c 3
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "运行 './start_cli.sh demo' 查看使用说明"
        exit 1
        ;;
esac

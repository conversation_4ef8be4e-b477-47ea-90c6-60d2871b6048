#!/bin/bash

# DyFlow Smart Monitor 启动脚本
# 智能LP监控系统

echo "🤖 DyFlow Smart Monitor"
echo "AI-Powered LP Discovery & Analysis"
echo "=================================="

# 检查参数
if [ $# -eq 0 ]; then
    echo ""
    echo "使用方法:"
    echo "  ./start_smart_monitor.sh monitor     # 启动持续监控"
    echo "  ./start_smart_monitor.sh discover    # 一次性发现分析"
    echo "  ./start_smart_monitor.sh analyze     # 详细AI分析过程"
    echo "  ./start_smart_monitor.sh reasoning   # 显示Agent推理过程"
    echo "  ./start_smart_monitor.sh demo-agent  # Agent推理演示"
    echo "  ./start_smart_monitor.sh compare     # 对比多个池子的AI分析"
    echo "  ./start_smart_monitor.sh config      # 显示配置"
    echo "  ./start_smart_monitor.sh fast        # 快速监控 (30s间隔)"
    echo "  ./start_smart_monitor.sh conservative # 保守监控 (高APR阈值)"
    echo ""
    echo "功能特点:"
    echo "  🔍 自动发现新的高APR池子"
    echo "  🤖 AI Agent分析投资策略"
    echo "  📊 实时监控和警报"
    echo "  💰 自动生成买入建议"
    echo "  📈 风险评估和区间建议"
    exit 1
fi

# 激活虚拟环境（如果存在）
if [ -d ".venv" ]; then
    source .venv/bin/activate
fi

# 根据参数执行相应命令
case $1 in
    "monitor")
        echo "🚀 启动智能监控系统..."
        echo "⏰ 扫描间隔: 60秒"
        echo "📊 APR警报阈值: 25%"
        echo "🤖 AI自动分析: 启用"
        echo ""
        echo "按 Ctrl+C 停止监控"
        echo "=================================="
        python dyflow_smart_monitor.py monitor
        ;;
    "discover")
        echo "🔍 执行一次性发现和分析..."
        python dyflow_smart_monitor.py discover -c 5
        ;;
    "analyze")
        echo "🧠 执行详细AI分析..."
        python dyflow_smart_monitor.py analyze -c 3
        ;;
    "reasoning")
        echo "🤖 显示Agent详细推理过程..."
        python dyflow_smart_monitor.py analyze --detailed -c 2
        ;;
    "demo-agent")
        echo "🧠 Agent推理过程演示..."
        python agent_reasoning_demo.py analyze --pool eth-usdt
        ;;
    "compare")
        echo "⚖️ 对比多个池子的AI分析..."
        python agent_reasoning_demo.py compare
        ;;
    "config")
        python dyflow_smart_monitor.py config
        ;;
    "fast")
        echo "⚡ 启动快速监控模式..."
        echo "⏰ 扫描间隔: 30秒"
        echo "📊 APR警报阈值: 20%"
        echo ""
        python dyflow_smart_monitor.py monitor --scan-interval 30 --alert-apr 20
        ;;
    "conservative")
        echo "🛡️ 启动保守监控模式..."
        echo "⏰ 扫描间隔: 120秒"
        echo "📊 APR警报阈值: 50%"
        echo "💰 最小APR: 30%"
        echo ""
        python dyflow_smart_monitor.py monitor --scan-interval 120 --alert-apr 50 --min-apr 30
        ;;
    "no-ai")
        echo "📊 启动无AI分析监控..."
        echo "⏰ 扫描间隔: 60秒"
        echo "🤖 AI分析: 禁用"
        echo ""
        python dyflow_smart_monitor.py monitor --no-analysis
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "运行 './start_smart_monitor.sh' 查看使用说明"
        exit 1
        ;;
esac

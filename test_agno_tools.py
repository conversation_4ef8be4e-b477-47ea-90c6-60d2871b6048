#!/usr/bin/env python3
"""
测试Agno Framework核心工具集的导入和基本功能
"""

def test_tool_imports():
    """测试工具导入"""
    print("🧪 测试Agno Framework核心工具集...")
    
    try:
        # 测试包级别导入
        import agno_tools
        print("✅ agno_tools包导入成功")
        
        # 测试工具状态检查
        available_tools = agno_tools.get_available_tools()
        print(f"✅ 工具状态检查成功: {available_tools['total_tools']}个工具")
        print(f"   Agno Framework可用: {available_tools['agno_framework_available']}")
        
        # 测试单个工具导入
        from agno_tools import (
            PoolScannerTool, PoolScannerToolSync,
            PoolScoringTool, PoolScoringToolSync,
            BinanceHedgeTool, BinanceHedgeToolSync,
            SupabaseDbTool, SupabaseDbToolSync,
            AGNO_AVAILABLE
        )
        print("✅ 所有工具类导入成功")
        
        # 测试工具实例化
        scanner = PoolScannerTool()
        scorer = PoolScoringTool()
        hedge = BinanceHedgeTool(api_key="test", api_secret="test", testnet=True)
        db = SupabaseDbTool(supabase_url="test", supabase_key="test")
        
        print("✅ 所有工具实例化成功")
        
        # 测试工具套件创建
        tool_suite = agno_tools.create_tool_suite()
        print(f"✅ 工具套件创建成功: {len(tool_suite)}个工具实例")
        
        # 验证工具属性
        tools_info = []
        for tool_name, tool_class in [
            ("PoolScannerTool", PoolScannerTool),
            ("PoolScoringTool", PoolScoringTool),
            ("BinanceHedgeTool", BinanceHedgeTool),
            ("SupabaseDbTool", SupabaseDbTool)
        ]:
            if hasattr(tool_class, 'name') and hasattr(tool_class, 'description'):
                tools_info.append({
                    'class': tool_name,
                    'name': tool_class.name,
                    'description': tool_class.description[:50] + "..."
                })
        
        print("✅ 工具属性验证:")
        for info in tools_info:
            print(f"   {info['class']}: {info['name']} - {info['description']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_tool_interface():
    """测试工具接口一致性"""
    print("\n🔍 测试工具接口一致性...")
    
    try:
        from agno_tools import (
            PoolScannerTool, PoolScoringTool, 
            BinanceHedgeTool, SupabaseDbTool
        )
        
        # 检查所有工具都有run方法
        tools = [
            PoolScannerTool(),
            PoolScoringTool(),
            BinanceHedgeTool(),
            SupabaseDbTool()
        ]
        
        for tool in tools:
            if not hasattr(tool, 'run'):
                print(f"❌ {tool.__class__.__name__} 缺少run方法")
                return False
            
            if not callable(getattr(tool, 'run')):
                print(f"❌ {tool.__class__.__name__}.run 不是可调用的")
                return False
        
        print("✅ 所有工具都实现了run方法")
        
        # 检查同步版本
        from agno_tools import (
            PoolScannerToolSync, PoolScoringToolSync,
            BinanceHedgeToolSync, SupabaseDbToolSync
        )
        
        sync_tools = [
            (PoolScannerToolSync(), 'scan_pools'),
            (PoolScoringToolSync(), 'score_pools'),
            (BinanceHedgeToolSync(), 'execute_hedge'),
            (SupabaseDbToolSync(), 'execute_operation')
        ]
        
        for tool, method_name in sync_tools:
            if not hasattr(tool, method_name):
                print(f"❌ {tool.__class__.__name__} 缺少{method_name}方法")
                return False
        
        print("✅ 所有同步版本工具都实现了对应方法")
        return True
        
    except Exception as e:
        print(f"❌ 接口测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 Agno Framework核心工具集验证测试")
    print("=" * 60)
    
    # 运行测试
    import_test = test_tool_imports()
    interface_test = test_tool_interface()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   导入测试: {'✅ 通过' if import_test else '❌ 失败'}")
    print(f"   接口测试: {'✅ 通过' if interface_test else '❌ 失败'}")
    
    if import_test and interface_test:
        print("\n🎉 所有测试通过！Agno Framework核心工具集创建成功！")
        print("\n📋 工具集包含:")
        print("   1. PoolScannerTool - DeFi池子扫描工具")
        print("   2. PoolScoringTool - 6因子动态评分工具") 
        print("   3. BinanceHedgeTool - Binance对冲操作工具")
        print("   4. SupabaseDbTool - Supabase数据库操作工具")
        print("\n🔧 使用方式:")
        print("   from agno_tools import PoolScannerTool, PoolScoringTool")
        print("   # 或创建完整工具套件")
        print("   import agno_tools")
        print("   tools = agno_tools.create_tool_suite()")
        return True
    else:
        print("\n❌ 测试失败，请检查代码实现")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
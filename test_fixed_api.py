#!/usr/bin/env python3
"""
测试修复后的PancakeSwap V3 API连接
验证真实数据获取功能
"""

import asyncio
import sys
import json
from datetime import datetime

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

async def test_fixed_pancakeswap_api():
    """测试修复后的PancakeSwap V3 API"""
    
    try:
        from src.integrations.pancakeswap import PancakeSwapV3Integration
        
        print("🔗 测试修复后的PancakeSwap V3 API连接...")
        
        # 创建API集成实例
        config = {}  # 使用默认配置
        async with PancakeSwapV3Integration(config) as api:
            
            # 测试1: 获取顶级池子
            print("\n📊 测试1: 获取顶级流动性池...")
            pools = await api.get_top_pools(limit=10)
            
            if pools and len(pools) > 0:
                print(f"✅ 成功获取 {len(pools)} 个池子")
                
                # 显示前3个池子的详细信息
                for i, pool in enumerate(pools[:3], 1):
                    token0 = pool['token0']['symbol']
                    token1 = pool['token1']['symbol']
                    volume = float(pool.get('volumeUSD', 0))
                    tvl = float(pool.get('totalValueLockedUSD', 0))
                    fee_tier = int(pool.get('feeTier', 0))
                    liquidity = pool.get('liquidity', '0')
                    
                    print(f"\n   池子 {i}: {token0}/{token1}")
                    print(f"     ID: {pool['id'][:20]}...")
                    print(f"     TVL: ${tvl:,.0f}")
                    print(f"     24h交易量: ${volume:,.0f}")
                    print(f"     手续费率: {fee_tier/10000:.2f}%")
                    print(f"     流动性: {liquidity}")
                    
                    # 计算简单的年化收益率估算
                    if tvl > 0 and volume > 0:
                        daily_fees = volume * (fee_tier / 1000000)  # 日手续费
                        apr = (daily_fees * 365 / tvl) * 100  # 年化收益率
                        print(f"     估算年化收益率: {apr:.2f}%")
                
                # 测试2: 获取特定池子详情
                print(f"\n📋 测试2: 获取池子详情...")
                first_pool_id = pools[0]['id']
                pool_details = await api.get_pool_details(first_pool_id)
                
                if pool_details:
                    print(f"✅ 成功获取池子详情")
                    print(f"   池子ID: {pool_details['id'][:20]}...")
                    print(f"   交易对: {pool_details['token0']['symbol']}/{pool_details['token1']['symbol']}")
                    print(f"   当前价格: {float(pool_details.get('token0Price', 0)):.6f}")
                    print(f"   Sqrt价格: {pool_details.get('sqrtPrice', 'N/A')}")
                    print(f"   当前Tick: {pool_details.get('tick', 'N/A')}")
                else:
                    print("❌ 获取池子详情失败")
                
                return True
            else:
                print("❌ 未获取到池子数据")
                return False
                
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_pool_scanner_with_real_data():
    """测试池子扫描工具使用真实数据"""
    
    try:
        from agno_tools.pool_scanner_tool import PoolScannerTool
        
        print("\n🔍 测试池子扫描工具（真实数据）...")
        
        scanner = PoolScannerTool()
        
        # 使用更宽松的过滤条件来获取更多池子
        result = await scanner.run(
            chain='bsc',
            filters={
                'min_tvl': 1000,      # 降低到$1K
                'min_volume_24h': 500, # 降低到$500
                'max_pools': 15,
                'min_fee_tvl': 1.0     # 降低到1%年化
            }
        )
        
        if result and 'pools' in result:
            pools = result['pools']
            print(f"✅ 扫描成功，找到 {len(pools)} 个符合条件的池子")
            
            if len(pools) > 0:
                # 显示最佳池子
                best_pools = sorted(pools, key=lambda p: p.get('fee_tvl', 0), reverse=True)[:5]
                
                print(f"\n🏆 前5个最佳池子（按年化费率排序）:")
                for i, pool in enumerate(best_pools, 1):
                    print(f"   {i}. {pool.get('pair_name', 'Unknown')}")
                    print(f"      TVL: ${pool.get('tvl_usd', 0):,.0f}")
                    print(f"      24h交易量: ${pool.get('volume_24h', 0):,.0f}")
                    print(f"      24h手续费: ${pool.get('fee24h', 0):,.0f}")
                    print(f"      年化费率: {pool.get('fee_tvl', 0):.1f}%")
                    print(f"      手续费率: {pool.get('fee_rate', 0)*100:.3f}%")
                    print()
                
                return True
            else:
                print("⚠️ 找到0个符合条件的池子，可能需要调整过滤条件")
                return True  # 工具运行正常，只是没找到符合条件的池子
        else:
            error = result.get('error', '未知错误') if result else '无结果'
            print(f"❌ 扫描失败: {error}")
            return False
            
    except Exception as e:
        print(f"❌ 池子扫描测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_agent_with_real_data():
    """测试Agent使用真实数据进行分析"""
    
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        import os
        
        print("\n🤖 测试Agent使用真实数据分析...")
        
        if not os.getenv('OPENAI_API_KEY'):
            print("⚠️ 无OPENAI_API_KEY，跳过AI分析测试")
            return True
        
        # 首先获取真实的池子数据
        from src.integrations.pancakeswap import PancakeSwapV3Integration
        
        config = {}
        async with PancakeSwapV3Integration(config) as api:
            pools = await api.get_top_pools(limit=3)
            
            if not pools or len(pools) == 0:
                print("❌ 无法获取真实池子数据进行AI分析")
                return False
            
            # 选择第一个池子进行分析
            pool = pools[0]
            token0 = pool['token0']['symbol']
            token1 = pool['token1']['symbol']
            tvl = float(pool.get('totalValueLockedUSD', 0))
            volume = float(pool.get('volumeUSD', 0))
            fee_tier = int(pool.get('feeTier', 0))
            
            # 创建分析Agent
            analyzer = Agent(
                name="DeFiPoolAnalyzer",
                role="Analyze DeFi pool performance and provide investment insights",
                model=OpenAIChat(id="gpt-3.5-turbo"),
                instructions=[
                    "You are a professional DeFi analyst.",
                    "Analyze pool data and provide clear investment insights.",
                    "Focus on risk assessment, yield potential, and market conditions.",
                    "Provide actionable recommendations."
                ],
                reasoning=False
            )
            
            # 准备真实数据进行分析
            analysis_prompt = f"""
            请分析以下真实的PancakeSwap V3池子数据：
            
            交易对: {token0}/{token1}
            TVL: ${tvl:,.0f}
            24小时交易量: ${volume:,.0f}
            手续费率: {fee_tier/10000:.2f}%
            池子ID: {pool['id']}
            
            请提供：
            1. 流动性分析
            2. 风险评估
            3. 收益潜力
            4. 投资建议
            
            请用中文回答，保持简洁专业。
            """
            
            print(f"   分析池子: {token0}/{token1}")
            print(f"   TVL: ${tvl:,.0f}")
            print(f"   24h交易量: ${volume:,.0f}")
            
            # 执行AI分析
            response = await analyzer.arun(analysis_prompt)
            
            print(f"\n🧠 AI分析结果:")
            print("=" * 50)
            print(response.content)
            print("=" * 50)
            
            print(f"\n✅ AI分析完成，分析长度: {len(response.content)} 字符")
            return True
            
    except Exception as e:
        print(f"❌ Agent分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("🚀 测试修复后的PancakeSwap V3 API和真实数据功能")
    print("=" * 60)
    
    results = []
    
    # 测试1: 修复后的API连接
    print("第1项测试：修复后的API连接")
    results.append(await test_fixed_pancakeswap_api())
    
    # 测试2: 池子扫描工具使用真实数据
    print("第2项测试：池子扫描工具")
    results.append(await test_pool_scanner_with_real_data())
    
    # 测试3: Agent使用真实数据分析
    print("第3项测试：Agent真实数据分析")
    results.append(await test_agent_with_real_data())
    
    # 总结结果
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print("\n" + "=" * 60)
    print("📋 真实数据测试结果总结")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"通过测试: {passed}/{total}")
    print(f"成功率: {success_rate:.1f}%")
    
    test_names = [
        "修复后的API连接",
        "池子扫描工具",
        "Agent真实数据分析"
    ]
    
    print("\n详细结果：")
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    if success_rate >= 80:
        print("\n🎉 测试成功！现在可以使用真实的PancakeSwap V3数据了")
        print("💡 建议：开始24小时真实数据监控")
        return True
    else:
        print("\n⚠️ 部分测试失败，但基本功能可用")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

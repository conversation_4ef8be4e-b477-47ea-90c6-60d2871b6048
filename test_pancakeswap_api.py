#!/usr/bin/env python3
"""
测试PancakeSwap V3 API连接
使用The Graph的正确端点和API密钥
"""

import asyncio
import sys
import aiohttp
import json
from datetime import datetime

# 添加src到路径
sys.path.insert(0, 'src')
sys.path.insert(0, '.')

async def test_pancakeswap_v3_api():
    """测试PancakeSwap V3 API连接"""
    
    # The Graph API配置
    endpoint = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
    api_key = "9731921233db132a98c2325878e6c153"
    
    # 简单的查询来测试连接
    query = """
    {
      factories(first: 1) {
        id
        poolCount
        txCount
        totalVolumeUSD
      }
      pools(first: 5, orderBy: tvlUSD, orderDirection: desc, where: { tvlUSD_gt: "100000" }) {
        id
        token0 {
          id
          symbol
          name
          decimals
        }
        token1 {
          id
          symbol
          name
          decimals
        }
        feeTier
        liquidity
        volumeUSD
        tvlUSD
        token0Price
        token1Price
      }
    }
    """
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}',
        'User-Agent': 'DyFlow-Test/1.0'
    }
    
    payload = {
        "query": query,
        "variables": {}
    }
    
    print("🔗 测试PancakeSwap V3 API连接...")
    print(f"📡 端点: {endpoint}")
    print(f"🔑 API密钥: {api_key[:10]}...")
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            async with session.post(endpoint, json=payload, headers=headers) as response:
                print(f"📊 响应状态: {response.status}")
                print(f"📋 响应头: {dict(response.headers)}")
                
                if response.status == 200:
                    result = await response.json()
                    
                    if 'errors' in result:
                        print(f"❌ GraphQL错误: {result['errors']}")
                        return False
                    
                    if 'data' in result:
                        data = result['data']
                        
                        # 显示工厂信息
                        if 'factories' in data and len(data['factories']) > 0:
                            factory = data['factories'][0]
                            print(f"\n🏭 PancakeSwap V3 工厂信息:")
                            print(f"   ID: {factory['id']}")
                            print(f"   池子总数: {factory['poolCount']}")
                            print(f"   交易总数: {factory['txCount']}")
                            print(f"   总交易量: ${float(factory['totalVolumeUSD']):,.0f}")
                        
                        # 显示池子信息
                        if 'pools' in data and len(data['pools']) > 0:
                            pools = data['pools']
                            print(f"\n💧 顶级流动性池 (共{len(pools)}个):")
                            
                            for i, pool in enumerate(pools, 1):
                                token0 = pool['token0']['symbol']
                                token1 = pool['token1']['symbol']
                                tvl = float(pool['tvlUSD'])
                                volume = float(pool['volumeUSD'])
                                fee_tier = int(pool['feeTier'])
                                
                                print(f"   {i}. {token0}/{token1}")
                                print(f"      TVL: ${tvl:,.0f}")
                                print(f"      24h交易量: ${volume:,.0f}")
                                print(f"      手续费率: {fee_tier/10000:.2f}%")
                                print(f"      池子ID: {pool['id'][:10]}...")
                                print()
                        
                        print("✅ API连接成功！获取到真实的PancakeSwap V3数据")
                        return True
                    else:
                        print("❌ 响应中没有数据")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP错误 {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False


async def test_specific_pool_query():
    """测试特定池子查询"""
    
    endpoint = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
    api_key = "9731921233db132a98c2325878e6c153"
    
    # 查询特定池子的详细信息
    query = """
    {
      pools(first: 3, orderBy: tvlUSD, orderDirection: desc, where: { tvlUSD_gt: "1000000" }) {
        id
        token0 {
          symbol
          decimals
        }
        token1 {
          symbol
          decimals
        }
        feeTier
        liquidity
        volumeUSD
        tvlUSD
        token0Price
        token1Price
        poolDayData(first: 1, orderBy: date, orderDirection: desc) {
          date
          volumeUSD
          tvlUSD
          feesUSD
        }
        poolHourData(first: 24, orderBy: periodStartUnix, orderDirection: desc) {
          periodStartUnix
          volumeUSD
          tvlUSD
          feesUSD
        }
      }
    }
    """
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}',
        'User-Agent': 'DyFlow-Test/1.0'
    }
    
    payload = {
        "query": query,
        "variables": {}
    }
    
    print("\n🔍 测试详细池子数据查询...")
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            async with session.post(endpoint, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    
                    if 'errors' in result:
                        print(f"❌ GraphQL错误: {result['errors']}")
                        return False
                    
                    if 'data' in result and 'pools' in result['data']:
                        pools = result['data']['pools']
                        print(f"✅ 获取到 {len(pools)} 个池子的详细数据")
                        
                        for i, pool in enumerate(pools, 1):
                            token0 = pool['token0']['symbol']
                            token1 = pool['token1']['symbol']
                            tvl = float(pool['tvlUSD'])
                            volume = float(pool['volumeUSD'])
                            
                            print(f"\n📊 池子 {i}: {token0}/{token1}")
                            print(f"   TVL: ${tvl:,.0f}")
                            print(f"   24h交易量: ${volume:,.0f}")
                            
                            # 显示24小时数据
                            if pool['poolDayData'] and len(pool['poolDayData']) > 0:
                                day_data = pool['poolDayData'][0]
                                fees_24h = float(day_data['feesUSD'])
                                print(f"   24h手续费: ${fees_24h:,.0f}")
                                
                                # 计算年化收益率
                                if tvl > 0:
                                    apr = (fees_24h * 365 / tvl) * 100
                                    print(f"   年化费率: {apr:.2f}%")
                            
                            # 显示小时数据统计
                            if pool['poolHourData']:
                                hour_data = pool['poolHourData']
                                total_volume_24h = sum(float(h['volumeUSD']) for h in hour_data)
                                total_fees_24h = sum(float(h['feesUSD']) for h in hour_data)
                                print(f"   24h总交易量(小时数据): ${total_volume_24h:,.0f}")
                                print(f"   24h总手续费(小时数据): ${total_fees_24h:,.0f}")
                        
                        return True
                    else:
                        print("❌ 未获取到池子数据")
                        return False
                else:
                    error_text = await response.text()
                    print(f"❌ HTTP错误 {response.status}: {error_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 PancakeSwap V3 API连接测试")
    print("=" * 50)
    
    # 测试1: 基本连接
    success1 = await test_pancakeswap_v3_api()
    
    # 测试2: 详细查询
    success2 = await test_specific_pool_query()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结")
    print("=" * 50)
    print(f"基本连接测试: {'✅ 通过' if success1 else '❌ 失败'}")
    print(f"详细查询测试: {'✅ 通过' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("\n🎉 所有测试通过！PancakeSwap V3 API连接正常")
        print("💡 现在可以在项目中使用真实的池子数据了")
        return True
    else:
        print("\n❌ 部分测试失败，需要检查API配置")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)

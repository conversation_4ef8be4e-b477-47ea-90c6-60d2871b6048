"""
测试真实池子数据查询和风险评估
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.pool_scanner_tool import PoolScannerToolSync
from src.tools.pool_scoring_tool import PoolScoringTool
from src.agents.enhanced_planner_with_tools import EnhancedPlannerAgent

def test_real_pools_query():
    """测试查询真实池子数据"""
    print("=== 测试真实池子数据查询 ===")
    
    # 使用同步版本的扫描工具
    scanner = PoolScannerToolSync()
    
    # 查询 BSC 链上的池子
    print("正在查询 BSC 链上的池子数据...")
    bsc_result = scanner.scan_pools('bsc', {
        'min_tvl': 100000,  # 最小 TVL $100K
        'max_pools': 10,    # 限制返回 10 个池子
        'min_volume_24h': 50000  # 最小24h交易量 $50K
    })
    
    if bsc_result.get('error'):
        print(f"BSC 查询失败: {bsc_result['error']}")
    else:
        pools = bsc_result.get('pools', [])
        print(f"BSC 查询成功，找到 {len(pools)} 个池子")
        for i, pool in enumerate(pools[:3], 1):  # 显示前3个
            print(f"  {i}. {pool['pair_name']} - TVL: ${pool['tvl_usd']:,.0f}, 年化费率: {pool['fee_tvl']:.2f}%")
    
    print()
    
    # 查询 Solana 链上的池子
    print("正在查询 Solana 链上的池子数据...")
    sol_result = scanner.scan_pools('solana', {
        'min_tvl': 50000,   # 最小 TVL $50K
        'max_pools': 10,    # 限制返回 10 个池子
        'min_volume_24h': 20000  # 最小24h交易量 $20K
    })
    
    if sol_result.get('error'):
        print(f"Solana 查询失败: {sol_result['error']}")
    else:
        pools = sol_result.get('pools', [])
        print(f"Solana 查询成功，找到 {len(pools)} 个池子")
        for i, pool in enumerate(pools[:3], 1):  # 显示前3个
            print(f"  {i}. {pool['pair_name']} - TVL: ${pool['tvl_usd']:,.0f}, 年化费率: {pool['fee_tvl']:.2f}%")
    
    return bsc_result, sol_result

def test_agent_risk_assessment(pools_data):
    """测试 Agent 风险评估"""
    print("\n=== 测试 Agent 风险评估 ===")
    
    try:
        # 创建增强版规划 Agent
        planner = EnhancedPlannerAgent()
        
        # 合并两条链的池子数据
        all_pools = []
        for result in pools_data:
            if not result.get('error'):
                all_pools.extend(result.get('pools', []))
        
        if not all_pools:
            print("没有有效的池子数据进行风险评估")
            return
        
        print(f"正在对 {len(all_pools)} 个池子进行风险评估...")
        
        # 模拟 Agent 评估过程
        # 1. 数据分析
        print("1. 数据分析阶段...")
        high_tvl_pools = [p for p in all_pools if p['tvl_usd'] > 500000]
        high_yield_pools = [p for p in all_pools if p['fee_tvl'] > 50]
        
        print(f"   - 高TVL池子 (>$500K): {len(high_tvl_pools)} 个")
        print(f"   - 高收益池子 (>50%年化): {len(high_yield_pools)} 个")
        
        # 2. 策略规划
        print("2. 策略规划阶段...")
        recommended_pools = []
        for pool in all_pools:
            risk_score = calculate_risk_score(pool)
            pool['risk_score'] = risk_score
            pool['risk_level'] = get_risk_level(risk_score)
            
            # 选择低风险、中等收益的池子
            if risk_score < 0.4 and pool['fee_tvl'] > 20:
                recommended_pools.append(pool)
        
        recommended_pools.sort(key=lambda x: x['fee_tvl'], reverse=True)
        
        print(f"   - 推荐池子数量: {len(recommended_pools)} 个")
        
        # 3. 风险管理
        print("3. 风险管理评估...")
        total_recommended_tvl = sum(p['tvl_usd'] for p in recommended_pools)
        avg_risk_score = sum(p['risk_score'] for p in recommended_pools) / len(recommended_pools) if recommended_pools else 0
        
        print(f"   - 推荐池子总TVL: ${total_recommended_tvl:,.0f}")
        print(f"   - 平均风险评分: {avg_risk_score:.3f}")
        
        # 输出详细风险评估结果
        print("\n=== 风险评估详细结果 ===")
        for i, pool in enumerate(recommended_pools[:5], 1):
            print(f"{i}. {pool['pair_name']} ({pool['chain']})")
            print(f"   TVL: ${pool['tvl_usd']:,.0f}")
            print(f"   年化费率: {pool['fee_tvl']:.2f}%")
            print(f"   风险评分: {pool['risk_score']:.3f}")
            print(f"   风险等级: {pool['risk_level']}")
            print()
        
        # 生成投资建议
        generate_investment_advice(recommended_pools, total_recommended_tvl, avg_risk_score)
        
    except Exception as e:
        print(f"Agent 风险评估失败: {str(e)}")

def calculate_risk_score(pool):
    """计算池子的风险评分 (0-1，越低越安全)"""
    risk_factors = []
    
    # TVL 风险 (TVL越高风险越低)
    if pool['tvl_usd'] > 1000000:
        risk_factors.append(0.1)
    elif pool['tvl_usd'] > 500000:
        risk_factors.append(0.2)
    elif pool['tvl_usd'] > 100000:
        risk_factors.append(0.3)
    else:
        risk_factors.append(0.5)
    
    # 交易量风险 (交易量越高风险越低)
    if pool['volume_24h'] > 500000:
        risk_factors.append(0.1)
    elif pool['volume_24h'] > 100000:
        risk_factors.append(0.2)
    else:
        risk_factors.append(0.4)
    
    # 收益率风险 (过高的收益率通常意味着高风险)
    if pool['fee_tvl'] > 200:  # 超过200%年化
        risk_factors.append(0.8)
    elif pool['fee_tvl'] > 100:  # 超过100%年化
        risk_factors.append(0.6)
    elif pool['fee_tvl'] > 50:   # 超过50%年化
        risk_factors.append(0.4)
    else:
        risk_factors.append(0.2)
    
    # 链风险
    if pool['chain'] == 'BSC':
        risk_factors.append(0.2)  # BSC 相对成熟
    else:  # SOL
        risk_factors.append(0.3)  # Solana 相对较新
    
    return sum(risk_factors) / len(risk_factors)

def get_risk_level(risk_score):
    """根据风险评分获取风险等级"""
    if risk_score < 0.3:
        return "低风险"
    elif risk_score < 0.5:
        return "中等风险"
    elif risk_score < 0.7:
        return "高风险"
    else:
        return "极高风险"

def generate_investment_advice(recommended_pools, total_tvl, avg_risk):
    """生成投资建议"""
    print("=== AI Agent 投资建议 ===")
    
    if not recommended_pools:
        print("当前市场条件下，未找到符合风险偏好的优质池子。")
        print("建议等待更好的市场机会。")
        return
    
    print(f"基于对 {len(recommended_pools)} 个推荐池子的分析：")
    print()
    
    # 风险等级建议
    if avg_risk < 0.3:
        print("✅ 整体风险等级：低")
        print("   建议投资比例：60-80% 的可用资金")
    elif avg_risk < 0.5:
        print("⚠️  整体风险等级：中等")
        print("   建议投资比例：30-50% 的可用资金")
    else:
        print("🚨 整体风险等级：偏高")
        print("   建议投资比例：10-20% 的可用资金")
    
    print()
    
    # 多样化建议
    bsc_pools = [p for p in recommended_pools if p['chain'] == 'BSC']
    sol_pools = [p for p in recommended_pools if p['chain'] == 'SOL']
    
    if bsc_pools and sol_pools:
        print("💡 多链分散建议：")
        print(f"   - BSC 池子: {len(bsc_pools)} 个")
        print(f"   - Solana 池子: {len(sol_pools)} 个")
        print("   建议在两条链之间分散投资以降低单链风险")
    
    print()
    
    # 收益预期
    avg_yield = sum(p['fee_tvl'] for p in recommended_pools) / len(recommended_pools)
    print(f"📈 预期年化收益：{avg_yield:.1f}%")
    print(f"🔒 平均风险评分：{avg_risk:.3f}/1.0")
    
    print()
    print("⚠️  风险提示：")
    print("   - DeFi 投资存在智能合约风险、无常损失等风险")
    print("   - 建议定期监控池子表现并及时调整")
    print("   - 不要投入超过您能承受损失的资金")

if __name__ == "__main__":
    print("开始测试真实池子数据查询和 Agent 风险评估...")
    print("=" * 60)
    
    # 1. 测试真实数据查询
    bsc_result, sol_result = test_real_pools_query()
    
    # 2. 测试 Agent 风险评估
    test_agent_risk_assessment([bsc_result, sol_result])
    
    print("\n" + "=" * 60)
    print("测试完成！")

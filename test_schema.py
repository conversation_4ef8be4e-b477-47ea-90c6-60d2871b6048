#!/usr/bin/env python3
"""
查询PancakeSwap V3 GraphQL Schema
"""

import asyncio
import aiohttp
import json

async def test_schema():
    """测试schema和可用字段"""
    
    endpoint = "https://gateway.thegraph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
    api_key = "9731921233db132a98c2325878e6c153"
    
    # 内省查询来获取schema
    introspection_query = """
    {
      __schema {
        types {
          name
          fields {
            name
            type {
              name
              kind
            }
          }
        }
      }
    }
    """
    
    # 简单查询来测试可用字段
    simple_query = """
    {
      factories(first: 1) {
        id
        poolCount
        txCount
        totalVolumeUSD
      }
    }
    """
    
    # 测试Pool类型的字段
    pool_query = """
    {
      pools(first: 1) {
        id
        token0 {
          id
          symbol
        }
        token1 {
          id
          symbol
        }
        feeTier
        liquidity
        volumeUSD
      }
    }
    """
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}',
        'User-Agent': 'DyFlow-Schema-Test/1.0'
    }
    
    print("🔍 测试PancakeSwap V3 GraphQL Schema...")
    
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            
            # 测试简单查询
            print("\n1. 测试Factory查询...")
            payload = {"query": simple_query}
            async with session.post(endpoint, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if 'errors' in result:
                        print(f"❌ Factory查询错误: {result['errors']}")
                    else:
                        print("✅ Factory查询成功")
                        if 'data' in result and 'factories' in result['data']:
                            factory = result['data']['factories'][0]
                            print(f"   工厂ID: {factory['id']}")
                            print(f"   池子数量: {factory['poolCount']}")
                
            # 测试Pool查询
            print("\n2. 测试Pool查询...")
            payload = {"query": pool_query}
            async with session.post(endpoint, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if 'errors' in result:
                        print(f"❌ Pool查询错误: {result['errors']}")
                    else:
                        print("✅ Pool查询成功")
                        if 'data' in result and 'pools' in result['data']:
                            pool = result['data']['pools'][0]
                            print(f"   池子ID: {pool['id']}")
                            print(f"   Token0: {pool['token0']['symbol']}")
                            print(f"   Token1: {pool['token1']['symbol']}")
                            print(f"   手续费率: {pool['feeTier']}")
                            print(f"   流动性: {pool['liquidity']}")
                            print(f"   交易量: {pool['volumeUSD']}")
            
            # 尝试不同的TVL字段名
            print("\n3. 测试不同的TVL字段名...")
            tvl_test_queries = [
                ("totalValueLockedUSD", "{ pools(first: 1) { id totalValueLockedUSD } }"),
                ("totalValueLocked", "{ pools(first: 1) { id totalValueLocked } }"),
                ("tvl", "{ pools(first: 1) { id tvl } }"),
                ("totalValueLockedToken0", "{ pools(first: 1) { id totalValueLockedToken0 } }"),
                ("totalValueLockedToken1", "{ pools(first: 1) { id totalValueLockedToken1 } }")
            ]
            
            for field_name, query in tvl_test_queries:
                payload = {"query": query}
                async with session.post(endpoint, json=payload, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        if 'errors' not in result:
                            print(f"   ✅ {field_name} 字段存在")
                            if 'data' in result and 'pools' in result['data'] and len(result['data']['pools']) > 0:
                                value = result['data']['pools'][0].get(field_name, 'N/A')
                                print(f"      值: {value}")
                        else:
                            print(f"   ❌ {field_name} 字段不存在")
            
            # 测试完整的池子查询
            print("\n4. 测试完整池子查询...")
            full_query = """
            {
              pools(first: 2, orderBy: volumeUSD, orderDirection: desc) {
                id
                token0 {
                  id
                  symbol
                  name
                  decimals
                }
                token1 {
                  id
                  symbol
                  name
                  decimals
                }
                feeTier
                liquidity
                volumeUSD
                totalValueLockedToken0
                totalValueLockedToken1
                token0Price
                token1Price
                sqrtPrice
                tick
              }
            }
            """
            
            payload = {"query": full_query}
            async with session.post(endpoint, json=payload, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    if 'errors' in result:
                        print(f"❌ 完整查询错误: {result['errors']}")
                    else:
                        print("✅ 完整查询成功")
                        if 'data' in result and 'pools' in result['data']:
                            pools = result['data']['pools']
                            print(f"   获取到 {len(pools)} 个池子")
                            
                            for i, pool in enumerate(pools, 1):
                                print(f"\n   池子 {i}:")
                                print(f"     ID: {pool['id'][:20]}...")
                                print(f"     交易对: {pool['token0']['symbol']}/{pool['token1']['symbol']}")
                                print(f"     手续费率: {int(pool['feeTier'])/10000:.2f}%")
                                print(f"     流动性: {pool['liquidity']}")
                                print(f"     交易量: ${float(pool['volumeUSD']):,.0f}")
                                print(f"     Token0锁定量: {pool['totalValueLockedToken0']}")
                                print(f"     Token1锁定量: {pool['totalValueLockedToken1']}")
                                
                                # 计算TVL (需要价格信息)
                                token0_locked = float(pool['totalValueLockedToken0'])
                                token1_locked = float(pool['totalValueLockedToken1'])
                                token0_price = float(pool['token0Price']) if pool['token0Price'] else 0
                                token1_price = float(pool['token1Price']) if pool['token1Price'] else 0
                                
                                # 简单的TVL估算 (这不是准确的计算方式)
                                estimated_tvl = (token0_locked * token0_price) + (token1_locked * token1_price)
                                print(f"     估算TVL: ${estimated_tvl:,.0f}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    asyncio.run(test_schema())

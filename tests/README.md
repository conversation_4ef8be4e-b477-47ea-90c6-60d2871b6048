# Dy-Flow v3 系統整合測試

## 概述

本測試套件專為 Dy-Flow v3 系統設計，使用真實的 API 數據來驗證整個系統的完整性和穩定性。

## 測試架構

### 測試組件

1. **Scout 測試** - 驗證真實數據收集
   - BSC PancakeSwap v3 池子數據
   - Solana Meteora DLMM 池子數據

2. **Scorer 測試** - 驗證評分算法
   - 四因子評分系統
   - 可對沖性判斷

3. **Planner 測試** - 驗證策略規劃
   - Active/Hedge/Passive 三大策略
   - 池子分配邏輯

4. **Risk Sentinel 測試** - 驗證風險監控
   - ATR + kDrop 計算
   - 實時警報生成

5. **完整管道測試** - 端到端數據流驗證

## 前置要求

### 1. 環境配置

```bash
# 安裝依賴
pip install -r requirements.txt

# 設置環境變量
export SUPABASE_URL="your_supabase_url"
export SUPABASE_KEY="your_supabase_key"
export BSCSCAN_API_KEY="your_bscscan_api_key"
```

### 2. 配置文件

確保以下配置文件存在並正確配置：
- `config/default.yaml` - 主配置文件
- `tests/test_config.yaml` - 測試配置文件

### 3. 數據庫設置

確保 Supabase 數據庫已設置並包含必要的表結構：
```sql
-- 運行 docs/supabase_schema.sql 創建表結構
```

## 運行測試

### 快速開始

```bash
# 運行完整的整合測試
python tests/run_integration_tests.py
```

### 運行特定組件測試

```python
# 導入測試框架
from tests.real_data_integration_test import RealDataIntegrationTest

# 創建測試實例
test = RealDataIntegrationTest()
await test.setup()

# 運行特定測試
bsc_result = await test.test_scout_bsc_real_data()
scorer_result = await test.test_scorer_real_evaluation()

# 清理
await test.cleanup()
```

## 測試結果

### 成功標準

測試被認為成功當：
- **總體成功率 ≥ 80%** - 系統整合度良好
- **總體成功率 ≥ 60%** - 部分成功，需要關注
- **總體成功率 < 60%** - 測試失敗，需要修復

### 輸出示例

```
🚀 開始 Dy-Flow v3 真實數據整合測試
============================================================

1️⃣ 測試 Scout BSC 真實數據收集...
   - API 連接: ✅
   - 數據收集: ✅
   - 數據品質: ✅
   - 數據庫存儲: ✅
   - 發現池子數量: 15

2️⃣ 測試 Scout Meteora 真實數據收集...
   - API 連接: ✅
   - 數據收集: ✅
   - 數據品質: ✅
   - 數據庫存儲: ✅
   - 發現池子數量: 12

3️⃣ 測試 Scorer 真實數據評分...
   - 數據獲取: ✅
   - 評分執行: ✅
   - 評分有效性: ✅
   - 數據庫存儲: ✅
   - 評分池子數量: 27
   - 平均評分: 0.654

4️⃣ 測試 Planner 真實策略規劃...
   - 數據獲取: ✅
   - 策略生成: ✅
   - 計劃有效性: ✅
   - 數據庫存儲: ✅
   - 創建計劃數量: 3
   - 策略分布: {'active': 1, 'hedge': 1, 'passive': 1}

5️⃣ 測試 Risk Sentinel 真實風險監控...
   - 價格數據獲取: ✅
   - 風險計算: ✅
   - 警報生成: ✅
   - 數據庫存儲: ✅
   - 風險事件數量: 2
   - 警報類型: {'price_drop': 1, 'tvl_drop': 1}

============================================================
📊 測試結果總結
============================================================
🎯 總體成功率: 95.2%
✅ 通過測試: 20/21
⏱️ 測試持續時間: 3.5 分鐘

🎉 測試成功！系統整合度良好 (95.2%)
```

### 測試報告

測試完成後會生成詳細的 JSON 報告：
```
tests/reports/integration_test_report_20250530_124500.json
```

報告包含：
- 每個組件的詳細測試結果
- 錯誤信息和異常情況
- 性能指標和執行時間
- 數據品質評估

## 故障排除

### 常見問題

1. **API 連接失敗**
   ```
   - 檢查網絡連接
   - 驗證 API 密鑰設置
   - 確認 API 端點可訪問
   ```

2. **數據庫連接失敗**
   ```
   - 檢查 SUPABASE_URL 和 SUPABASE_KEY
   - 確認數據庫表結構已創建
   - 驗證網絡連接到 Supabase
   ```

3. **數據品質問題**
   ```
   - 檢查 API 響應格式是否變更
   - 驗證數據映射邏輯
   - 確認必要字段存在
   ```

4. **評分算法問題**
   ```
   - 檢查原始數據是否足夠
   - 驗證評分參數配置
   - 確認算法邏輯正確
   ```

### 調試模式

啟用詳細日誌：
```bash
export LOG_LEVEL=DEBUG
python tests/run_integration_tests.py
```

## 測試擴展

### 添加新測試

1. 在 `real_data_integration_test.py` 中添加新的測試方法
2. 遵循命名規範：`test_component_feature`
3. 返回標準化的結果字典
4. 添加適當的錯誤處理

### 自定義配置

可以複製 `tests/test_config.yaml` 並修改參數：
```python
test = RealDataIntegrationTest("custom_config.yaml")
```

## 持續集成

### GitHub Actions 示例

```yaml
name: Integration Tests
on: [push, pull_request]

jobs:
  integration-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run integration tests
        env:
          SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
          SUPABASE_KEY: ${{ secrets.SUPABASE_KEY }}
          BSCSCAN_API_KEY: ${{ secrets.BSCSCAN_API_KEY }}
        run: python tests/run_integration_tests.py
```

## 性能基準

### 預期執行時間

| 組件 | 預期時間 | 備註 |
|------|----------|------|
| Scout BSC | 30-60秒 | 取決於 API 響應速度 |
| Scout Meteora | 20-40秒 | 取決於網絡延遲 |
| Scorer | 10-30秒 | 取決於池子數量 |
| Planner | 5-15秒 | 取決於評分數據量 |
| Risk Sentinel | 15-45秒 | 取決於價格數據獲取 |
| **總計** | **2-4分鐘** | 正常網絡條件下 |

### 資源使用

- **內存**: 通常 < 500MB
- **網絡**: 主要是 API 請求
- **數據庫**: 讀寫操作，無大量數據

## 最佳實踐

1. **定期運行** - 建議每日運行以確保系統穩定性
2. **監控結果** - 關注成功率趨勢和性能變化
3. **及時修復** - 成功率下降時立即調查
4. **更新測試** - 隨著系統演進更新測試用例
5. **保存報告** - 歸檔測試報告用於分析

## 聯繫支持

如果遇到測試問題：
1. 檢查本文檔的故障排除部分
2. 查看測試報告中的詳細錯誤信息
3. 提交 Issue 並附上測試報告
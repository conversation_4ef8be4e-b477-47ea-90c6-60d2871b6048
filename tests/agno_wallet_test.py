"""
基於 Agno Framework 的錢包分析器測試
"""

import asyncio
import os
import sys
from pathlib import Path

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agents.wallet_analyzer_agno import DyFlowWalletAnalyzer


async def test_agno_wallet_analyzer():
    """測試基於 Agno Framework 的錢包分析器"""
    
    print("🚀 Dy-Flow Agno Framework 錢包分析器測試")
    print("🔧 使用真正的 Agno Framework 構建專業 DeFi 分析 Agent")
    print("=" * 80)
    
    # 配置
    openai_api_key = os.getenv("OPENAI_API_KEY")
    bscscan_api_key = "**********************************"
    
    if not openai_api_key:
        print("❌ 請設置 OPENAI_API_KEY 環境變量")
        print("export OPENAI_API_KEY=your_api_key_here")
        return
    
    # 測試錢包
    wallet_address = "******************************************"
    
    try:
        print("============================================================")
        print("📊 初始化 Agno 錢包分析器")
        print("============================================================")
        
        # 創建 Agno 錢包分析器
        analyzer = DyFlowWalletAnalyzer(
            openai_api_key=openai_api_key,
            bscscan_api_key=bscscan_api_key
        )
        print("✅ Agno 錢包分析器初始化成功")
        
        print(f"🎯 分析目標錢包: {wallet_address}")
        
        print("\n============================================================")
        print("📊 執行 Agno Agent 錢包分析")
        print("============================================================")
        
        # 使用 Agno Agent 進行分析（流式輸出）
        analyzer.print_analysis(wallet_address)
        
        print("\n============================================================")
        print("📊 測試總結")
        print("============================================================")
        print("🎉 Agno Framework 錢包分析器測試完成！")
        
        print("\n✅ 成功驗證的功能:")
        print("   • Agno Framework Agent 架構")
        print("   • 自定義 Toolkit 集成")
        print("   • 多重價格源 API 整合")
        print("   • BSCScan API 數據獲取")
        print("   • 智能錢包分析和詐騙幣檢測")
        print("   • 流式 AI 響應輸出")
        print("   • 結構化工具調用")
        
        print("\n🌟 Agno Framework 優勢:")
        print("   • 輕量級高性能 Agent 框架")
        print("   • 原生工具集成支持")
        print("   • 智能推理和工具調用")
        print("   • 流式響應輸出")
        print("   • 企業級可擴展性")
        
    except Exception as e:
        print(f"❌ 測試失敗: {str(e)}")
        import traceback
        traceback.print_exc()


def test_simple_agno_functionality():
    """測試簡化的 Agno 功能"""
    print("\n🧪 測試簡化 Agno 功能")
    print("-" * 50)
    
    try:
        from agno.agent import Agent
        from agno.models.openai import OpenAIChat
        
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            print("❌ 需要 OPENAI_API_KEY")
            return
        
        # 創建簡單的 Agno Agent
        agent = Agent(
            name="Simple Test Agent",
            description="測試 Agno 基本功能",
            model=OpenAIChat(id="gpt-4o", api_key=openai_api_key),
            instructions=["你是一個專業的 DeFi 分析助手"],
            markdown=True
        )
        
        print("✅ Agno Agent 創建成功")
        
        # 測試簡單響應
        response = agent.run("解釋什麼是 DeFi 錢包分析？")
        print(f"📝 Agent 響應: {response.content[:200]}...")
        
        print("✅ Agno 基本功能測試成功")
        
    except Exception as e:
        print(f"❌ Agno 功能測試失敗: {str(e)}")


if __name__ == "__main__":
    # 測試簡化功能
    test_simple_agno_functionality()
    
    # 測試完整錢包分析器
    asyncio.run(test_agno_wallet_analyzer())
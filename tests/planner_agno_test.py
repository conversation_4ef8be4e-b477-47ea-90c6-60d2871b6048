"""
Planner Agno Agent 測試
驗證 Agno Framework 集成和 AI 增強策略規劃功能
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import structlog

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agents.planner_agno import PlannerAgnoAgent, AGNO_AVAILABLE
from src.utils.models_v3 import PoolScore, Plan, AgentResult
from src.utils.config import Config

logger = structlog.get_logger(__name__)

async def test_planner_agno_basic():
    """基礎功能測試"""
    print(f"\n🧪 測試：Planner Agno Agent 基礎功能")
    print(f"Agno Framework 可用性: {AGNO_AVAILABLE}")
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = PlannerAgnoAgent(
            name="test_planner_agno",
            config=config,
            database=None  # 測試模式下可以為 None
        )
        
        print(f"✅ Agent 創建成功: {agent}")
        print(f"   - Agno 可用: {agent.agno_available}")
        print(f"   - 推理功能: {agent.use_reasoning}")
        print(f"   - 主要模型: {agent.primary_model}")
        print(f"   - 次要模型: {agent.secondary_model}")
        print(f"   - 知識庫: {agent.enable_knowledge_base}")
        print(f"   - 市場數據: {agent.enable_market_data}")
        
        # 3. 初始化測試
        await agent.initialize()
        print(f"✅ Agent 初始化成功")
        
        if agent.agno_available:
            print(f"   - 市場分析器: {agent.agno_market_analyst is not None}")
            print(f"   - 策略顧問: {agent.agno_strategy_advisor is not None}")
            print(f"   - 組合優化器: {agent.agno_portfolio_optimizer is not None}")
            print(f"   - 記憶存儲: {agent.memory_storage is not None}")
            print(f"   - DeFi 知識庫: {agent.knowledge_base is not None}")
        
        # 4. 獲取規劃洞察
        insights = await agent.get_planning_insights()
        print(f"✅ 規劃洞察獲取成功:")
        for key, value in insights.items():
            print(f"   - {key}: {value}")
        
        # 5. 清理測試
        await agent.cleanup()
        print(f"✅ Agent 清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_planner_agno_execution():
    """執行功能測試"""
    print(f"\n🧪 測試：Planner Agno Agent 執行功能")
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = PlannerAgnoAgent(
            name="test_planner_agno_exec",
            config=config,
            database=None
        )
        
        # 3. 初始化
        await agent.initialize()
        
        # 4. 創建模擬評分池子數據
        mock_scored_pools = [
            PoolScore(
                id="pool_1",
                score=95.0,
                hedgeable=True
            ),
            PoolScore(
                id="pool_2",
                score=88.0,
                hedgeable=False
            ),
            PoolScore(
                id="pool_3",
                score=82.0,
                hedgeable=True
            )
        ]
        
        # 模擬數據源方法
        agent.planner._get_scored_pools = lambda: mock_scored_pools
        
        # 5. 執行策略規劃
        print(f"開始執行策略規劃...")
        result = await agent.execute()
        
        print(f"✅ 執行完成:")
        print(f"   - 狀態: {result.status}")
        print(f"   - 執行計劃數: {len(result.data) if result.data else 0}")
        print(f"   - Agno 增強: {result.metadata.get('agno_enhanced', False)}")
        print(f"   - 降級模式: {result.metadata.get('fallback_mode', False)}")
        
        if result.metadata:
            print(f"   - 元數據:")
            for key, value in result.metadata.items():
                if key not in ['market_assessment', 'strategy_analyses', 'optimal_portfolio']:
                    print(f"     * {key}: {value}")
            
            # 顯示 AI 洞察
            ai_insights = result.metadata.get('ai_insights', {})
            if ai_insights:
                print(f"   - AI 洞察:")
                for key, value in ai_insights.items():
                    print(f"     * {key}: {value}")
        
        # 6. 清理
        await agent.cleanup()
        
        return result.status == "success"
        
    except Exception as e:
        print(f"❌ 執行測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_planner_agno_ai_analysis():
    """AI 分析功能測試"""
    print(f"\n🧪 測試：Planner Agno Agent AI 分析功能")
    
    if not AGNO_AVAILABLE:
        print("⚠️  Agno Framework 不可用，跳過 AI 分析測試")
        return True
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = PlannerAgnoAgent(
            name="test_planner_agno_analysis",
            config=config,
            database=None
        )
        
        # 3. 初始化
        await agent.initialize()
        
        if not agent.agno_available:
            print("⚠️  Agno 增強功能不可用，跳過分析測試")
            return True
        
        # 4. 測試市場環境分析
        print("🔍 測試市場環境分析...")
        try:
            market_assessment = await agent._analyze_market_environment_with_ai()
            if market_assessment:
                print(f"✅ 市場環境分析成功:")
                print(f"   - 市場情緒: {market_assessment.overall_sentiment}")
                print(f"   - 波動率狀態: {market_assessment.volatility_regime}")
                print(f"   - 流動性條件: {market_assessment.liquidity_conditions}")
                print(f"   - 短期趨勢: {market_assessment.short_term_trend}")
                print(f"   - 信心度: {market_assessment.confidence_score:.2f}")
                print(f"   - 關鍵催化劑數: {len(market_assessment.key_catalysts)}")
            else:
                print("⚠️  市場環境分析未返回結果")
        except Exception as e:
            print(f"⚠️  市場環境分析測試失敗: {e}")
        
        # 5. 測試策略分析
        print("🔍 測試策略分析...")
        try:
            mock_pools = [
                PoolScore(id="test_pool", score=90.0, hedgeable=True)
            ]
            
            for strategy_type in ["delta_neutral", "ladder_ss", "passive_high_tvl"]:
                analysis = await agent._analyze_strategy_with_ai(strategy_type, None, mock_pools)
                if analysis:
                    print(f"✅ {strategy_type} 策略分析成功:")
                    print(f"   - 適合度評分: {analysis.suitability_score:.1f}")
                    print(f"   - 預期收益率: {analysis.expected_return:.2f}")
                    print(f"   - 風險級別: {analysis.risk_level}")
                    print(f"   - 建議分配: {analysis.recommended_allocation:.2f}")
                    print(f"   - 執行複雜度: {analysis.execution_complexity}")
                else:
                    print(f"⚠️  {strategy_type} 策略分析未返回結果")
        except Exception as e:
            print(f"⚠️  策略分析測試失敗: {e}")
        
        # 6. 測試組合優化
        print("🔍 測試組合優化...")
        try:
            mock_strategy_analyses = {
                "delta_neutral": type('Analysis', (), {
                    'suitability_score': 85.0,
                    'expected_return': 0.25,
                    'recommended_allocation': 0.4,
                    'model_dump': lambda: {"strategy": "delta_neutral", "score": 85.0}
                })(),
                "passive_high_tvl": type('Analysis', (), {
                    'suitability_score': 75.0,
                    'expected_return': 0.12,
                    'recommended_allocation': 0.6,
                    'model_dump': lambda: {"strategy": "passive_high_tvl", "score": 75.0}
                })()
            }
            
            mock_pools = [
                PoolScore(id="test_pool_1", chain="BSC", score=90.0, hedgeable=True, created_at=datetime.now()),
                PoolScore(id="test_pool_2", chain="SOL", score=85.0, hedgeable=False, created_at=datetime.now())
            ]
            
            portfolio = await agent._optimize_portfolio_with_ai(mock_strategy_analyses, None, mock_pools)
            if portfolio:
                print(f"✅ 組合優化成功:")
                print(f"   - 總分配金額: ${portfolio.total_allocation:,.0f}")
                print(f"   - 預期組合收益率: {portfolio.expected_portfolio_apy:.2f}")
                print(f"   - 組合風險評分: {portfolio.portfolio_risk_score:.1f}")
                print(f"   - Delta中性分配: {portfolio.delta_neutral_allocation:.2f}")
                print(f"   - 被動策略分配: {portfolio.passive_high_tvl_allocation:.2f}")
                print(f"   - 多樣化評分: {portfolio.diversification_score:.2f}")
                print(f"   - 再平衡頻率: {portfolio.rebalancing_frequency}")
                print(f"   - 池子分配數: {len(portfolio.pool_allocations)}")
            else:
                print("⚠️  組合優化未返回結果")
        except Exception as e:
            print(f"⚠️  組合優化測試失敗: {e}")
        
        # 7. 清理
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ AI 分析測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_planner_agno_strategy_learning():
    """策略學習功能測試"""
    print(f"\n🧪 測試：Planner Agno Agent 策略學習功能")
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = PlannerAgnoAgent(
            name="test_planner_agno_learning",
            config=config,
            database=None
        )
        
        # 3. 初始化
        await agent.initialize()
        
        # 4. 模擬策略學習更新
        mock_strategy_analyses = {
            "delta_neutral": type('Analysis', (), {
                'suitability_score': 85.0,
                'expected_return': 0.25,
                'model_dump': lambda: {"strategy": "delta_neutral", "score": 85.0}
            })()
        }
        
        mock_portfolio = type('Portfolio', (), {
            'total_allocation': 50000.0,
            'expected_portfolio_apy': 0.20,
            'model_dump': lambda: {"allocation": 50000.0, "apy": 0.20}
        })()
        
        print("🔍 測試策略學習更新...")
        agent._update_strategy_learning(mock_strategy_analyses, mock_portfolio)
        
        print(f"✅ 策略學習更新成功:")
        print(f"   - 學習記錄數: {len(agent.strategy_performance_history.get('strategy_decisions', []))}")
        
        # 5. 測試策略識別
        best_strategy = agent._identify_best_strategy(mock_strategy_analyses)
        print(f"✅ 最佳策略識別: {best_strategy}")
        
        # 6. 測試優先級計算
        mock_allocation = type('Allocation', (), {
            'confidence_level': 0.8,
            'allocation_amount': 25000.0
        })()
        
        priority = agent._calculate_priority(mock_allocation)
        print(f"✅ 優先級計算: {priority}")
        
        # 7. 測試風險級別確定
        for strategy_type in ["delta_neutral", "ladder_ss", "passive_high_tvl"]:
            mock_alloc = type('Allocation', (), {'strategy_type': strategy_type})()
            risk_level = agent._determine_risk_level(mock_alloc)
            print(f"✅ {strategy_type} 風險級別: {risk_level}")
        
        # 8. 清理
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 策略學習測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_planner_agno_fallback():
    """降級模式測試"""
    print(f"\n🧪 測試：Planner Agno Agent 降級模式")
    
    try:
        # 強制禁用 Agno
        import src.agents.planner_agno as planner_module
        original_agno = planner_module.AGNO_AVAILABLE
        planner_module.AGNO_AVAILABLE = False
        
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = PlannerAgnoAgent(
            name="test_planner_agno_fallback",
            config=config,
            database=None
        )
        
        print(f"✅ 降級模式 Agent 創建成功: {agent}")
        print(f"   - Agno 可用: {agent.agno_available}")
        
        # 3. 初始化
        await agent.initialize()
        print(f"✅ 降級模式初始化成功")
        
        # 4. 獲取規劃洞察
        insights = await agent.get_planning_insights()
        print(f"✅ 降級模式規劃洞察:")
        print(f"   - Agno 可用: {insights.get('agno_framework_available', False)}")
        print(f"   - 市場分析器: {insights.get('market_analyst_available', False)}")
        print(f"   - 策略顧問: {insights.get('strategy_advisor_available', False)}")
        print(f"   - 組合優化器: {insights.get('portfolio_optimizer_available', False)}")
        print(f"   - 知識庫: {insights.get('knowledge_base_enabled', False)}")
        
        # 5. 清理
        await agent.cleanup()
        
        # 恢復原始設置
        planner_module.AGNO_AVAILABLE = original_agno
        
        return True
        
    except Exception as e:
        print(f"❌ 降級測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_all_tests():
    """運行所有測試"""
    print("🚀 開始 Planner Agno Agent 測試套件")
    print("=" * 60)
    
    test_results = []
    
    # 基礎功能測試
    result1 = await test_planner_agno_basic()
    test_results.append(("基礎功能", result1))
    
    # 執行功能測試
    result2 = await test_planner_agno_execution()
    test_results.append(("執行功能", result2))
    
    # AI 分析功能測試
    result3 = await test_planner_agno_ai_analysis()
    test_results.append(("AI 分析功能", result3))
    
    # 策略學習功能測試
    result4 = await test_planner_agno_strategy_learning()
    test_results.append(("策略學習功能", result4))
    
    # 降級模式測試
    result5 = await test_planner_agno_fallback()
    test_results.append(("降級模式", result5))
    
    # 測試結果總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！Planner Agno Agent 準備就緒")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查錯誤信息")
        return False

async def main():
    """主函數"""
    try:
        success = await run_all_tests()
        
        if success:
            print("\n✨ Planner Agno Agent 測試完成")
            print("\n📋 下一步建議:")
            print("1. 配置 OpenAI API Key 以啟用 AI 策略分析")
            print("2. 設置 Anthropic API Key 以獲得策略顧問功能")
            print("3. 集成到 Agno DAG 調度系統")
            print("4. 配置 DeFi 知識庫以增強決策品質")
            print("5. 設置組合監控和再平衡機制")
            
            if AGNO_AVAILABLE:
                print("\n🎯 Agno Framework 已就緒:")
                print("   - 可以使用完整的 AI 策略規劃功能")
                print("   - 支持市場環境評估和策略分析")
                print("   - 具備組合優化和風險管理")
                print("   - 智能策略學習和適應")
                print("   - DeFi 專業知識庫集成")
            else:
                print("\n⚠️  Agno Framework 未安裝:")
                print("   - 當前使用降級模式")
                print("   - 建議安裝以獲得完整功能:")
                print("     pip install agno openai anthropic duckduckgo-search")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️  測試被用戶中斷")
        return False
    except Exception as e:
        print(f"\n💥 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 設置結構化日誌
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.dev.ConsoleRenderer()
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # 運行測試
    asyncio.run(main())

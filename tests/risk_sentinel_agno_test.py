"""
Risk Sentinel Agno Agent 測試
驗證 Agno Framework 集成和 AI 增強風險監控功能
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import structlog

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agents.risk_sentinel_agno import RiskSentinelAgnoAgent, AGNO_AVAILABLE
from src.utils.models_v3 import PoolRaw, RiskAlert, AgentResult
from src.utils.config import Config

logger = structlog.get_logger(__name__)

async def test_risk_sentinel_agno_basic():
    """基礎功能測試"""
    print(f"\n🧪 測試：Risk Sentinel Agno Agent 基礎功能")
    print(f"Agno Framework 可用性: {AGNO_AVAILABLE}")
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = RiskSentinelAgnoAgent(
            name="test_risk_sentinel_agno",
            config=config,
            database=None  # 測試模式下可以為 None
        )
        
        print(f"✅ Agent 創建成功: {agent}")
        print(f"   - Agno 可用: {agent.agno_available}")
        print(f"   - 推理功能: {agent.use_reasoning}")
        print(f"   - 主要模型: {agent.primary_model}")
        print(f"   - 次要模型: {agent.secondary_model}")
        print(f"   - 市場數據: {agent.enable_market_data}")
        
        # 3. 初始化測試
        await agent.initialize()
        print(f"✅ Agent 初始化成功")
        
        if agent.agno_available:
            print(f"   - 市場風險分析器: {agent.agno_market_risk_analyzer is not None}")
            print(f"   - 池子風險分析器: {agent.agno_pool_risk_analyzer is not None}")
            print(f"   - 趨勢分析器: {agent.agno_trend_analyzer is not None}")
            print(f"   - 記憶存儲: {agent.memory_storage is not None}")
        
        # 4. 獲取風險摘要
        summary = await agent.get_risk_summary()
        print(f"✅ 風險摘要獲取成功:")
        for key, value in summary.items():
            print(f"   - {key}: {value}")
        
        # 5. 清理測試
        await agent.cleanup()
        print(f"✅ Agent 清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_risk_sentinel_agno_execution():
    """執行功能測試"""
    print(f"\n🧪 測試：Risk Sentinel Agno Agent 執行功能")
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = RiskSentinelAgnoAgent(
            name="test_risk_sentinel_agno_exec",
            config=config,
            database=None
        )
        
        # 3. 初始化
        await agent.initialize()
        
        # 4. 創建模擬數據 (如果沒有真實數據源)
        mock_positions = [
            {
                "pool_id": "test_pool_1",
                "chain": "BSC",
                "amount": 10000.0,
                "risk_level": "medium"
            }
        ]
        
        mock_pools = [
            PoolRaw(
                id="test_pool_1",
                chain="BSC",
                tvl_usd=1000000.0,
                volume_24h=50000.0,
                created_at=datetime.now()
            ),
            PoolRaw(
                id="test_pool_2", 
                chain="SOL",
                tvl_usd=500000.0,
                volume_24h=25000.0,
                created_at=datetime.now()
            )
        ]
        
        # 模擬數據源方法
        agent.risk_sentinel._get_active_positions = lambda: mock_positions
        agent.risk_sentinel._get_monitored_pools = lambda: mock_pools
        
        # 5. 執行風險監控
        print(f"開始執行風險監控...")
        result = await agent.execute()
        
        print(f"✅ 執行完成:")
        print(f"   - 狀態: {result.status}")
        print(f"   - 風險告警數: {len(result.data) if result.data else 0}")
        print(f"   - Agno 增強: {result.metadata.get('agno_enhanced', False)}")
        print(f"   - 降級模式: {result.metadata.get('fallback_mode', False)}")
        
        if result.metadata:
            print(f"   - 元數據:")
            for key, value in result.metadata.items():
                if key not in ['market_risk_assessment', 'trend_analysis']:
                    print(f"     * {key}: {value}")
            
            # 顯示 AI 洞察
            ai_insights = result.metadata.get('ai_insights', {})
            if ai_insights:
                print(f"   - AI 洞察:")
                for key, value in ai_insights.items():
                    print(f"     * {key}: {value}")
        
        # 6. 清理
        await agent.cleanup()
        
        return result.status == "success"
        
    except Exception as e:
        print(f"❌ 執行測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_risk_sentinel_agno_analysis():
    """AI 分析功能測試"""
    print(f"\n🧪 測試：Risk Sentinel Agno Agent AI 分析功能")
    
    if not AGNO_AVAILABLE:
        print("⚠️  Agno Framework 不可用，跳過 AI 分析測試")
        return True
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = RiskSentinelAgnoAgent(
            name="test_risk_sentinel_agno_analysis",
            config=config,
            database=None
        )
        
        # 3. 初始化
        await agent.initialize()
        
        if not agent.agno_available:
            print("⚠️  Agno 增強功能不可用，跳過分析測試")
            return True
        
        # 4. 測試市場風險分析
        print("🔍 測試市場風險分析...")
        try:
            market_risk = await agent._analyze_market_risk_with_ai()
            if market_risk:
                print(f"✅ 市場風險分析成功:")
                print(f"   - 風險級別: {market_risk.overall_risk_level}")
                print(f"   - 市場波動率: {market_risk.market_volatility:.2f}")
                print(f"   - 信心度: {market_risk.confidence_score:.2f}")
                print(f"   - 風險因子數: {len(market_risk.risk_factors)}")
                print(f"   - 預警信號數: {len(market_risk.warning_signals)}")
            else:
                print("⚠️  市場風險分析未返回結果")
        except Exception as e:
            print(f"⚠️  市場風險分析測試失敗: {e}")
        
        # 5. 測試趨勢分析
        print("🔍 測試趨勢分析...")
        try:
            trend_analysis = await agent._analyze_market_trends_with_ai()
            if trend_analysis:
                print(f"✅ 趨勢分析成功:")
                print(f"   - 趨勢方向: {trend_analysis.trend_direction}")
                print(f"   - 趨勢強度: {trend_analysis.trend_strength:.2f}")
                print(f"   - 信心度: {trend_analysis.confidence_score:.2f}")
                print(f"   - 時間周期: {trend_analysis.time_horizon}")
            else:
                print("⚠️  趨勢分析未返回結果")
        except Exception as e:
            print(f"⚠️  趨勢分析測試失敗: {e}")
        
        # 6. 測試池子風險分析
        print("🔍 測試池子風險分析...")
        try:
            mock_pool = PoolRaw(
                id="test_analysis_pool",
                chain="BSC",
                tvl_usd=1000000.0,
                volume_24h=50000.0,
                created_at=datetime.now()
            )
            
            pool_risk = await agent._analyze_pool_risk_with_ai(mock_pool, None, None)
            if pool_risk:
                print(f"✅ 池子風險分析成功:")
                print(f"   - 風險級別: {pool_risk.risk_level}")
                print(f"   - 風險評分: {pool_risk.risk_score:.1f}")
                print(f"   - 無常損失風險: {pool_risk.impermanent_loss_risk:.2f}")
                print(f"   - 流動性風險: {pool_risk.liquidity_drain_risk:.2f}")
                print(f"   - 需要立即行動: {pool_risk.requires_immediate_action}")
                print(f"   - 緩解策略數: {len(pool_risk.mitigation_strategies)}")
            else:
                print("⚠️  池子風險分析未返回結果")
        except Exception as e:
            print(f"⚠️  池子風險分析測試失敗: {e}")
        
        # 7. 清理
        await agent.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ AI 分析測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_risk_sentinel_agno_fallback():
    """降級模式測試"""
    print(f"\n🧪 測試：Risk Sentinel Agno Agent 降級模式")
    
    try:
        # 強制禁用 Agno
        import src.agents.risk_sentinel_agno as risk_module
        original_agno = risk_module.AGNO_AVAILABLE
        risk_module.AGNO_AVAILABLE = False
        
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = RiskSentinelAgnoAgent(
            name="test_risk_sentinel_agno_fallback",
            config=config,
            database=None
        )
        
        print(f"✅ 降級模式 Agent 創建成功: {agent}")
        print(f"   - Agno 可用: {agent.agno_available}")
        
        # 3. 初始化
        await agent.initialize()
        print(f"✅ 降級模式初始化成功")
        
        # 4. 獲取風險摘要
        summary = await agent.get_risk_summary()
        print(f"✅ 降級模式風險摘要:")
        print(f"   - Agno 可用: {summary.get('agno_framework_available', False)}")
        print(f"   - 市場分析器: {summary.get('market_risk_analyzer_available', False)}")
        print(f"   - 池子分析器: {summary.get('pool_risk_analyzer_available', False)}")
        print(f"   - 趨勢分析器: {summary.get('trend_analyzer_available', False)}")
        
        # 5. 清理
        await agent.cleanup()
        
        # 恢復原始設置
        risk_module.AGNO_AVAILABLE = original_agno
        
        return True
        
    except Exception as e:
        print(f"❌ 降級測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_all_tests():
    """運行所有測試"""
    print("🚀 開始 Risk Sentinel Agno Agent 測試套件")
    print("=" * 60)
    
    test_results = []
    
    # 基礎功能測試
    result1 = await test_risk_sentinel_agno_basic()
    test_results.append(("基礎功能", result1))
    
    # 執行功能測試
    result2 = await test_risk_sentinel_agno_execution()
    test_results.append(("執行功能", result2))
    
    # AI 分析功能測試
    result3 = await test_risk_sentinel_agno_analysis()
    test_results.append(("AI 分析功能", result3))
    
    # 降級模式測試
    result4 = await test_risk_sentinel_agno_fallback()
    test_results.append(("降級模式", result4))
    
    # 測試結果總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！Risk Sentinel Agno Agent 準備就緒")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查錯誤信息")
        return False

async def main():
    """主函數"""
    try:
        success = await run_all_tests()
        
        if success:
            print("\n✨ Risk Sentinel Agno Agent 測試完成")
            print("\n📋 下一步建議:")
            print("1. 配置 OpenAI API Key 以啟用 AI 風險分析")
            print("2. 設置 DuckDuckGo 搜索以獲取市場數據")
            print("3. 集成到 Agno DAG 調度系統")
            print("4. 配置風險告警通知渠道")
            
            if AGNO_AVAILABLE:
                print("\n🎯 Agno Framework 已就緒:")
                print("   - 可以使用完整的 AI 風險分析功能")
                print("   - 支持市場風險評估和趨勢分析")
                print("   - 具備池子級別的深度風險分析")
                print("   - 智能告警生成和優先級排序")
            else:
                print("\n⚠️  Agno Framework 未安裝:")
                print("   - 當前使用降級模式")
                print("   - 建議安裝以獲得完整功能:")
                print("     pip install agno openai anthropic duckduckgo-search")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️  測試被用戶中斷")
        return False
    except Exception as e:
        print(f"\n💥 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 設置結構化日誌
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.dev.ConsoleRenderer()
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # 運行測試
    asyncio.run(main())

"""
Scorer V2 Agno Agent 測試
驗證 Agno Framework 集成和 AI 增強功能
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
import structlog

# 添加項目根目錄到 Python 路徑
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.agents.scorer_v2_agno import ScorerV2AgnoAgent, AGNO_AVAILABLE
from src.utils.models_v3 import PoolRaw, AgentResult
from src.utils.config import Config

logger = structlog.get_logger(__name__)

async def test_scorer_v2_agno_basic():
    """基礎功能測試"""
    print(f"\n🧪 測試：Scorer V2 Agno Agent 基礎功能")
    print(f"Agno Framework 可用性: {AGNO_AVAILABLE}")
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = ScorerV2AgnoAgent(
            name="test_scorer_v2_agno",
            config=config,
            database=None  # 測試模式下可以為 None
        )
        
        print(f"✅ Agent 創建成功: {agent}")
        print(f"   - Agno 可用: {agent.agno_available}")
        print(f"   - 推理功能: {agent.use_reasoning}")
        print(f"   - 主要模型: {agent.primary_model}")
        print(f"   - 次要模型: {agent.secondary_model}")
        
        # 3. 初始化測試
        await agent.initialize()
        print(f"✅ Agent 初始化成功")
        
        if agent.agno_available:
            print(f"   - 市場分析器: {agent.agno_market_analyzer is not None}")
            print(f"   - 權重優化器: {agent.agno_weight_optimizer is not None}")
            print(f"   - 池子分析器: {agent.agno_pool_analyzer is not None}")
        
        # 4. 獲取 AI 洞察
        insights = await agent.get_ai_insights()
        print(f"✅ AI 洞察獲取成功:")
        for key, value in insights.items():
            print(f"   - {key}: {value}")
        
        # 5. 清理測試
        await agent.cleanup()
        print(f"✅ Agent 清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_scorer_v2_agno_execution():
    """執行功能測試"""
    print(f"\n🧪 測試：Scorer V2 Agno Agent 執行功能")
    
    try:
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = ScorerV2AgnoAgent(
            name="test_scorer_v2_agno_exec",
            config=config,
            database=None
        )
        
        # 3. 初始化
        await agent.initialize()
        
        # 4. 創建模擬池子數據 (如果沒有真實數據源)
        mock_pools = [
            PoolRaw(
                id="test_pool_1",
                chain="BSC", 
                tvl_usd=1000000.0,
                fee24h=2500.0,
                fee_tvl=0.25,
                created_at=datetime.now()
            ),
            PoolRaw(
                id="test_pool_2",
                chain="SOL",
                tvl_usd=500000.0,
                fee24h=1200.0,
                fee_tvl=0.24,
                created_at=datetime.now()
            )
        ]
        
        # 模擬 _get_pool_raws 方法
        agent.scorer_v2._get_pool_raws = lambda: mock_pools
        
        # 5. 執行評分
        print(f"開始執行評分...")
        result = await agent.execute()
        
        print(f"✅ 執行完成:")
        print(f"   - 狀態: {result.status}")
        print(f"   - 評分池子數: {len(result.data) if result.data else 0}")
        print(f"   - Agno 增強: {result.metadata.get('agno_enhanced', False)}")
        print(f"   - 降級模式: {result.metadata.get('fallback_mode', False)}")
        
        if result.metadata:
            print(f"   - 元數據:")
            for key, value in result.metadata.items():
                if key not in ['market_assessment', 'weight_optimization', 'top_pools_analysis']:
                    print(f"     * {key}: {value}")
        
        # 6. 清理
        await agent.cleanup()
        
        return result.status == "success"
        
    except Exception as e:
        print(f"❌ 執行測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_scorer_v2_agno_fallback():
    """降級模式測試"""
    print(f"\n🧪 測試：Scorer V2 Agno Agent 降級模式")
    
    try:
        # 強制禁用 Agno
        import src.agents.scorer_v2_agno as scorer_module
        original_agno = scorer_module.AGNO_AVAILABLE
        scorer_module.AGNO_AVAILABLE = False
        
        # 1. 載入配置
        config = Config()
        
        # 2. 創建 Agent 實例
        agent = ScorerV2AgnoAgent(
            name="test_scorer_v2_agno_fallback",
            config=config,
            database=None
        )
        
        print(f"✅ 降級模式 Agent 創建成功: {agent}")
        print(f"   - Agno 可用: {agent.agno_available}")
        
        # 3. 初始化
        await agent.initialize()
        print(f"✅ 降級模式初始化成功")
        
        # 4. 獲取洞察
        insights = await agent.get_ai_insights()
        print(f"✅ 降級模式洞察:")
        print(f"   - Agno 可用: {insights.get('agno_framework_available', False)}")
        
        # 5. 清理
        await agent.cleanup()
        
        # 恢復原始設置
        scorer_module.AGNO_AVAILABLE = original_agno
        
        return True
        
    except Exception as e:
        print(f"❌ 降級測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

async def run_all_tests():
    """運行所有測試"""
    print("🚀 開始 Scorer V2 Agno Agent 測試套件")
    print("=" * 60)
    
    test_results = []
    
    # 基礎功能測試
    result1 = await test_scorer_v2_agno_basic()
    test_results.append(("基礎功能", result1))
    
    # 執行功能測試  
    result2 = await test_scorer_v2_agno_execution()
    test_results.append(("執行功能", result2))
    
    # 降級模式測試
    result3 = await test_scorer_v2_agno_fallback()
    test_results.append(("降級模式", result3))
    
    # 測試結果總結
    print("\n" + "=" * 60)
    print("📊 測試結果總結:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通過" if result else "❌ 失敗"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 總體結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！Scorer V2 Agno Agent 準備就緒")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查錯誤信息")
        return False

async def main():
    """主函數"""
    try:
        success = await run_all_tests()
        
        if success:
            print("\n✨ Scorer V2 Agno Agent 測試完成")
            print("\n📋 下一步建議:")
            print("1. 配置 OpenAI API Key 以啟用完整 AI 功能")
            print("2. 安裝 Agno Framework: pip install agno")
            print("3. 集成到 Agno DAG 調度系統")
            print("4. 配置動態權重調整參數")
            
            if AGNO_AVAILABLE:
                print("\n🎯 Agno Framework 已就緒:")
                print("   - 可以使用完整的 AI 推理功能")
                print("   - 支持結構化輸出和記憶存儲")
                print("   - 市場分析和權重優化功能可用")
            else:
                print("\n⚠️  Agno Framework 未安裝:")
                print("   - 當前使用降級模式")
                print("   - 建議安裝以獲得完整功能:")
                print("     pip install agno openai anthropic")
        
        return success
        
    except KeyboardInterrupt:
        print("\n⏹️  測試被用戶中斷")
        return False
    except Exception as e:
        print(f"\n💥 測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # 設置結構化日誌
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.dev.ConsoleRenderer()
        ],
        wrapper_class=structlog.stdlib.BoundLogger,
        logger_factory=structlog.stdlib.LoggerFactory(),
        context_class=dict,
        cache_logger_on_first_use=True,
    )
    
    # 運行測試
    asyncio.run(main())

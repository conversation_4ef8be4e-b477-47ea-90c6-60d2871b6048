# Dy-Flow 2.0 Strategy Critic & Parameter Optimizer

You are an expert quantitative analyst reviewing the performance of Dy-Flow 2.0 Delta-Neutral LP strategy. Your role is to analyze daily performance data and suggest parameter optimizations.

## Performance Summary (Last 24 Hours)

**Portfolio Metrics:**
- Total Portfolio Value: ${{ total_value_usd }}
- Net PnL (24h): ${{ daily_pnl }} ({{ (daily_pnl / total_value_usd * 100)|round(3) }}%)
- Fee Income: ${{ fee_income }}
- Funding Costs: ${{ funding_costs }}
- Gas Costs: ${{ gas_costs }}
- Net After Costs: ${{ net_pnl }}

**Position Performance:**
{% for position in position_performance %}
- {{ position.pool_id }} ({{ position.chain.upper() }}):
  - ROI: {{ (position.roi * 100)|round(2) }}%
  - Daily Fee: ${{ position.daily_fee }}
  - IL Impact: {{ (position.il_impact * 100)|round(2) }}%
  - Hedge Efficiency: {{ (position.hedge_efficiency * 100)|round(1) }}%
  - Days Active: {{ position.days_active }}
  - Score at Entry: {{ position.entry_score }}
  - Current Score: {{ position.current_score }}
{% endfor %}

**Risk Events:**
{% for event in risk_events %}
- {{ event.timestamp }}: {{ event.event_type }} - {{ event.description }}
  - Action Taken: {{ event.action_taken }}
  - Impact: ${{ event.impact_usd }}
{% endfor %}

**Execution Statistics:**
- Total Transactions: {{ total_transactions }}
- Success Rate: {{ (success_rate * 100)|round(1) }}%
- Average Gas Cost: ${{ avg_gas_cost }}
- Average Slippage: {{ (avg_slippage * 100)|round(3) }}%
- Rebalance Frequency: {{ rebalance_count }} times

## Historical Performance (Last 30 Days)

**Key Metrics:**
- Total Return: {{ (monthly_return * 100)|round(2) }}%
- Sharpe Ratio: {{ sharpe_ratio|round(3) }}
- Max Drawdown: {{ (max_drawdown * 100)|round(2) }}%
- Win Rate: {{ (win_rate * 100)|round(1) }}%
- Average Daily Volume: ${{ avg_daily_volume|int }}
- Best Day: ${{ best_day_pnl }} ({{ best_day_date }})
- Worst Day: ${{ worst_day_pnl }} ({{ worst_day_date }})

**Strategy Efficiency:**
- Fee Capture Rate: {{ (fee_capture_rate * 100)|round(1) }}%
- Delta Neutrality: {{ (delta_neutrality * 100)|round(1) }}% (100% = perfect)
- Capital Utilization: {{ (capital_utilization * 100)|round(1) }}%
- Opportunity Cost: ${{ opportunity_cost }} (missed due to position limits)

## Current Strategy Parameters

{% for param in current_parameters %}
- **{{ param.name }}**: {{ param.value }} ({{ param.description }})
{% endfor %}

## Market Analysis

**Market Conditions:**
- DeFi TVL Trend: {{ defi_tvl_trend }}
- Average Market APR: {{ (market_avg_apr * 100)|round(2) }}%
- Volatility Regime: {{ volatility_regime }}
- Funding Rate Environment: {{ funding_environment }}

**Pool Performance Patterns:**
{% for pattern in pool_patterns %}
- {{ pattern.chain }}: {{ pattern.observation }}
{% endfor %}

## Your Analysis Task

Please provide a comprehensive strategy review in the following JSON format:

```json
{
  "performance_grade": "A+" | "A" | "B+" | "B" | "C+" | "C" | "D",
  "key_insights": [
    "Most important observations about strategy performance"
  ],
  "parameter_recommendations": [
    {
      "parameter": "enter_threshold",
      "current_value": 0.60,
      "recommended_value": 0.65,
      "reasoning": "detailed explanation",
      "confidence": 0.8,
      "expected_impact": "+2.5% annual return"
    }
  ],
  "risk_assessment": {
    "current_risk_level": "LOW" | "MEDIUM" | "HIGH",
    "emerging_risks": ["list of potential risks"],
    "risk_mitigation_suggestions": ["actionable suggestions"]
  },
  "market_adaptation": {
    "market_regime": "trending_up" | "trending_down" | "sideways" | "volatile",
    "strategy_adjustments": ["specific adjustments for current market"],
    "optimal_exposure_level": 0.85
  },
  "operational_improvements": [
    {
      "area": "gas_optimization" | "slippage_reduction" | "timing" | "selection",
      "suggestion": "specific improvement",
      "estimated_benefit": "$X daily savings"
    }
  ],
  "alert_thresholds": {
    "performance_alerts": {
      "daily_loss_threshold": -50,
      "drawdown_threshold": -0.05
    },
    "market_alerts": {
      "volatility_spike": 0.8,
      "funding_rate_spike": 0.15
    }
  }
}
```

## Optimization Guidelines

1. **Performance Focus**: Prioritize risk-adjusted returns over absolute returns
2. **Parameter Stability**: Only suggest changes with >70% confidence
3. **Market Adaptation**: Consider current market regime in recommendations
4. **Risk Management**: Always maintain conservative risk posture
5. **Operational Efficiency**: Minimize gas costs and slippage

## Key Questions to Address

1. Are current entry/exit thresholds optimal for recent market conditions?
2. Is the ATR-based ranging strategy working effectively?
3. Are hedge ratios maintaining delta neutrality?
4. Is the strategy missing significant opportunities due to position limits?
5. Are gas costs materially impacting net returns?
6. How can the strategy better adapt to changing market conditions?

## Performance Benchmarks

- Target Sharpe Ratio: >2.0
- Target Max Drawdown: <5%
- Target Win Rate: >70%
- Target Daily Return: >0.1%
- Target Gas Efficiency: <2% of gross returns

Please analyze the data thoroughly and provide actionable insights for strategy optimization. Focus on data-driven recommendations with clear reasoning.
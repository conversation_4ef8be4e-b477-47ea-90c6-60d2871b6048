#!/usr/bin/env python3
"""
Portfolio Manager Web Dashboard 启动脚本
简单启动 Dy-Flow 2.1 Web 界面
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(__file__))

if __name__ == "__main__":
    try:
        from src.ui.web_server import main
        import asyncio
        
        print("🚀 启动 Portfolio Manager Web Dashboard")
        print("📱 TailwindCSS 响应式界面")
        print("⚡ 实时数据更新")
        print("")
        
        asyncio.run(main())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保安装了所需的依赖包:")
        print("pip install flask")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
"""
DyFlow CLI 工具常量定義
"""

# BSC 網絡配置
BSC_RPC_URLS = [
    "https://bsc-dataseed.binance.org/",
    "https://bsc-dataseed1.defibit.io/",
    "https://bsc-dataseed1.ninicoin.io/"
]

# PancakeSwap 合約地址
PANCAKE_ADDRESSES = {
    "factory_v2": "0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73",
    "factory_v3": "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865",
    "v3_nft_manager": "0x46A15B0b27311cedF172AB29E4f4766fbE7F4364",
    "router_v2": "0x10ED43C718714eb63d5aA57B78B54704E256024E"
}

# 代幣地址
TOKEN_ADDRESSES = {
    "WBNB": "0xbb4CdB9CBd36B01bD1cBaEBF2De08d9173bc095c",
    "USDT": "0x55d398326f99059fF775485246999027B3197955",
    "USDC": "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d",
    "BUSD": "0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56",
    "B": "0x6bdcce4a559076e37755a78ce0c06214e59e4444"
}

# API 端點
API_ENDPOINTS = {
    "pancake_v2_graph": "https://api.thegraph.com/subgraphs/name/pancakeswap/pairs",
    "pancake_v3_graph": "https://api.thegraph.com/subgraphs/name/pancakeswap/exchange-v3-bsc",
    "pancake_backup": "https://bsc.streamingfast.io/subgraphs/name/pancakeswap/exchange-v2",
    "coingecko_bsc": "https://api.coingecko.com/api/v3/simple/token_price/binance-smart-chain"
}

# 通用 ABI
ERC20_ABI = [
    {"constant": True, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "type": "function"},
    {"constant": True, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "type": "function"},
    {"constant": True, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint8"}], "type": "function"},
    {"constant": True, "inputs": [{"name": "_owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "balance", "type": "uint256"}], "type": "function"}
]

V3_NFT_MANAGER_ABI = [
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}],
        "name": "tokenOfOwnerByIndex",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}],
        "name": "positions",
        "outputs": [
            {"internalType": "uint96", "name": "nonce", "type": "uint96"},
            {"internalType": "address", "name": "operator", "type": "address"},
            {"internalType": "address", "name": "token0", "type": "address"},
            {"internalType": "address", "name": "token1", "type": "address"},
            {"internalType": "uint24", "name": "fee", "type": "uint24"},
            {"internalType": "int24", "name": "tickLower", "type": "int24"},
            {"internalType": "int24", "name": "tickUpper", "type": "int24"},
            {"internalType": "uint128", "name": "liquidity", "type": "uint128"},
            {"internalType": "uint256", "name": "feeGrowthInside0LastX128", "type": "uint256"},
            {"internalType": "uint256", "name": "feeGrowthInside1LastX128", "type": "uint256"},
            {"internalType": "uint128", "name": "tokensOwed0", "type": "uint128"},
            {"internalType": "uint128", "name": "tokensOwed1", "type": "uint128"}
        ],
        "stateMutability": "view",
        "type": "function"
    }
]

FACTORY_V2_ABI = [
    {
        "constant": True,
        "inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}],
        "name": "getPair",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "payable": False,
        "stateMutability": "view",
        "type": "function"
    }
]

# 默認配置
DEFAULT_CONFIG = {
    "rpc_timeout": 30,
    "api_timeout": 30,
    "retry_count": 3,
    "retry_delay": 1.0,
    "min_tvl": 1000,
    "decimal_precision": 6
}

"""
LP 掃描工具 - 整合 V2/V3 LP 查詢功能
整合自 tools/lp_position_checker.py 等
"""

import asyncio
import logging
from decimal import Decimal, getcontext
from web3 import Web3
from typing import Optional, Dict, List, Tuple

from .constants import BSC_RPC_URLS, PANCAKE_ADDRESSES, TOKEN_ADDRESSES, ERC20_ABI, FACTORY_V2_ABI, DEFAULT_CONFIG
from .pancakeswap_v3 import PancakeV3LPFetcher

# 設置高精度
getcontext().prec = 50

logger = logging.getLogger(__name__)

class LPScanner:
    """LP 位置掃描器 - 支持 V2 和 V3"""
    
    def __init__(self, rpc_url: str = None):
        self.rpc_url = rpc_url or BSC_RPC_URLS[0]
        self.w3 = Web3(Web3.HTTPProvider(self.rpc_url))
        
        # PancakeSwap 地址
        self.factory_v2_address = self.w3.to_checksum_address(PANCAKE_ADDRESSES["factory_v2"])
        
        # V3 查詢器
        self.v3_fetcher = PancakeV3LPFetcher(rpc_url=self.rpc_url)
        
        # Pair V2 ABI (ERC20 + token0/token1 functions)
        self.pair_v2_abi = ERC20_ABI + [
            {"constant": True, "inputs": [], "name": "token0", "outputs": [{"name": "", "type": "address"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "token1", "outputs": [{"name": "", "type": "address"}], "type": "function"}
        ]
        
        # 驗證連接
        if not self.w3.is_connected():
            logger.warning(f"無法連接到 RPC: {self.rpc_url}")
            # 嘗試備用 RPC
            for backup_rpc in BSC_RPC_URLS[1:]:
                try:
                    self.w3 = Web3(Web3.HTTPProvider(backup_rpc))
                    if self.w3.is_connected():
                        self.rpc_url = backup_rpc
                        self.v3_fetcher = PancakeV3LPFetcher(rpc_url=self.rpc_url)
                        logger.info(f"切換到備用 RPC: {backup_rpc}")
                        break
                except Exception as e:
                    logger.debug(f"備用 RPC {backup_rpc} 也失敗: {e}")
                    continue
            else:
                raise Exception("所有 RPC 端點都無法連接")

    def get_token_info(self, token_address: str) -> Dict:
        """獲取代幣資訊"""
        try:
            contract = self.w3.eth.contract(address=token_address, abi=ERC20_ABI)
            return {
                "address": token_address,
                "symbol": contract.functions.symbol().call(),
                "name": contract.functions.name().call(),
                "decimals": contract.functions.decimals().call()
            }
        except Exception as e:
            logger.error(f"Error getting token info for {token_address}: {e}")
            return {
                "address": token_address,
                "symbol": "UNKNOWN",
                "name": "Unknown Token",
                "decimals": 18
            }

    async def check_v2_lp_for_pair(self, wallet_address: str, token0_address: str, token1_address: str) -> Optional[Dict]:
        """檢查指定代幣對的 V2 LP"""
        logger.info(f"檢查 V2 LP: {token0_address} / {token1_address}")
        
        factory_contract = self.w3.eth.contract(address=self.factory_v2_address, abi=FACTORY_V2_ABI)
        
        # V2 LP地址不區分順序，嘗試兩種組合
        pair_address_1 = factory_contract.functions.getPair(token0_address, token1_address).call()
        pair_address_2 = factory_contract.functions.getPair(token1_address, token0_address).call()

        for lp_address in {pair_address_1, pair_address_2}:
            if lp_address == "******************************************":
                continue

            logger.info(f"發現潛在 V2 LP 對地址: {lp_address}")
            lp_contract = self.w3.eth.contract(address=lp_address, abi=self.pair_v2_abi)
            
            try:
                balance_raw = lp_contract.functions.balanceOf(self.w3.to_checksum_address(wallet_address)).call()
                if balance_raw > 0:
                    decimals = lp_contract.functions.decimals().call()
                    balance = Decimal(balance_raw) / (Decimal(10) ** decimals)
                    token0 = lp_contract.functions.token0().call()
                    token1 = lp_contract.functions.token1().call()
                    
                    # 確認是指定的代幣對
                    if ((token0.lower() == token0_address.lower() and token1.lower() == token1_address.lower()) or 
                        (token0.lower() == token1_address.lower() and token1.lower() == token0_address.lower())):
                        
                        logger.info(f"發現 V2 LP: {lp_address}, 餘額: {balance}")
                        return {
                            "type": "PancakeSwap V2 LP",
                            "chain": "bsc", 
                            "version": "v2",
                            "lp_address": lp_address,
                            "balance": str(balance),
                            "token0": self.get_token_info(token0),
                            "token1": self.get_token_info(token1)
                        }
            except Exception as e:
                logger.debug(f"檢查 V2 LP {lp_address} 時出錯: {e}")
        return None

    async def find_lp_for_token_pair(self, wallet_address: str, token0_address: str, token1_address: str) -> Dict:
        """查找指定代幣對的所有 LP 位置 (V2 + V3)"""
        logger.info(f"查找錢包 {wallet_address} 的 {token0_address}/{token1_address} LP 位置")
        
        results = {
            "wallet_address": wallet_address,
            "token_pair": f"{token0_address}/{token1_address}",
            "v2_position": None,
            "v3_positions": [],
            "total_positions": 0
        }
        
        try:
            # 檢查 V2
            v2_position = await self.check_v2_lp_for_pair(wallet_address, token0_address, token1_address)
            if v2_position:
                results["v2_position"] = v2_position
                results["total_positions"] += 1
            
            # 檢查 V3
            v3_positions = await self.v3_fetcher.find_v3_positions_for_wallet(
                wallet_address, 
                token_filter=(token0_address, token1_address)
            )
            if v3_positions:
                results["v3_positions"] = v3_positions
                results["total_positions"] += len(v3_positions)
                
        except Exception as e:
            logger.error(f"查找 LP 位置時出錯: {e}")
            results["error"] = str(e)
        
        return results

    async def scan_wallet_all_lp(self, wallet_address: str) -> Dict:
        """掃描錢包的所有 LP 位置"""
        logger.info(f"掃描錢包 {wallet_address} 的所有 LP 位置")
        
        results = {
            "wallet_address": wallet_address,
            "v3_positions": [],
            "total_v3_positions": 0,
            "scan_timestamp": asyncio.get_event_loop().time()
        }
        
        try:
            # 掃描所有 V3 位置
            v3_positions = await self.v3_fetcher.find_v3_positions_for_wallet(wallet_address)
            results["v3_positions"] = v3_positions
            results["total_v3_positions"] = len(v3_positions)
            
            # 注意: V2 位置掃描需要知道具體的代幣對，因為需要查詢 Factory
            # 這裡我們不進行盲掃描，因為效率太低
            # 如果需要 V2 掃描，建議使用 GraphQL API 或指定特定代幣對
            
        except Exception as e:
            logger.error(f"掃描錢包 LP 時出錯: {e}")
            results["error"] = str(e)
        
        return results

    def format_lp_search_report(self, results: Dict) -> str:
        """格式化 LP 搜索報告"""
        if "error" in results:
            return f"❌ 搜索失敗: {results['error']}"
        
        report = []
        report.append("=" * 80)
        report.append("🔍 LP 位置搜索報告")
        report.append("=" * 80)
        
        report.append(f"\n📊 錢包地址: {results['wallet_address']}")
        
        if "token_pair" in results:
            report.append(f"搜索代幣對: {results['token_pair']}")
        
        report.append(f"總發現位置: {results.get('total_positions', results.get('total_v3_positions', 0))}")
        
        # V2 位置
        if results.get("v2_position"):
            v2 = results["v2_position"]
            report.append(f"\n💎 發現 V2 LP 位置:")
            report.append(f"  類型: {v2['type']}")
            report.append(f"  LP 地址: {v2['lp_address']}")
            report.append(f"  餘額: {v2['balance']}")
            report.append(f"  代幣對: {v2['token0']['symbol']} / {v2['token1']['symbol']}")
        
        # V3 位置
        v3_positions = results.get("v3_positions", [])
        if v3_positions:
            report.append(f"\n🔥 發現 {len(v3_positions)} 個 V3 LP 位置:")
            for i, pos in enumerate(v3_positions, 1):
                if pos.get("success"):
                    pool = pos["pool_info"]
                    position = pos["position_info"]
                    fees = pos["unclaimed_fees"]
                    
                    report.append(f"\n  {i}. NFT #{pos['nft_token_id']}")
                    report.append(f"     交易對: {pool['token0']['symbol']} / {pool['token1']['symbol']}")
                    report.append(f"     手續費率: {pool['fee_percentage']:.2%}")
                    report.append(f"     流動性: {position['liquidity']}")
                    report.append(f"     價格範圍: {position['lower_price']:.6f} - {position['upper_price']:.6f}")
                    if fees['fee_value_usd'] > 0.01:
                        report.append(f"     未領取手續費: ${fees['fee_value_usd']:.2f}")
        
        if results.get('total_positions', results.get('total_v3_positions', 0)) == 0:
            report.append(f"\n❌ 未發現任何 LP 位置")
            if "token_pair" in results:
                report.append(f"   指定代幣對 {results['token_pair']} 沒有發現活躍的 LP 位置")
            else:
                report.append(f"   該錢包沒有發現任何 PancakeSwap LP 位置")
        
        report.append("\n" + "=" * 80)
        return "\n".join(report)

    # 便捷方法: 查找特定代幣的 LP
    async def find_b_usdt_lp(self, wallet_address: str) -> Dict:
        """查找 B/USDT LP 位置 (便捷方法)"""
        b_token_address = TOKEN_ADDRESSES["B"]
        usdt_address = TOKEN_ADDRESSES["USDT"]
        return await self.find_lp_for_token_pair(wallet_address, b_token_address, usdt_address)

"""
PancakeSwap V3 LP 位置查詢工具
整合自 tools/simple_lp_info.py
"""

import asyncio
import logging
from decimal import Decimal, getcontext
from web3 import Web3
import aiohttp
from typing import Optional, Dict, List, Tuple

from .constants import BSC_RPC_URLS, PANCAKE_ADDRESSES, TOKEN_ADDRESSES, API_ENDPOINTS, ERC20_ABI, V3_NFT_MANAGER_ABI, DEFAULT_CONFIG

# 設置高精度
getcontext().prec = 50

logger = logging.getLogger(__name__)

class PancakeV3LPFetcher:
    """PancakeSwap V3 LP 位置查詢器"""
    
    def __init__(self, rpc_url: str = None, chain_platform_id: str = "binance-smart-chain"):
        self.rpc_url = rpc_url or BSC_RPC_URLS[0]
        self.chain_platform_id = chain_platform_id
        self.w3 = Web3(Web3.HTTPProvider(self.rpc_url))
        self.v3_nft_manager_address = self.w3.to_checksum_address(PANCAKE_ADDRESSES["v3_nft_manager"])
        
        # 驗證連接
        if not self.w3.is_connected():
            logger.warning(f"無法連接到 RPC: {self.rpc_url}")
            # 嘗試備用 RPC
            for backup_rpc in BSC_RPC_URLS[1:]:
                try:
                    self.w3 = Web3(Web3.HTTPProvider(backup_rpc))
                    if self.w3.is_connected():
                        self.rpc_url = backup_rpc
                        logger.info(f"切換到備用 RPC: {backup_rpc}")
                        break
                except Exception as e:
                    logger.debug(f"備用 RPC {backup_rpc} 也失敗: {e}")
                    continue
            else:
                raise Exception("所有 RPC 端點都無法連接")

    async def get_token_prices(self, token_addresses: List[str]) -> Dict[str, Decimal]:
        """獲取代幣價格 (使用 CoinGecko API)"""
        try:
            url = API_ENDPOINTS["coingecko_bsc"]
            params = {
                "contract_addresses": ",".join(token_addresses),
                "vs_currencies": "usd"
            }
            
            timeout = aiohttp.ClientTimeout(total=DEFAULT_CONFIG["api_timeout"])
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        prices = {}
                        for addr in token_addresses:
                            addr_lower = addr.lower()
                            if addr_lower in data and 'usd' in data[addr_lower]:
                                prices[addr] = Decimal(str(data[addr_lower]['usd']))
                            else:
                                logger.warning(f"Price not found for token: {addr}")
                                prices[addr] = Decimal('0')
                        
                        return prices
                    else:
                        logger.error(f"CoinGecko API error: {response.status}")
                        
        except Exception as e:
            logger.error(f"Error fetching prices: {e}")
        
        # 返回零價格作為fallback
        return {addr: Decimal('0') for addr in token_addresses}

    def get_token_info(self, token_address: str) -> Dict:
        """獲取代幣資訊"""
        try:
            contract = self.w3.eth.contract(address=token_address, abi=ERC20_ABI)
            return {
                "address": token_address,
                "symbol": contract.functions.symbol().call(),
                "name": contract.functions.name().call(),
                "decimals": contract.functions.decimals().call()
            }
        except Exception as e:
            logger.error(f"Error getting token info for {token_address}: {e}")
            return {
                "address": token_address,
                "symbol": "UNKNOWN",
                "name": "Unknown Token",
                "decimals": 18
            }

    def tick_to_price(self, tick: int) -> Decimal:
        """將 tick 轉換為價格"""
        return Decimal(1.0001) ** tick

    async def fetch_v3_lp_details(self, nft_token_id: int) -> Dict:
        """獲取 V3 LP 位置詳細信息"""
        logger.info(f"獲取 NFT Token ID {nft_token_id} 的詳細信息")
        
        try:
            # 獲取 NFT 持倉資訊
            nft_manager_contract = self.w3.eth.contract(
                address=self.v3_nft_manager_address, 
                abi=V3_NFT_MANAGER_ABI
            )
            position_details = nft_manager_contract.functions.positions(nft_token_id).call()
            
            token0_address = position_details[2]
            token1_address = position_details[3]
            fee_tier = position_details[4]
            tick_lower = position_details[5]
            tick_upper = position_details[6]
            liquidity = position_details[7]
            tokens_owed0 = position_details[10]
            tokens_owed1 = position_details[11]
            
            # 獲取代幣資訊
            token0_info = self.get_token_info(token0_address)
            token1_info = self.get_token_info(token1_address)
            
            # 計算價格範圍
            lower_price = self.tick_to_price(tick_lower)
            upper_price = self.tick_to_price(tick_upper)
            
            # 獲取代幣價格
            prices = await self.get_token_prices([token0_address, token1_address])
            token0_price_usd = prices.get(token0_address, Decimal('0'))
            token1_price_usd = prices.get(token1_address, Decimal('0'))
            
            # 計算未收取的手續費
            fee0_amount = Decimal(tokens_owed0) / (Decimal(10) ** token0_info["decimals"])
            fee1_amount = Decimal(tokens_owed1) / (Decimal(10) ** token1_info["decimals"])
            fee_value_usd = (fee0_amount * token0_price_usd) + (fee1_amount * token1_price_usd)
            
            return {
                "nft_token_id": nft_token_id,
                "success": True,
                "chain": "bsc",
                "version": "v3",
                "pool_info": {
                    "token0": token0_info,
                    "token1": token1_info,
                    "fee_tier": fee_tier,
                    "fee_percentage": fee_tier / 10000
                },
                "position_info": {
                    "liquidity": str(liquidity),
                    "tick_lower": tick_lower,
                    "tick_upper": tick_upper,
                    "lower_price": float(lower_price),
                    "upper_price": float(upper_price)
                },
                "prices": {
                    "token0_price_usd": float(token0_price_usd),
                    "token1_price_usd": float(token1_price_usd)
                },
                "unclaimed_fees": {
                    "fee0_amount": float(fee0_amount),
                    "fee1_amount": float(fee1_amount),
                    "fee_value_usd": float(fee_value_usd)
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing V3 LP position: {e}")
            return {
                "nft_token_id": nft_token_id,
                "success": False,
                "error": str(e)
            }

    async def find_v3_positions_for_wallet(self, wallet_address: str, token_filter: Tuple[str, str] = None) -> List[Dict]:
        """查找錢包的所有 V3 LP 位置，可選擇過濾特定代幣對"""
        logger.info(f"查找錢包 {wallet_address} 的 V3 LP 位置")
        
        try:
            nft_manager_contract = self.w3.eth.contract(
                address=self.v3_nft_manager_address, 
                abi=V3_NFT_MANAGER_ABI
            )
            checksum_wallet_address = self.w3.to_checksum_address(wallet_address)
            
            v3_positions = []
            balance_nft = nft_manager_contract.functions.balanceOf(checksum_wallet_address).call()
            logger.info(f"錢包 {wallet_address} 有 {balance_nft} 個 V3 NFT 位置")
            
            if balance_nft == 0:
                return []
            
            for i in range(balance_nft):
                token_id = nft_manager_contract.functions.tokenOfOwnerByIndex(checksum_wallet_address, i).call()
                position_details = nft_manager_contract.functions.positions(token_id).call()
                
                pos_token0 = self.w3.to_checksum_address(position_details[2])
                pos_token1 = self.w3.to_checksum_address(position_details[3])
                liquidity = position_details[7]
                
                # 只處理有流動性的位置
                if liquidity > 0:
                    # 如果指定了代幣過濾器，檢查是否匹配
                    if token_filter:
                        filter_token0, filter_token1 = token_filter
                        filter_token0 = self.w3.to_checksum_address(filter_token0)
                        filter_token1 = self.w3.to_checksum_address(filter_token1)
                        
                        is_match = (pos_token0 == filter_token0 and pos_token1 == filter_token1) or \
                                   (pos_token0 == filter_token1 and pos_token1 == filter_token0)
                        
                        if not is_match:
                            continue
                    
                    # 獲取完整位置詳情
                    details = await self.fetch_v3_lp_details(token_id)
                    if details.get("success"):
                        v3_positions.append(details)
            
            return v3_positions
            
        except Exception as e:
            logger.error(f"Error finding V3 positions for wallet: {e}")
            return []

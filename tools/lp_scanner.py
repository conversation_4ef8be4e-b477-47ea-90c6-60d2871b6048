#!/usr/bin/env python3
"""
Optimized Pancake LP Analyzer
单一脚本，集成BSC数据获取和LP收益分析
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from decimal import Decimal, getcontext
import aiohttp
from web3 import Web3
import time

# 设置高精度
getcontext().prec = 50

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TokenInfo:
    address: str
    symbol: str
    name: str
    decimals: int
    price_usd: Decimal

@dataclass
class LPPosition:
    pool_address: str
    token0: TokenInfo
    token1: TokenInfo
    balance: Decimal
    total_supply: Decimal
    pool_token0_balance: Decimal
    pool_token1_balance: Decimal
    pool_version: str
    fee_tier: Optional[str] = None

    @property
    def share_percentage(self) -> Decimal:
        if self.total_supply == 0:
            return Decimal('0')
        return (self.balance / self.total_supply) * 100

    @property
    def token0_amount(self) -> Decimal:
        if self.total_supply == 0:
            return Decimal('0')
        return (self.balance / self.total_supply) * self.pool_token0_balance

    @property
    def token1_amount(self) -> Decimal:
        if self.total_supply == 0:
            return Decimal('0')
        return (self.balance / self.total_supply) * self.pool_token1_balance

    @property
    def total_value_usd(self) -> Decimal:
        token0_value = self.token0_amount * self.token0.price_usd
        token1_value = self.token1_amount * self.token1.price_usd
        return token0_value + token1_value

class OptimizedPancakeAnalyzer:
    def __init__(self):
        self.bsc_rpc = "https://bsc-dataseed.binance.org/"
        self.w3 = Web3(Web3.HTTPProvider(self.bsc_rpc))
        
        # PancakeSwap 合约地址
        self.pancake_factory_v2 = "0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73"
        self.pancake_factory_v3 = "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865"
        
        # 常用 ERC20 ABI (简化版)
        self.erc20_abi = [
            {"constant": True, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint8"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "totalSupply", "outputs": [{"name": "", "type": "uint256"}], "type": "function"},
            {"constant": True, "inputs": [{"name": "_owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "balance", "type": "uint256"}], "type": "function"},
        ]
        
        # PancakeSwap V2 LP Token ABI
        self.lp_v2_abi = [
            {"constant": True, "inputs": [], "name": "token0", "outputs": [{"name": "", "type": "address"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "token1", "outputs": [{"name": "", "type": "address"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "getReserves", "outputs": [{"name": "_reserve0", "type": "uint112"}, {"name": "_reserve1", "type": "uint112"}, {"name": "_blockTimestampLast", "type": "uint32"}], "type": "function"},
        ] + self.erc20_abi
        
        # 价格缓存
        self.price_cache = {}
        self.cache_timestamp = 0
        self.cache_ttl = 300  # 5分钟缓存

    async def get_token_prices(self, token_addresses: List[str]) -> Dict[str, Decimal]:
        """从 CoinGecko 获取代币价格"""
        current_time = time.time()
        
        # 检查缓存
        if current_time - self.cache_timestamp < self.cache_ttl:
            cached_prices = {}
            for addr in token_addresses:
                if addr.lower() in self.price_cache:
                    cached_prices[addr] = self.price_cache[addr.lower()]
            if len(cached_prices) == len(token_addresses):
                return cached_prices

        try:
            # BSC 代币地址格式化
            formatted_addresses = [f"binance-smart-chain:{addr}" for addr in token_addresses]
            address_string = ",".join(formatted_addresses)
            
            url = f"https://api.coingecko.com/api/v3/simple/token_price/binance-smart-chain"
            params = {
                "contract_addresses": ",".join(token_addresses),
                "vs_currencies": "usd"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        prices = {}
                        for addr in token_addresses:
                            addr_lower = addr.lower()
                            if addr_lower in data and 'usd' in data[addr_lower]:
                                price = Decimal(str(data[addr_lower]['usd']))
                                prices[addr] = price
                                self.price_cache[addr_lower] = price
                            else:
                                logger.warning(f"Price not found for token: {addr}")
                                prices[addr] = Decimal('0')
                        
                        self.cache_timestamp = current_time
                        return prices
                    else:
                        logger.error(f"CoinGecko API error: {response.status}")
                        
        except Exception as e:
            logger.error(f"Error fetching prices: {e}")
        
        # 返回零价格作为后备
        return {addr: Decimal('0') for addr in token_addresses}

    def get_token_info(self, token_address: str, price_usd: Decimal = Decimal('0')) -> TokenInfo:
        """获取代币信息"""
        try:
            contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(token_address),
                abi=self.erc20_abi
            )
            
            name = contract.functions.name().call()
            symbol = contract.functions.symbol().call()
            decimals = contract.functions.decimals().call()
            
            return TokenInfo(
                address=token_address,
                symbol=symbol,
                name=name,
                decimals=decimals,
                price_usd=price_usd
            )
        except Exception as e:
            logger.error(f"Error getting token info for {token_address}: {e}")
            return TokenInfo(
                address=token_address,
                symbol="UNKNOWN",
                name="Unknown Token",
                decimals=18,
                price_usd=price_usd
            )

    def is_pancakeswap_lp_token(self, token_address: str) -> Tuple[bool, str]:
        """检查是否为 PancakeSwap LP Token"""
        try:
            contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(token_address),
                abi=self.lp_v2_abi
            )
            
            # 尝试调用 LP Token 特有的方法
            try:
                token0 = contract.functions.token0().call()
                token1 = contract.functions.token1().call()
                reserves = contract.functions.getReserves().call()
                return True, "V2"
            except:
                # 可能是 V3 或其他类型
                pass
                
            # TODO: 添加 V3 检测逻辑
            return False, ""
            
        except Exception as e:
            return False, ""

    async def get_lp_position(self, wallet_address: str, lp_token_address: str) -> Optional[LPPosition]:
        """获取 LP 持仓详情"""
        try:
            # 检查是否为 LP Token
            is_lp, version = self.is_pancakeswap_lp_token(lp_token_address)
            if not is_lp:
                return None

            lp_contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(lp_token_address),
                abi=self.lp_v2_abi
            )
            
            # 获取基础信息
            user_balance = lp_contract.functions.balanceOf(
                self.w3.to_checksum_address(wallet_address)
            ).call()
            
            if user_balance == 0:
                return None
                
            total_supply = lp_contract.functions.totalSupply().call()
            token0_address = lp_contract.functions.token0().call()
            token1_address = lp_contract.functions.token1().call()
            reserves = lp_contract.functions.getReserves().call()
            
            # 获取代币价格
            prices = await self.get_token_prices([token0_address, token1_address])
            
            # 获取代币信息
            token0_info = self.get_token_info(token0_address, prices.get(token0_address, Decimal('0')))
            token1_info = self.get_token_info(token1_address, prices.get(token1_address, Decimal('0')))
            
            # 转换为实际数量
            user_balance_decimal = Decimal(user_balance) / Decimal(10 ** 18)
            total_supply_decimal = Decimal(total_supply) / Decimal(10 ** 18)
            token0_reserve = Decimal(reserves[0]) / Decimal(10 ** token0_info.decimals)
            token1_reserve = Decimal(reserves[1]) / Decimal(10 ** token1_info.decimals)
            
            return LPPosition(
                pool_address=lp_token_address,
                token0=token0_info,
                token1=token1_info,
                balance=user_balance_decimal,
                total_supply=total_supply_decimal,
                pool_token0_balance=token0_reserve,
                pool_token1_balance=token1_reserve,
                pool_version=version
            )
            
        except Exception as e:
            logger.error(f"Error getting LP position for {lp_token_address}: {e}")
            return None

    async def scan_wallet_lp_positions(self, wallet_address: str) -> List[LPPosition]:
        """扫描钱包中的所有 LP 持仓"""
        positions = []
        
        try:
            # 这里需要实现代币余额扫描逻辑
            # 由于BSC链上数据量大，这里简化为检查已知的LP token
            # 实际应用中可以使用 BSCScan API 或其他索引服务
            
            logger.info(f"开始扫描钱包 {wallet_address} 的 LP 持仓...")
            
            # 示例：检查一些常见的 PancakeSwap LP 对
            common_lp_tokens = [
                "******************************************",  # BNB-BUSD
                "******************************************",  # BNB-ETH
                "******************************************",  # BNB-USDT
                # 可以添加更多常见 LP 对
            ]
            
            for lp_token in common_lp_tokens:
                try:
                    position = await self.get_lp_position(wallet_address, lp_token)
                    if position and position.balance > 0:
                        positions.append(position)
                        logger.info(f"发现 LP 持仓: {position.token0.symbol}-{position.token1.symbol}")
                except Exception as e:
                    logger.debug(f"检查 LP token {lp_token} 时出错: {e}")
                    
            return positions
            
        except Exception as e:
            logger.error(f"扫描钱包 LP 持仓时出错: {e}")
            return []

    def format_position_report(self, positions: List[LPPosition]) -> str:
        """格式化持仓报告"""
        if not positions:
            return "未发现任何 PancakeSwap LP 持仓"
        
        report = []
        report.append("=" * 80)
        report.append("PancakeSwap LP 持仓分析报告")
        report.append("=" * 80)
        
        total_value = Decimal('0')
        
        for i, pos in enumerate(positions, 1):
            report.append(f"\n📊 持仓 {i}: {pos.token0.symbol}-{pos.token1.symbol} ({pos.pool_version})")
            report.append(f"池子地址: {pos.pool_address}")
            report.append(f"LP Token 余额: {pos.balance:.6f}")
            report.append(f"持仓占比: {pos.share_percentage:.4f}%")
            report.append(f"")
            report.append(f"代币组成:")
            report.append(f"  {pos.token0.symbol}: {pos.token0_amount:.6f} (${pos.token0_amount * pos.token0.price_usd:.2f})")
            report.append(f"  {pos.token1.symbol}: {pos.token1_amount:.6f} (${pos.token1_amount * pos.token1.price_usd:.2f})")
            report.append(f"")
            report.append(f"总价值: ${pos.total_value_usd:.2f}")
            report.append("-" * 60)
            
            total_value += pos.total_value_usd
        
        report.append(f"\n💰 总计 LP 价值: ${total_value:.2f}")
        report.append("=" * 80)
        
        return "\n".join(report)

async def main():
    """主函数"""
    wallet_address = "******************************************"
    
    logger.info("启动 PancakeSwap LP 分析器...")
    
    analyzer = OptimizedPancakeAnalyzer()
    
    # 扫描 LP 持仓
    positions = await analyzer.scan_wallet_lp_positions(wallet_address)
    
    # 生成报告
    report = analyzer.format_position_report(positions)
    print(report)
    
    # 保存报告到文件
    with open(f"pancake_lp_report_{wallet_address[-8:]}.txt", "w", encoding="utf-8") as f:
        f.write(report)
    
    logger.info(f"报告已保存到 pancake_lp_report_{wallet_address[-8:]}.txt")

if __name__ == "__main__":
    asyncio.run(main())

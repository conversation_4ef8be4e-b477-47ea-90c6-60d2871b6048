#!/usr/bin/env python3
"""
Enhanced Wallet Scanner with BSCScan API
使用 BSCScan API 获取完整的代币持仓信息
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from decimal import Decimal, getcontext
import aiohttp
from web3 import Web3
import time

# 设置高精度
getcontext().prec = 50

# 日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TokenBalance:
    contract_address: str
    name: str
    symbol: str
    balance: str
    decimals: str
    type: str
    
@dataclass
class DetailedTokenInfo:
    address: str
    symbol: str
    name: str
    decimals: int
    balance: Decimal
    price_usd: Decimal
    value_usd: Decimal
    is_lp_token: bool = False
    lp_details: Optional[dict] = None

class EnhancedWalletScanner:
    def __init__(self):
        self.bsc_rpc = "https://bsc-dataseed.binance.org/"
        self.w3 = Web3(Web3.HTTPProvider(self.bsc_rpc))
        self.bscscan_api = "https://api.bscscan.com/api"
        
        # 常用 ABI
        self.erc20_abi = [
            {"constant": True, "inputs": [], "name": "name", "outputs": [{"name": "", "type": "string"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "symbol", "outputs": [{"name": "", "type": "string"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "decimals", "outputs": [{"name": "", "type": "uint8"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "totalSupply", "outputs": [{"name": "", "type": "uint256"}], "type": "function"},
            {"constant": True, "inputs": [{"name": "_owner", "type": "address"}], "name": "balanceOf", "outputs": [{"name": "balance", "type": "uint256"}], "type": "function"},
        ]
        
        self.lp_v2_abi = [
            {"constant": True, "inputs": [], "name": "token0", "outputs": [{"name": "", "type": "address"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "token1", "outputs": [{"name": "", "type": "address"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "getReserves", "outputs": [{"name": "_reserve0", "type": "uint112"}, {"name": "_reserve1", "type": "uint112"}, {"name": "_blockTimestampLast", "type": "uint32"}], "type": "function"},
            {"constant": True, "inputs": [], "name": "factory", "outputs": [{"name": "", "type": "address"}], "type": "function"},
        ] + self.erc20_abi
        
        # PancakeSwap 工厂地址
        self.pancake_factories = [
            "******************************************",  # V2
            "******************************************",  # V3
        ]

    async def get_wallet_token_balances(self, wallet_address: str) -> List[TokenBalance]:
        """使用 BSCScan API 获取钱包所有代币余额"""
        try:
            url = self.bscscan_api
            params = {
                'module': 'account',
                'action': 'tokentx',
                'address': wallet_address,
                'page': 1,
                'offset': 10000,
                'sort': 'desc'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data['status'] == '1':
                            # 提取所有涉及的代币合约
                            token_contracts = set()
                            for tx in data['result']:
                                token_contracts.add(tx['contractAddress'])
                            
                            # 获取每个代币的当前余额
                            token_balances = []
                            for contract in token_contracts:
                                balance_info = await self.get_token_balance_info(wallet_address, contract)
                                if balance_info:
                                    token_balances.append(balance_info)
                            
                            return token_balances
                        
        except Exception as e:
            logger.error(f"Error fetching token balances: {e}")
        
        return []

    async def get_token_balance_info(self, wallet_address: str, contract_address: str) -> Optional[TokenBalance]:
        """获取特定代币的余额信息"""
        try:
            # 获取余额
            url = self.bscscan_api
            params = {
                'module': 'account',
                'action': 'tokenbalance',
                'contractaddress': contract_address,
                'address': wallet_address,
                'tag': 'latest'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        if data['status'] == '1' and int(data['result']) > 0:
                            # 获取代币信息
                            token_info = await self.get_token_info_from_contract(contract_address)
                            if token_info:
                                return TokenBalance(
                                    contract_address=contract_address,
                                    name=token_info['name'],
                                    symbol=token_info['symbol'],
                                    balance=data['result'],
                                    decimals=str(token_info['decimals']),
                                    type='ERC-20'
                                )
        except Exception as e:
            logger.debug(f"Error getting balance for {contract_address}: {e}")
        
        return None

    async def get_token_info_from_contract(self, contract_address: str) -> Optional[dict]:
        """从合约获取代币信息"""
        try:
            contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(contract_address),
                abi=self.erc20_abi
            )
            
            name = contract.functions.name().call()
            symbol = contract.functions.symbol().call()
            decimals = contract.functions.decimals().call()
            
            return {
                'name': name,
                'symbol': symbol,
                'decimals': decimals
            }
        except Exception as e:
            logger.debug(f"Error getting token info for {contract_address}: {e}")
            return None

    def is_pancakeswap_lp_token(self, contract_address: str) -> Tuple[bool, Optional[dict]]:
        """检查是否为 PancakeSwap LP Token"""
        try:
            contract = self.w3.eth.contract(
                address=self.w3.to_checksum_address(contract_address),
                abi=self.lp_v2_abi
            )
            
            # 检查是否有 LP Token 特有的方法
            try:
                token0_address = contract.functions.token0().call()
                token1_address = contract.functions.token1().call()
                reserves = contract.functions.getReserves().call()
                
                # 检查工厂地址
                try:
                    factory = contract.functions.factory().call()
                    if factory.lower() in [f.lower() for f in self.pancake_factories]:
                        return True, {
                            'token0': token0_address,
                            'token1': token1_address,
                            'reserves': reserves,
                            'factory': factory,
                            'version': 'V2'
                        }
                except:
                    # 如果没有 factory 方法，可能是其他类型的 LP
                    pass
                    
                return True, {
                    'token0': token0_address,
                    'token1': token1_address,
                    'reserves': reserves,
                    'factory': 'Unknown',
                    'version': 'Unknown'
                }
                
            except Exception as e:
                logger.debug(f"Not an LP token: {contract_address}")
                return False, None
                
        except Exception as e:
            return False, None

    async def get_token_prices(self, token_addresses: List[str]) -> Dict[str, Decimal]:
        """获取代币价格"""
        try:
            url = "https://api.coingecko.com/api/v3/simple/token_price/binance-smart-chain"
            params = {
                "contract_addresses": ",".join(token_addresses),
                "vs_currencies": "usd"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        prices = {}
                        for addr in token_addresses:
                            addr_lower = addr.lower()
                            if addr_lower in data and 'usd' in data[addr_lower]:
                                prices[addr] = Decimal(str(data[addr_lower]['usd']))
                            else:
                                prices[addr] = Decimal('0')
                        
                        return prices
                        
        except Exception as e:
            logger.error(f"Error fetching prices: {e}")
        
        return {addr: Decimal('0') for addr in token_addresses}

    async def analyze_wallet(self, wallet_address: str) -> List[DetailedTokenInfo]:
        """分析钱包中的所有代币"""
        logger.info(f"开始分析钱包: {wallet_address}")
        
        # 获取所有代币余额
        token_balances = await self.get_wallet_token_balances(wallet_address)
        
        if not token_balances:
            logger.warning("未获取到代币余额数据")
            return []
        
        logger.info(f"发现 {len(token_balances)} 个代币持仓")
        
        # 获取价格
        token_addresses = [tb.contract_address for tb in token_balances]
        prices = await self.get_token_prices(token_addresses)
        
        detailed_tokens = []
        lp_tokens_found = 0
        
        for token_balance in token_balances:
            try:
                # 计算实际余额
                balance = Decimal(token_balance.balance) / Decimal(10 ** int(token_balance.decimals))
                
                if balance <= 0:
                    continue
                
                price = prices.get(token_balance.contract_address, Decimal('0'))
                value_usd = balance * price
                
                # 检查是否为 LP Token
                is_lp, lp_details = self.is_pancakeswap_lp_token(token_balance.contract_address)
                
                if is_lp:
                    lp_tokens_found += 1
                    logger.info(f"发现 LP Token: {token_balance.symbol}")
                
                detailed_token = DetailedTokenInfo(
                    address=token_balance.contract_address,
                    symbol=token_balance.symbol,
                    name=token_balance.name,
                    decimals=int(token_balance.decimals),
                    balance=balance,
                    price_usd=price,
                    value_usd=value_usd,
                    is_lp_token=is_lp,
                    lp_details=lp_details
                )
                
                detailed_tokens.append(detailed_token)
                
            except Exception as e:
                logger.error(f"Error processing token {token_balance.symbol}: {e}")
        
        logger.info(f"分析完成，发现 {lp_tokens_found} 个 LP Token")
        return detailed_tokens

    def format_wallet_report(self, tokens: List[DetailedTokenInfo]) -> str:
        """格式化钱包分析报告"""
        if not tokens:
            return "未发现任何代币持仓"
        
        report = []
        report.append("=" * 100)
        report.append("BSC 钱包持仓分析报告")
        report.append("=" * 100)
        
        # 分类显示
        lp_tokens = [t for t in tokens if t.is_lp_token]
        regular_tokens = [t for t in tokens if not t.is_lp_token]
        
        total_value = sum(t.value_usd for t in tokens)
        
        if lp_tokens:
            report.append(f"\n🏊‍♀️ LP 流动性持仓 ({len(lp_tokens)} 个):")
            report.append("-" * 80)
            
            lp_total_value = Decimal('0')
            for token in sorted(lp_tokens, key=lambda x: x.value_usd, reverse=True):
                if token.lp_details:
                    version = token.lp_details.get('version', 'Unknown')
                    factory = token.lp_details.get('factory', 'Unknown')
                else:
                    version = 'Unknown'
                    factory = 'Unknown'
                
                report.append(f"📊 {token.symbol} ({version})")
                report.append(f"   合约: {token.address}")
                report.append(f"   余额: {token.balance:.6f}")
                report.append(f"   价值: ${token.value_usd:.2f}")
                if factory != 'Unknown':
                    report.append(f"   工厂: {factory[:20]}...")
                report.append("")
                
                lp_total_value += token.value_usd
            
            report.append(f"LP 总价值: ${lp_total_value:.2f}")
            report.append("")
        
        if regular_tokens:
            report.append(f"\n💰 常规代币持仓 ({len(regular_tokens)} 个):")
            report.append("-" * 80)
            
            # 只显示有价值的代币
            valuable_tokens = [t for t in regular_tokens if t.value_usd > 0.01]
            
            for token in sorted(valuable_tokens, key=lambda x: x.value_usd, reverse=True):
                report.append(f"🪙 {token.symbol} ({token.name})")
                report.append(f"   合约: {token.address}")
                report.append(f"   余额: {token.balance:.6f}")
                report.append(f"   价格: ${token.price_usd:.6f}")
                report.append(f"   价值: ${token.value_usd:.2f}")
                report.append("")
        
        report.append("=" * 100)
        report.append(f"💎 总计钱包价值: ${total_value:.2f}")
        report.append(f"📊 LP Token 数量: {len(lp_tokens)}")
        report.append(f"🪙 常规代币数量: {len(regular_tokens)}")
        report.append("=" * 100)
        
        return "\n".join(report)

async def main():
    """主函数"""
    wallet_address = "******************************************"
    
    logger.info("启动增强型钱包扫描器...")
    
    scanner = EnhancedWalletScanner()
    
    # 分析钱包
    tokens = await scanner.analyze_wallet(wallet_address)
    
    # 生成报告
    report = scanner.format_wallet_report(tokens)
    print(report)
    
    # 保存报告
    filename = f"enhanced_wallet_report_{wallet_address[-8:]}.txt"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(report)
    
    logger.info(f"报告已保存到 {filename}")

if __name__ == "__main__":
    asyncio.run(main())
